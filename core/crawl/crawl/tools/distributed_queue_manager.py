#!/usr/bin/env python3
"""
分布式队列管理器
解决多机器环境下的队列填充协调问题
"""

import time
import json
import uuid
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from sqlalchemy.sql.elements import True_

# 导入 Scrapy settings
try:
    from scrapy.utils.project import get_project_settings
    settings = get_project_settings()
except ImportError:
    settings = None

from .stocks import (
    get_redis_connection,
    populate_eastmoney_min_queue,
    populate_stock_detail_queue,
    get_queue_status,
    eastmoney_min_queue,
    stock_detail_queue
)

logger = logging.getLogger(__name__)


class DistributedQueueManager:
    """分布式队列管理器"""
    
    def __init__(self, machine_id: str = None):
        self.redis_conn = get_redis_connection()
        self.machine_id = machine_id or self._generate_machine_id()
        
        # Redis 键名
        self.lock_key_prefix = "queue_lock:"
        self.last_fill_key_prefix = "last_fill:"
        self.machine_info_key = "queue_machines"
        
        # 配置
        self.lock_timeout = 300  # 锁超时时间（5分钟）
        self.queue_refresh_interval = 3600  # 队列刷新间隔（1小时）
        self.min_queue_threshold = 50  # 最小队列阈值
        
        logger.info(f"分布式队列管理器初始化，机器ID: {self.machine_id}")
    
    def _generate_machine_id(self) -> str:
        """生成机器ID"""
        import socket
        hostname = socket.gethostname()
        return f"{hostname}-{uuid.uuid4().hex[:8]}"
    
    def _acquire_lock(self, queue_name: str) -> bool:
        """获取分布式锁"""
        lock_key = f"{self.lock_key_prefix}{queue_name}"
        lock_value = f"{self.machine_id}-{int(time.time())}"
        
        # 尝试获取锁
        if self.redis_conn.set(lock_key, lock_value, nx=True, ex=self.lock_timeout):
            logger.info(f"成功获取队列 {queue_name} 的填充锁")
            return True
        
        # 检查锁是否过期
        current_lock = self.redis_conn.get(lock_key)
        if current_lock:
            try:
                lock_info = current_lock.decode('utf-8')
                lock_time = int(lock_info.split('-')[-1])
                if time.time() - lock_time > self.lock_timeout:
                    # 锁已过期，强制获取
                    if self.redis_conn.set(lock_key, lock_value, ex=self.lock_timeout):
                        logger.warning(f"强制获取过期锁: {queue_name}")
                        return True
            except Exception as e:
                logger.error(f"检查锁状态失败: {e}")
        
        return False
    
    def _release_lock(self, queue_name: str):
        """释放分布式锁"""
        lock_key = f"{self.lock_key_prefix}{queue_name}"
        self.redis_conn.delete(lock_key)
        logger.info(f"释放队列 {queue_name} 的填充锁")
    
    def _should_refresh_queue(self, queue_name: str) -> bool:
        """判断是否需要刷新队列"""
        # 检查队列长度
        if queue_name == "eastmoney-min":
            current_length = self.redis_conn.scard(eastmoney_min_queue)
        elif queue_name == "stock-detail":
            current_length = self.redis_conn.scard(stock_detail_queue)

        else:
            return False
        
        # 如果队列长度不足，需要刷新
        if current_length < self.min_queue_threshold:
            logger.info(f"队列 {queue_name} 长度不足 ({current_length} < {self.min_queue_threshold})")
            return True
        
        # 检查上次填充时间
        last_fill_key = f"{self.last_fill_key_prefix}{queue_name}"
        last_fill_time = self.redis_conn.get(last_fill_key)
        
        if not last_fill_time:
            logger.info(f"队列 {queue_name} 从未填充过")
            return True
        
        try:
            last_time = float(last_fill_time.decode('utf-8'))
            time_since_last = time.time() - last_time
            
            if time_since_last > self.queue_refresh_interval:
                logger.info(f"队列 {queue_name} 上次填充时间过久 ({time_since_last/3600:.1f}小时前)")
                return True
        except Exception as e:
            logger.error(f"解析上次填充时间失败: {e}")
            return True
        
        return False
    
    def _update_last_fill_time(self, queue_name: str):
        """更新最后填充时间"""
        last_fill_key = f"{self.last_fill_key_prefix}{queue_name}"
        self.redis_conn.set(last_fill_key, str(time.time()))
    
    def _register_machine(self):
        """注册机器信息"""
        machine_info = {
            "machine_id": self.machine_id,
            "last_seen": time.time(),
            "status": "active"
        }
        self.redis_conn.hset(self.machine_info_key, self.machine_id, json.dumps(machine_info))
    
    def ensure_queue_available(self, queue_name: str, force_refresh: bool = False) -> bool:
        """确保队列可用（核心方法）
        
        Args:
            queue_name: 队列名称 ("eastmoney-min")
            force_refresh: 是否强制刷新
            
        Returns:
            bool: 队列是否可用
        """
        try:
            # 注册机器
            self._register_machine()
            
            # 检查是否需要刷新队列
            if not force_refresh and not self._should_refresh_queue(queue_name):
                logger.info(f"队列 {queue_name} 无需刷新")
                return True
            
            # 尝试获取锁
            if not self._acquire_lock(queue_name):
                # 获取锁失败，等待其他机器填充
                logger.info(f"未能获取锁，等待其他机器填充队列 {queue_name}")
                return self._wait_for_queue_fill(queue_name)
            
            try:
                # 获取锁成功，执行队列填充
                logger.info(f"开始填充队列 {queue_name}")
                
                if queue_name == "eastmoney-min":
                    success = populate_eastmoney_min_queue(force_full=True)
                elif queue_name == "stock-detail":
                    # 根据频率设置优先级
                    # 1: 高频率(热门文章), 2: 中频率(盈利数据、主营业务), 3: 低频率(机构持股)
                    # 这里设置为2，即包含高和中频率数据
                    success = populate_stock_detail_queue(force_full=True)

                else:
                    logger.error(f"未知队列类型: {queue_name}")
                    return False
                
                if success:
                    self._update_last_fill_time(queue_name)
                    logger.info(f"队列 {queue_name} 填充成功")
                    return True
                else:
                    logger.error(f"队列 {queue_name} 填充失败")
                    return False
                    
            finally:
                # 释放锁
                self._release_lock(queue_name)
                
        except Exception as e:
            logger.error(f"确保队列可用时发生错误: {e}")
            return False
    
    def _wait_for_queue_fill(self, queue_name: str, max_wait: int = 60) -> bool:
        """等待其他机器填充队列"""
        logger.info(f"等待其他机器填充队列 {queue_name}，最多等待 {max_wait} 秒")
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            # 检查队列长度
            if queue_name == "eastmoney-min":
                current_length = self.redis_conn.scard(eastmoney_min_queue)
            elif queue_name == "stock-detail":
                current_length = self.redis_conn.scard(stock_detail_queue)
            else:
                return False
            
            if current_length >= self.min_queue_threshold:
                logger.info(f"队列 {queue_name} 已被其他机器填充，长度: {current_length}")
                return True
            
            time.sleep(5)  # 等待5秒后重新检查
        
        logger.warning(f"等待超时，队列 {queue_name} 仍未被填充")
        return False
    
    def get_queue_info(self) -> Dict[str, Any]:
        """获取队列信息"""
        try:
            status = get_queue_status()
            
            # 获取锁信息
            locks = {}
            for queue_name in ["eastmoney-min", "stock-detail"]:
                lock_key = f"{self.lock_key_prefix}{queue_name}"
                lock_info = self.redis_conn.get(lock_key)
                if lock_info:
                    locks[queue_name] = lock_info.decode('utf-8')
                else:
                    locks[queue_name] = None

            # 获取最后填充时间
            last_fills = {}
            for queue_name in ["eastmoney-min", "stock-detail"]:
                last_fill_key = f"{self.last_fill_key_prefix}{queue_name}"
                last_fill = self.redis_conn.get(last_fill_key)
                if last_fill:
                    last_fills[queue_name] = datetime.fromtimestamp(
                        float(last_fill.decode('utf-8'))
                    ).strftime('%Y-%m-%d %H:%M:%S')
                else:
                    last_fills[queue_name] = "从未填充"
            
            # 获取机器信息
            machines = {}
            machine_data = self.redis_conn.hgetall(self.machine_info_key)
            for machine_id, info in machine_data.items():
                try:
                    machines[machine_id.decode('utf-8')] = json.loads(info.decode('utf-8'))
                except:
                    pass
            
            return {
                "machine_id": self.machine_id,
                "queue_status": status,
                "locks": locks,
                "last_fills": last_fills,
                "machines": machines,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"获取队列信息失败: {e}")
            return {}


# 全局实例
_queue_manager = None

def get_queue_manager(machine_id: str = None) -> DistributedQueueManager:
    """获取队列管理器实例"""
    global _queue_manager
    if _queue_manager is None:
        _queue_manager = DistributedQueueManager(machine_id)
    return _queue_manager
