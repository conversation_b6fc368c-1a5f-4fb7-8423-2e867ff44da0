# -*- coding: UTF-8 -*-

import redis
import json
import time
from crawl.db.db import DBSession, Stock, Source
from crawl.tools.helper import stock_code, stock_code2, ago_day_timestamp, stock_code_with_market, get_reporting_period
import os
import logging
from datetime import datetime

# 导入 Scrapy settings
try:
    from scrapy.utils.project import get_project_settings
    settings = get_project_settings()
except ImportError:
    # 如果不在 Scrapy 环境中，使用环境变量作为后备
    settings = None

# 设置日志
logger = logging.getLogger(__name__)

# 任务队列名称
xueqiu_detail_queue = 'xueqiu-detail:start_urls'  # 雪球详情分布式队列
eastmoney_min_queue = 'eastmoney-min:start_urls'  # 东方财富分时分布式队列
stock_detail_queue = 'stock-detail:start_urls'    # 股票详情分布式队列（多数据源）

# 失败记录队列
failed_queue = 'xueqiu:failed_urls'

# 配置参数
batch_size = 200
min_queue_size = 50  # 队列最小长度，低于此值时补充数据
max_failed_count = 3  # 最大失败次数

# 优先级配置 - 基于时间频率
PRIORITY_CONFIG = {
    'daily': {
        'name': '每日更新',
        'priority': 1,
        'check_interval': 1,  # 1天
        'description': '需要每日更新的高频数据'
    },
    'weekly': {
        'name': '每周更新', 
        'priority': 2,
        'check_interval': 7,  # 7天
        'description': '需要每周更新的中频数据'
    },
    'monthly': {
        'name': '每月更新',
        'priority': 3, 
        'check_interval': 30,  # 30天
        'description': '需要每月更新的低频数据'
    },
    'quarterly': {
        'name': '每季度更新',
        'priority': 4,
        'check_interval': 90,  # 90天
        'description': '需要每季度更新的低频数据'
    }
}

def should_update_data(data_type, last_update_time=None):
    """
    判断某个数据类型是否需要更新
    
    Args:
        data_type (str): 数据类型 (daily, weekly, monthly, quarterly)
        last_update_time (int, optional): 上次更新时间戳，如果为None则强制更新
    
    Returns:
        bool: 是否需要更新
    """
    if data_type not in PRIORITY_CONFIG:
        return True  # 未知类型默认需要更新
    
    if last_update_time is None:
        return True  # 没有上次更新时间，需要更新
    
    config = PRIORITY_CONFIG[data_type]
    check_interval = config['check_interval']
    
    # 计算距离上次更新的天数
    days_since_update = (datetime.now() - datetime.fromtimestamp(last_update_time)).days
    
    return days_since_update >= check_interval

def get_priority_by_frequency(frequency):
    """
    根据频率获取优先级数值
    
    Args:
        frequency (str): 频率类型 (daily, weekly, monthly, quarterly)
    
    Returns:
        int: 优先级数值
    """
    return PRIORITY_CONFIG.get(frequency, {}).get('priority', 999)

def get_frequency_by_priority(priority):
    """
    根据优先级数值获取频率类型
    
    Args:
        priority (int): 优先级数值
    
    Returns:
        str: 频率类型
    """
    for freq, config in PRIORITY_CONFIG.items():
        if config['priority'] == priority:
            return freq
    return 'unknown'

def filter_urls_by_frequency(urls_data, frequency_filter=None):
    """
    根据频率过滤URL数据
    
    Args:
        urls_data (list): URL数据列表
        frequency_filter (str, optional): 频率过滤，只添加该频率及更高频率的URL
    
    Returns:
        list: 过滤后的URL数据列表
    """
    if frequency_filter is None:
        return urls_data
    
    if frequency_filter not in PRIORITY_CONFIG:
        return urls_data
    
    filter_priority = PRIORITY_CONFIG[frequency_filter]['priority']
    
    filtered_urls = []
    for url_data in urls_data:
        if 'frequency' in url_data and url_data['frequency'] in PRIORITY_CONFIG:
            url_priority = PRIORITY_CONFIG[url_data['frequency']]['priority']
            if url_priority <= filter_priority:
                filtered_urls.append(url_data)
        else:
            # 如果没有频率信息，默认添加
            filtered_urls.append(url_data)
    
    return filtered_urls

# 初始化变量
r = None
data_list = []
queue_length = 0
actual_batch_size = 0

# 连接Redis（优先使用 Scrapy settings，后备使用环境变量）
try:
    if settings:
        # 使用 Scrapy settings 中的配置
        redis_host = settings.get('REDIS_HOST', 'localhost')
        redis_port = settings.get('REDIS_PORT', 6379)
        redis_db = settings.get('REDIS_DB', 8)
        redis_password = settings.get('REDIS_PASSWORD')
    else:
        # 后备：使用环境变量
        redis_host = os.getenv('BB_REDIS_HOST', 'localhost')
        redis_port = int(os.getenv('BB_REDIS_PORT', 6379)) if os.getenv('BB_REDIS_PORT') else 6379
        redis_db = int(os.getenv('BB_REDIS_CRAWL_DB', 8))
        redis_password = os.getenv('BB_REDIS_PASSWORD')

    r = redis.Redis(host=redis_host, port=redis_port, db=redis_db, password=redis_password)

    # 测试连接
    r.ping()
    logger.info("Redis connection established successfully")

    # 注意：旧版本代码已移除，现在使用新的分布式队列系统
    data_list = []

except Exception as e:
    logger.warning(f"Redis connection failed: {e}. Redis functionality will be disabled.")
    r = None
    data_list = []
    queue_length = 0
    actual_batch_size = 0


# ============================================================================
# 雪球详情分布式队列管理
# ============================================================================


# ============================================================================
# 新版分布式队列管理函数
# ============================================================================

def get_redis_connection():
    """获取Redis连接"""
    global r
    if r is None:
        try:
            if settings:
                # 使用 Scrapy settings 中的配置
                redis_host = settings.get('REDIS_HOST', 'localhost')
                redis_port = settings.get('REDIS_PORT', 6379)
                redis_db = settings.get('REDIS_DB', 8)
                redis_password = settings.get('REDIS_PASSWORD')
            else:
                # 后备：使用环境变量
                redis_host = os.getenv('BB_REDIS_HOST', 'localhost')
                redis_port = int(os.getenv('BB_REDIS_PORT', 6379))
                redis_db = int(os.getenv('BB_REDIS_CRAWL_DB', 8))
                redis_password = os.getenv('BB_REDIS_PASSWORD')

            r = redis.Redis(host=redis_host, port=redis_port, db=redis_db, password=redis_password)
            r.ping()
            logger.info("Redis connection re-established")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            return None
    return r


def populate_xueqiu_queue(force_full=True):
    """从数据库获取股票数据并填充到雪球详情队列

    Args:
        force_full (bool): 是否强制全量填充，默认True
    """
    redis_conn = get_redis_connection()
    if not redis_conn:
        logger.error("Redis connection not available")
        return False

    try:
        # 检查当前队列长度（使用SET类型）
        current_length = redis_conn.scard(xueqiu_detail_queue)
        logger.info(f"当前雪球详情队列长度: {current_length}")

        # 全量填充模式：不再删除现有队列，直接进行追加
        if force_full:
            logger.info("执行全量填充模式，保留现有队列，直接追加数据")
        elif current_length >= min_queue_size:
            logger.info(f"队列长度足够 ({current_length} >= {min_queue_size})，无需补充")
            return True

        # 从数据库获取股票数据
        session = DBSession()
        try:
            # 全量填充：获取所有有效股票
            if force_full:
                stocks = session.query(Stock).filter(Stock.code.isnot(None)).yield_per(1000)
            else:
                # 增量填充：获取评分大于0的股票，按评分降序排列
                stocks = session.query(Stock).filter(Stock.score > 0).order_by(Stock.score.desc()).limit(batch_size * 2)

            added_count = 0
            for stock in stocks:
                if stock.code:
                    # 构建雪球API URL
                    url = f'https://api.xueqiu.com/query/v1/symbol/search/status.json?count=20&comment=0&symbol={stock_code(stock.code)}&hl=0&source=all&sort=alpha&page=1&q=&type=11'

                    # 使用JSON格式推送到队列（符合scrapy-redis新版本要求）
                    url_data = {
                        "url": url,
                        "code": stock.code,
                        "data_type": "detail"
                    }
                    redis_conn.sadd(xueqiu_detail_queue, json.dumps(url_data, ensure_ascii=False))
                    added_count += 1

                    # 批量处理日志
                    if added_count % batch_size == 0:
                        logger.info(f"已处理 {added_count} 个股票")

                    # 增量模式：如果队列长度达到目标，停止添加
                    if not force_full and redis_conn.scard(xueqiu_detail_queue) >= batch_size:
                        break

            logger.info(f"成功添加 {added_count} 个任务到雪球详情队列")
            return True

        finally:
            session.close()

    except Exception as e:
        logger.error(f"填充雪球队列时出错: {e}")
        return False


def check_and_populate_queue(force_full=False):
    """检查队列长度，如果不足则补充数据

    Args:
        force_full (bool): 是否强制全量填充，默认False（保持增量模式）
    """
    redis_conn = get_redis_connection()
    if not redis_conn:
        return False

    try:
        current_length = redis_conn.scard(xueqiu_detail_queue)
        logger.info(f"检查雪球详情队列，当前长度: {current_length}")

        if force_full or current_length < min_queue_size:
            if force_full:
                logger.info("强制全量填充模式")
            else:
                logger.info(f"队列长度不足 ({current_length} < {min_queue_size})，开始补充数据")
            return populate_xueqiu_queue(force_full=force_full)
        else:
            logger.info("队列长度充足，无需补充")
            return True

    except Exception as e:
        logger.error(f"检查队列时出错: {e}")
        return False


def populate_eastmoney_min_queue(force_full=True):
    """从数据库获取股票数据并填充到东方财富分时队列

    Args:
        force_full (bool): 是否强制全量填充，默认True
    """
    redis_conn = get_redis_connection()
    if not redis_conn:
        logger.error("Redis connection not available")
        return False

    try:
        # 检查当前队列长度
        current_length = redis_conn.scard(eastmoney_min_queue)
        logger.info(f"当前东方财富分时队列长度: {current_length}")

        # 全量填充模式：不再删除现有队列，直接进行追加
        if force_full:
            logger.info("执行全量填充模式，保留现有队列，直接追加数据")
        elif current_length >= min_queue_size:
            logger.info(f"队列长度足够 ({current_length} >= {min_queue_size})，无需补充")
            return True

        # 获取数据库会话
        session = DBSession()

        try:
            # 全量填充：获取所有有效股票
            if force_full:
                stocks = session.query(Source).filter(Source.code.isnot(None)).yield_per(1000)
            else:
                # 增量填充：限制数量
                stocks = session.query(Source).filter(Source.code.isnot(None)).limit(batch_size * 2)

            count = 0
            for stock in stocks:
                if stock.code:
                    # 构建东方财富分时API URL
                    secid = stock_code2(stock.code)
                    timestamp = int(time.time() * 1000)
                    url = f'http://push2.eastmoney.com/api/qt/stock/trends2/get?fields1=f1%2Cf2%2Cf3%2Cf4%2Cf5%2Cf6%2Cf7%2Cf8%2Cf9%2Cf10%2Cf11%2Cf12%2Cf13&fields2=f51%2Cf52%2Cf53%2Cf54%2Cf55%2Cf56%2Cf57%2Cf58&ut=fa5fd1943c7b386f172d6893dbfba10b&ndays=1&iscr=0&iscca=0&_={timestamp}&secid={secid}'

                    # 使用JSON格式推送到队列（符合scrapy-redis新版本要求）
                    url_data = {
                        "url": url,
                        "code": stock.code,
                        "data_type": "min"
                    }
                    redis_conn.sadd(eastmoney_min_queue, json.dumps(url_data, ensure_ascii=False))
                    count += 1

                    # 批量处理日志
                    if count % batch_size == 0:
                        logger.info(f"已处理 {count} 个股票")

                    # 增量模式：如果队列长度达到目标，停止添加
                    if not force_full and redis_conn.scard(eastmoney_min_queue) >= batch_size:
                        break

            logger.info(f"成功填充东方财富分时队列，共 {count} 个URL")
            return True

        finally:
            session.close()

    except Exception as e:
        logger.error(f"Error populating eastmoney min queue: {e}")
        return False


def populate_stock_detail_queue(force_full=True, priority_filter=None):
    """从数据库获取股票数据并填充到股票详情队列（多数据源）

    Args:
        force_full (bool): 是否强制全量填充，默认True
        priority_filter (int, optional): 优先级过滤，只添加优先级小于等于该值的URL。
            1: 只添加高优先级
            2: 添加高和中优先级
            3: 添加所有优先级（默认）
    """
    redis_conn = get_redis_connection()
    if not redis_conn:
        logger.error("Redis connection not available")
        return False

    try:
        # 检查当前队列长度
        current_length = redis_conn.scard(stock_detail_queue)
        logger.info(f"当前股票详情队列长度: {current_length}")

        # 全量填充模式：不再删除现有队列，直接进行追加
        if force_full:
            logger.info("执行全量填充模式，保留现有队列，直接追加数据")
        elif current_length >= min_queue_size:
            logger.info(f"队列长度足够 ({current_length} >= {min_queue_size})，无需补充")
            return True

        # 获取数据库会话
        session = DBSession()

        try:
            # 全量填充：获取所有有效股票
            if force_full:
                stocks = session.query(Stock).filter(Stock.code.isnot(None)).yield_per(1000)
                logger.info("执行全量填充，获取所有有效股票")
            else:
                # 增量填充：限制数量
                stocks = session.query(Stock).filter(Stock.code.isnot(None)).limit(batch_size * 2)
                logger.info(f"执行增量填充，限制数量为 {batch_size * 2}")

            count = 0
            stock_processed = 0
            timestamp = str(ago_day_timestamp(0))
            skipped_count = 0

            # 统计各种情况的数量
            status_counts = {
                'total_stocks': 0,
                'has_code': 0,
                'added_urls': 0,
                'skipped_urls': 0,
                'skipped_due_to_frequency': 0,
                'skipped_due_to_priority': 0
            }

            for stock in stocks:
                status_counts['total_stocks'] += 1
                if status_counts['total_stocks'] % 100 == 0:
                    logger.info(f"已遍历 {status_counts['total_stocks']} 个股票")

                if stock.code:
                    status_counts['has_code'] += 1
                    stock_processed += 1
                    symbol = stock_code(stock.code)

                    # 为每个股票生成4种不同的数据源URL
                    urls_data = [
                        # 个股热门文章 - 高优先级 (1) - 每日更新
                        {
                            "url": f'https://xueqiu.com/query/v1/symbol/search/status.json?count=10&comment=0&symbol={symbol}&hl=0&source=all&sort=alpha&page=1&q=&type=11',
                            "code": stock.code,
                            "data_type": "hot",
                            "priority": 1,
                            "frequency": "daily"
                        },
                        # 雪球季度盈利数据 - 中优先级 (2) - 每月更新
                        {
                            "url": f'https://stock.xueqiu.com/v5/stock/finance/cn/income.json?symbol={symbol}&type=all&is_detail=true&count=9&timestamp={timestamp}',
                            "code": stock.code,
                            "data_type": "income",
                            "priority": 2,
                            "frequency": "monthly"
                        },
                        # 主营业务 - 中优先级 (2) - 每月更新
                        {
                            "url": f'https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_F10_FN_MAINOP&columns=SECUCODE%2CSECURITY_CODE%2CREPORT_DATE%2CMAINOP_TYPE%2CITEM_NAME%2CMAIN_BUSINESS_INCOME%2CMBI_RATIO%2CMAIN_BUSINESS_COST%2CMBC_RATIO%2CMAIN_BUSINESS_RPOFIT%2CMBR_RATIO%2CGROSS_RPOFIT_RATIO%2CRANK&quoteColumns=&filter=(SECUCODE%3D%22{stock_code_with_market(stock.code)}%22)(REPORT_DATE%3D%27{get_reporting_period()}%27)&pageNumber=1&pageSize=200&sortTypes=1%2C1&sortColumns=MAINOP_TYPE%2CRANK&source=HSF10&client=PC&v={int(time.time() * 1000)}',
                            "code": stock.code,
                            "data_type": "business",
                            "priority": 2,
                            "frequency": "monthly"
                        },
                        # 机构持股 - 低优先级 (3) - 每月更新
                        {
                            "url": f'https://data.eastmoney.com/dataapi/zlsj/detail?SHType=&SHCode=&SCode={stock.code}&ReportDate={get_reporting_period()}&sortField=HOLDER_CODE&sortDirec=1&pageNum=1&pageSize=1000',
                            "code": stock.code,
                            "data_type": "holder",
                            "priority": 3,
                            "frequency": "monthly"
                        }
                    ]

                    # 将每个URL数据推送到队列（根据优先级和频率过滤）
                    for url_data in urls_data:
                        # 检查是否符合优先级过滤条件
                        if priority_filter is None or url_data['priority'] <= priority_filter:
                            # 获取上次更新时间（从Redis中获取）
                            last_update_key = f"last_update:{stock.code}:{url_data['data_type']}"
                            last_update_time = redis_conn.get(last_update_key)
                            
                            # 如果有上次更新时间，则转换为整数
                            if last_update_time:
                                try:
                                    last_update_time = int(last_update_time)
                                    logger.debug(f"股票 {stock.code} 的 {url_data['data_type']} 数据上次更新时间: {datetime.fromtimestamp(last_update_time)}")
                                except:
                                    last_update_time = None
                                    logger.warning(f"无法解析股票 {stock.code} 的 {url_data['data_type']} 数据上次更新时间")
                            else:
                                logger.debug(f"股票 {stock.code} 的 {url_data['data_type']} 数据没有上次更新记录")
                            
                            # 判断是否需要更新该数据
                            need_update = should_update_data(url_data.get('frequency', 'daily'), last_update_time)
                            
                            if need_update:
                                # 更新上次更新时间为当前时间
                                redis_conn.set(last_update_key, int(time.time()))
                                # 添加到队列
                                redis_conn.sadd(stock_detail_queue, json.dumps(url_data, ensure_ascii=False))
                                count += 1
                                status_counts['added_urls'] += 1
                                logger.info(f"添加URL到队列: {url_data['data_type']} ({url_data.get('frequency', 'unknown')}) for {stock.code}")
                            else:
                                status_counts['skipped_urls'] += 1
                                status_counts['skipped_due_to_frequency'] += 1
                                logger.info(f"跳过URL，不需要更新: {url_data['data_type']} (频率: {url_data.get('frequency', 'unknown')}) for {stock.code}")
                        else:
                            status_counts['skipped_urls'] += 1
                            status_counts['skipped_due_to_priority'] += 1
                            logger.debug(f"跳过URL，优先级不满足: {url_data['data_type']} (优先级: {url_data['priority']}) for {stock.code}")

                    # 批量处理日志 - 每处理10个股票就记录一次
                    if stock_processed % 10 == 0:
                        logger.info(f"已处理 {stock_processed} 个股票，{count} 个URL")

                    # 增量模式：如果队列长度达到目标，停止添加
                    if not force_full and redis_conn.scard(stock_detail_queue) >= batch_size:
                        logger.info(f"增量模式下队列长度已达到目标 ({redis_conn.scard(stock_detail_queue)} >= {batch_size})，停止添加")
                        break
                else:
                    logger.debug(f"跳过没有代码的股票")

            # 打印最终统计信息
            logger.info(f"股票详情队列填充完成统计:")
            logger.info(f"- 总股票数: {status_counts['total_stocks']}")
            logger.info(f"- 有代码的股票数: {status_counts['has_code']}")
            logger.info(f"- 已添加的URL数: {status_counts['added_urls']}")
            logger.info(f"- 已跳过的URL数: {status_counts['skipped_urls']}")
            logger.info(f"  - 因频率原因跳过: {status_counts['skipped_due_to_frequency']}")
            logger.info(f"  - 因优先级原因跳过: {status_counts['skipped_due_to_priority']}")
            logger.info(f"成功填充股票详情队列，共 {count} 个URL")
            return True

        finally:
            session.close()

    except Exception as e:
        logger.error(f"Error populating stock detail queue: {e}")
        return False


def record_failed_url(url_data, error_msg=""):
    """记录失败的URL"""
    redis_conn = get_redis_connection()
    if not redis_conn:
        return False

    try:
        # 解析URL数据
        if isinstance(url_data, str):
            try:
                url_info = json.loads(url_data)
            except:
                url_info = {'url': url_data}
        else:
            url_info = url_data

        # 添加失败信息
        failed_record = {
            'url_data': url_info,
            'failed_at': int(time.time()),
            'error_msg': error_msg,
            'failed_count': 1
        }

        # 检查是否已经存在失败记录
        failed_key = f"failed:{url_info.get('stock_code', 'unknown')}"
        existing_record = redis_conn.get(failed_key)

        if existing_record:
            try:
                existing_data = json.loads(existing_record)
                failed_record['failed_count'] = existing_data.get('failed_count', 0) + 1
            except:
                pass

        # 如果失败次数超过限制，删除该URL，否则记录失败
        if failed_record['failed_count'] >= max_failed_count:
            logger.warning(f"URL失败次数达到上限 ({max_failed_count})，删除: {url_info.get('url', 'unknown')}")
            redis_conn.delete(failed_key)
            return True
        else:
            # 记录失败，设置过期时间（24小时）
            redis_conn.setex(failed_key, 86400, json.dumps(failed_record, ensure_ascii=False))
            logger.info(f"记录URL失败 (第{failed_record['failed_count']}次): {url_info.get('url', 'unknown')}")
            return False

    except Exception as e:
        logger.error(f"记录失败URL时出错: {e}")
        return False


def get_queue_status():
    """获取队列状态信息"""
    redis_conn = get_redis_connection()
    if not redis_conn:
        return None

    try:
        status = {
            'xueqiu_detail_queue_length': redis_conn.scard(xueqiu_detail_queue),
            'eastmoney_min_queue_length': redis_conn.scard(eastmoney_min_queue),
            'stock_detail_queue_length': redis_conn.scard(stock_detail_queue),
            'failed_urls_count': len(redis_conn.keys('failed:*')),
            'min_queue_size': min_queue_size,
            'batch_size': batch_size,
            'max_failed_count': max_failed_count
        }
        return status
    except Exception as e:
        logger.error(f"获取队列状态时出错: {e}")
        return None


def clear_failed_records():
    """清理失败记录"""
    redis_conn = get_redis_connection()
    if not redis_conn:
        return False

    try:
        failed_keys = redis_conn.keys('failed:*')
        if failed_keys:
            redis_conn.delete(*failed_keys)
            logger.info(f"清理了 {len(failed_keys)} 个失败记录")
        return True
    except Exception as e:
        logger.error(f"清理失败记录时出错: {e}")
        return False