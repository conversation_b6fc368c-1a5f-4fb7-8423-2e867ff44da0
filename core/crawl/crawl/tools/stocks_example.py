#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
股票数据爬取优先级管理示例

这个文件展示了如何使用新的基于频率的优先级系统来管理股票数据的爬取。
"""

from stocks import (
    populate_stock_detail_queue,
    populate_stock_detail_queue_smart,
    get_queue_frequency_stats,
    PRIORITY_CONFIG,
    should_update_data,
    get_priority_by_frequency
)
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def example_basic_frequency_filtering():
    """示例1：基本的频率过滤功能"""
    logger.info("=== 示例1：基本的频率过滤功能 ===")
    
    # 显示优先级配置
    logger.info("优先级配置:")
    for freq, config in PRIORITY_CONFIG.items():
        logger.info(f"  {freq}: {config['name']} (优先级: {config['priority']}, 检查间隔: {config['check_interval']}天)")
    
    # 示例：只填充每日更新的数据
    logger.info("\n1. 只填充每日更新的数据 (daily)")
    success = populate_stock_detail_queue(force_full=False, frequency_filter='daily')
    if success:
        logger.info("✓ 每日数据填充成功")
    else:
        logger.error("✗ 每日数据填充失败")
    
    # 示例：填充每日和每周更新的数据
    logger.info("\n2. 填充每日和每周更新的数据 (weekly)")
    success = populate_stock_detail_queue(force_full=False, frequency_filter='weekly')
    if success:
        logger.info("✓ 每日+每周数据填充成功")
    else:
        logger.error("✗ 每日+每周数据填充失败")
    
    # 示例：填充所有频率的数据
    logger.info("\n3. 填充所有频率的数据 (quarterly)")
    success = populate_stock_detail_queue(force_full=False, frequency_filter='quarterly')
    if success:
        logger.info("✓ 所有频率数据填充成功")
    else:
        logger.error("✗ 所有频率数据填充失败")

def example_smart_queue_population():
    """示例2：智能队列填充功能"""
    logger.info("\n=== 示例2：智能队列填充功能 ===")
    
    # 智能填充：每日更新模式
    logger.info("\n1. 智能填充 - 每日更新模式")
    success = populate_stock_detail_queue_smart(force_full=False, target_frequency='daily')
    if success:
        logger.info("✓ 每日更新模式填充成功")
    else:
        logger.error("✗ 每日更新模式填充失败")
    
    # 智能填充：每月更新模式
    logger.info("\n2. 智能填充 - 每月更新模式")
    success = populate_stock_detail_queue_smart(force_full=False, target_frequency='monthly')
    if success:
        logger.info("✓ 每月更新模式填充成功")
    else:
        logger.error("✗ 每月更新模式填充失败")
    
    # 智能填充：季度更新模式
    logger.info("\n3. 智能填充 - 季度更新模式")
    success = populate_stock_detail_queue_smart(force_full=False, target_frequency='quarterly')
    if success:
        logger.info("✓ 季度更新模式填充成功")
    else:
        logger.error("✗ 季度更新模式填充失败")

def example_frequency_utilities():
    """示例3：频率工具函数的使用"""
    logger.info("\n=== 示例3：频率工具函数的使用 ===")
    
    # 测试频率优先级转换
    logger.info("\n1. 频率优先级转换:")
    for freq in ['daily', 'weekly', 'monthly', 'quarterly']:
        priority = get_priority_by_frequency(freq)
        logger.info(f"  {freq} -> 优先级 {priority}")
    
    # 测试数据更新判断
    logger.info("\n2. 数据更新判断:")
    import time
    current_time = int(time.time())
    
    # 模拟不同时间的数据
    test_cases = [
        ('daily', current_time - 3600),      # 1小时前
        ('daily', current_time - 86400),     # 1天前
        ('daily', current_time - 172800),    # 2天前
        ('monthly', current_time - 2592000), # 30天前
        ('monthly', current_time - 5184000), # 60天前
    ]
    
    for freq, last_update in test_cases:
        should_update = should_update_data(freq, last_update)
        days_ago = (current_time - last_update) // 86400
        logger.info(f"  {freq} 数据 ({days_ago}天前): {'需要更新' if should_update else '无需更新'}")

def example_queue_statistics():
    """示例4：队列统计信息"""
    logger.info("\n=== 示例4：队列统计信息 ===")
    
    # 获取队列频率统计
    stats = get_queue_frequency_stats()
    if stats:
        logger.info("队列频率统计:")
        for freq, count in stats.items():
            if freq != 'total':
                logger.info(f"  {freq}: {count}")
        logger.info(f"  总计: {stats['total']}")
    else:
        logger.warning("无法获取队列统计信息")

def example_usage_scenarios():
    """示例5：实际使用场景"""
    logger.info("\n=== 示例5：实际使用场景 ===")
    
    scenarios = [
        {
            'name': '日常维护',
            'description': '每日更新热门文章，每周更新基础数据',
            'frequency': 'weekly',
            'force_full': False
        },
        {
            'name': '月度更新',
            'description': '更新所有月度及更高频率的数据',
            'frequency': 'monthly',
            'force_full': False
        },
        {
            'name': '季度大更新',
            'description': '全量更新所有数据，包括季度数据',
            'frequency': 'quarterly',
            'force_full': True
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        logger.info(f"\n{i}. {scenario['name']}")
        logger.info(f"   描述: {scenario['description']}")
        logger.info(f"   频率: {scenario['frequency']}")
        logger.info(f"   全量: {scenario['force_full']}")
        
        # 执行场景
        success = populate_stock_detail_queue_smart(
            force_full=scenario['force_full'],
            target_frequency=scenario['frequency']
        )
        
        if success:
            logger.info(f"   ✓ {scenario['name']} 执行成功")
        else:
            logger.error(f"   ✗ {scenario['name']} 执行失败")

def main():
    """主函数"""
    logger.info("股票数据爬取优先级管理系统示例")
    logger.info("=" * 50)
    
    try:
        # 运行所有示例
        example_basic_frequency_filtering()
        example_smart_queue_population()
        example_frequency_utilities()
        example_queue_statistics()
        example_usage_scenarios()
        
        logger.info("\n" + "=" * 50)
        logger.info("所有示例执行完成！")
        
    except Exception as e:
        logger.error(f"示例执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

