# -*- coding: utf-8 -*-

"""
数据获取模块 - 使用DAO模式重构
保持向后兼容性，逐步迁移到新的DAO模式
"""

import datetime
import pandas as pd
import time
from typing import List, Optional, Tuple, Any

from utils.logger import logger
from utils.helper import sz_or_sh
from db.database import DBSession, engine, sqlUrl
from db.models import Stock, Quant, Daliy, Task, Stockkline
from dao.stock_dao import StockDAO, StockHistoryDAO, SourceDAO, StockklineDAO, StockCodeDAO
from dao.quant_dao import QuantDAO, DaliyDAO, TaskDAO

# 初始化DAO实例
stock_dao = StockDAO()
stock_code_dao = StockCodeDAO()
stock_history_dao = StockHistoryDAO()
source_dao = SourceDAO()
stockkline_dao = StockklineDAO()
quant_dao = QuantDAO()
daliy_dao = DaliyDAO()
task_dao = TaskDAO()


def is_trade_date(date: str) -> bool:
    """
    检查是否为交易日

    :param date: 日期字符串 (YYYY-MM-DD 或 YYYYMMDD)
    :return: 是否为交易日
    """
    try:
        # 标准化日期格式为YYYYMMDD（数据库中的格式）
        if len(date) == 10 and '-' in date:  # YYYY-MM-DD
            formatted_date = date.replace('-', '')
        else:
            formatted_date = date

        return daliy_dao.is_trade_date(formatted_date)
    except Exception as e:
        logger.error(f"Error checking trade date {date}: {e}")
        return False


def fetch_all_kline() -> List[str]:
    """
    获取所有K线图形函数

    :return: K线函数列表
    """
    try:
        kline_functions = stockkline_dao.get_all_kline_functions()
        logger.info('获取所有K线图形，总计%s', len(kline_functions))
        return kline_functions
    except Exception as e:
        logger.error(f"Error fetching all kline: {e}")
        return []

def fetch_all_stock_codes() -> List[str]:
    """
    获取所有股票代码

    :return: 股票代码列表
    """
    try:
        stock_codes = stock_code_dao.get_all_codes()
        logger.info('获取所有股票的code，总计%s', len(stock_codes))
        return stock_codes
    except Exception as e:
        logger.error(f"Error fetching all stock codes: {e}")

def fetch_all_stock(mark: bool = True) -> List[str]:
    """
    获取所有股票代码

    :param mark: 是否添加交易所前缀
    :return: 股票代码列表
    """
    try:
        session = DBSession()
        try:
            stocks = session.query(Stock).yield_per(1000)
            stock_arr = []

            for s in stocks:
                if s.code and len(s.code) == 6:
                    if mark:
                        prefixed_code = sz_or_sh(s.code)
                        if prefixed_code:
                            stock_arr.append(prefixed_code)
                    else:
                        stock_arr.append(s.code)

            logger.info('获取所有股票的code，总计%s', len(stock_arr))
            return stock_arr
        finally:
            session.close()
    except Exception as e:
        logger.error(f"Error fetching all stock codes: {e}")
        return []


def fetch_quant():
    """
     获取量化配置
    """
    session = DBSession()
    try:
        quant = session.query(Quant).filter(Quant.name=='quant').all()[0]

        start_date = quant.startDate
        end_date = quant.endDate

        if not quant.endDate:
            end_date = datetime.datetime.now().strftime("%Y-%m-%d")

        decision_date = quant.decisionDate

        if not quant.decisionDate:
            decision_date = start_date

        # 成交额
        average_amount = quant.averageAmount

        # 市值
        average_value = quant.averageValue

        return start_date, end_date, decision_date, average_amount, average_value, quant.id
    finally:
        session.close()

def fetch_quant_raw():
    """
     获取量化配置
    """
    session = DBSession()
    try:
        quant = session.query(Quant).filter(Quant.name=='quant').all()[0]
        return quant
    finally:
        session.close()

def fetch_all_history(begin, end):
    start_time = time.time()
    sql = 'SELECT * FROM {table} WHERE tradedate >= "{begin}" AND tradedate <= "{end}"'.format(table='stockHistory', begin=begin.replace('-', ''), end=end.replace('-', ''))
    try:
        df = pd.read_sql(sql, con=engine)
        # df = cx.read_sql(conn=sqlUrl, query=str(sql))
        end_time = time.time()
        diff_time = end_time - start_time
        print('时间消耗:', diff_time) 
        return df
    except Exception as e:
        print(e)

    return None

def fetch_all_stock_data():
    start_time = time.time()
    sql = 'SELECT * FROM {table}'.format(table='stock')
    try:
        df = pd.read_sql(sql, con=engine)
        # df = cx.read_sql(conn=sqlUrl, query=str(sql))
        end_time = time.time()
        diff_time = end_time - start_time
        print('时间消耗:', diff_time) 
        return df
    except Exception as e:
        print(e)

    return None

def fetch_all_stock_source():
    start_time = time.time()
    sql = 'SELECT * FROM {table}'.format(table='source')
    try:
        df = pd.read_sql(sql, con=engine)
        # df = cx.read_sql(conn=sqlUrl, query=str(sql))
        end_time = time.time()
        diff_time = end_time - start_time
        print('时间消耗:', diff_time) 
        return df
    except Exception as e:
        print(e)

    return None

def fetch_quant_stock():
    start_time = time.time()
    sql = 'SELECT * FROM {table}'.format(table='stockquant')
    try:
        df = pd.read_sql(sql, con=engine)
        # df = cx.read_sql(conn=sqlUrl, query=str(sql))
        end_time = time.time()
        diff_time = end_time - start_time
        print('时间消耗:', diff_time) 
        return df
    except Exception as e:
        print(e)

    return None

def quant_monitor(quantId, success):
    session = DBSession()
    
    task = session.query(Task).get(quantId)

    if task:
        if success:
            task.success = task.success + 1
        else: 
            task.fail = task.fail + 1
        task.lastUpdated = datetime.datetime.now()
        session.commit()
        session.close()
