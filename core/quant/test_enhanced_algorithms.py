#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
增强算法测试脚本
Enhanced Algorithms Test Script

用于测试和验证增强版股票分析算法的效果

使用方法:
python test_enhanced_algorithms.py --code 000001 --days 30
python test_enhanced_algorithms.py --scan --limit 10

作者: AI量化分析师
版本: v1.0
"""

import sys
import os
from pathlib import Path
import argparse
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from utils.logger import logger
from utils.helper import ago_day_timestr
from db.fetch import fetch_all_stock, fetch_all_history, is_trade_date
from tasks.tools.enhanced_signal_generator import EnhancedSignalGenerator
from tasks.tools.enhanced_risk_control import EnhancedRiskControl
from tasks.tools.technical_indicators import TechnicalIndicators


def test_single_stock(stock_code: str, days: int = 30):
    """测试单只股票的增强算法效果"""
    try:
        logger.info(f"开始测试股票 {stock_code} 的增强算法效果")
        
        # 获取历史数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = ago_day_timestr(days + 60, '%Y%m%d')  # 多获取一些数据用于指标计算
        
        df = fetch_all_history(begin=start_date, end=end_date)
        if df.empty:
            logger.error("无法获取历史数据")
            return
        
        stock_data = df[df['code'] == stock_code].copy()
        if stock_data.empty:
            logger.error(f"股票 {stock_code} 无历史数据")
            return
        
        # 确保数据排序
        stock_data = stock_data.sort_values('tradedate').reset_index(drop=True)
        
        if len(stock_data) < 30:
            logger.error(f"股票 {stock_code} 数据不足")
            return
        
        logger.info(f"获取到 {len(stock_data)} 条历史数据")
        
        # 初始化增强算法组件
        enhanced_generator = EnhancedSignalGenerator()
        enhanced_risk_control = EnhancedRiskControl()
        technical_indicators = TechnicalIndicators()
        
        # 测试技术指标计算
        logger.info("=" * 60)
        logger.info("技术指标测试结果:")
        logger.info("=" * 60)
        
        # 测试增强版均线信号
        ma_signals = technical_indicators.calculate_ma_signals(stock_data)
        logger.info(f"增强版均线信号: {ma_signals}")
        
        # 测试增强版MACD信号
        macd_signals = technical_indicators.calculate_macd_signals(stock_data)
        logger.info(f"增强版MACD信号: {macd_signals}")
        
        # 测试增强版RSI信号
        rsi_signals = technical_indicators.calculate_rsi_signals(stock_data)
        logger.info(f"增强版RSI信号: {rsi_signals}")
        
        # 测试增强版布林带信号
        bb_signals = technical_indicators.calculate_bollinger_bands(stock_data)
        logger.info(f"增强版布林带信号: {bb_signals}")
        
        # 测试增强版ADX信号
        adx_signals = technical_indicators.calculate_adx_signals(stock_data)
        logger.info(f"增强版ADX信号: {adx_signals}")
        
        # 测试风险控制
        logger.info("=" * 60)
        logger.info("风险控制测试结果:")
        logger.info("=" * 60)
        
        current_price = float(stock_data.iloc[-1]['close'])
        
        # 测试动态止损
        stop_loss_buy = enhanced_risk_control.calculate_dynamic_stop_loss(
            stock_data, current_price, 'buy'
        )
        stop_loss_sell = enhanced_risk_control.calculate_dynamic_stop_loss(
            stock_data, current_price, 'sell'
        )
        
        # 测试动态止盈
        take_profit_buy = enhanced_risk_control.calculate_dynamic_take_profit(
            stock_data, current_price, 'buy'
        )
        take_profit_sell = enhanced_risk_control.calculate_dynamic_take_profit(
            stock_data, current_price, 'sell'
        )
        
        logger.info(f"当前价格: {current_price:.2f}")
        logger.info(f"买入止损: {stop_loss_buy:.2f} ({(stop_loss_buy/current_price-1)*100:.2f}%)")
        logger.info(f"买入止盈: {take_profit_buy:.2f} ({(take_profit_buy/current_price-1)*100:.2f}%)")
        logger.info(f"卖出止损: {stop_loss_sell:.2f} ({(stop_loss_sell/current_price-1)*100:.2f}%)")
        logger.info(f"卖出止盈: {take_profit_sell:.2f} ({(take_profit_sell/current_price-1)*100:.2f}%)")
        
        # 测试仓位计算
        account_value = 100000  # 10万账户
        position_size = enhanced_risk_control.calculate_position_size(
            account_value, current_price, stop_loss_buy
        )
        logger.info(f"建议仓位: {position_size} 股 (约 {position_size * current_price:.0f} 元)")
        
        # 测试风险评估
        all_signals = {}
        all_signals.update(ma_signals)
        all_signals.update(macd_signals)
        all_signals.update(rsi_signals)
        all_signals.update(bb_signals)
        all_signals.update(adx_signals)
        
        risk_level = enhanced_risk_control.assess_risk_level(stock_data, all_signals)
        logger.info(f"风险等级: {risk_level.value}")
        
        # 测试增强版信号生成
        logger.info("=" * 60)
        logger.info("增强版信号生成测试结果:")
        logger.info("=" * 60)
        
        enhanced_signal = enhanced_generator.generate_enhanced_signal(stock_data, account_value)
        
        if enhanced_signal:
            logger.info("✅ 成功生成增强版交易信号:")
            logger.info(enhanced_generator.get_signal_summary(enhanced_signal))
            
            # 详细信息
            signal_dict = enhanced_signal.to_dict()
            for key, value in signal_dict.items():
                if isinstance(value, list) and value:
                    logger.info(f"  {key}: {', '.join(value[:3])}")
                elif not isinstance(value, list):
                    logger.info(f"  {key}: {value}")
        else:
            logger.info("❌ 未生成交易信号（可能因为信号强度不足或风险过高）")
        
        logger.info("=" * 60)
        logger.info("测试完成")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"测试单只股票失败: {e}")


def test_market_scan(limit: int = 10):
    """测试市场扫描功能"""
    try:
        logger.info(f"开始测试市场扫描功能，限制 {limit} 只股票")
        
        # 获取股票列表
        stock_codes = fetch_all_stock(mark=False)
        if not stock_codes:
            logger.error("无法获取股票列表")
            return
        
        # 限制测试数量
        test_codes = stock_codes[:limit]
        logger.info(f"将测试以下股票: {', '.join(test_codes)}")
        
        # 获取历史数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = ago_day_timestr(90, '%Y%m%d')
        
        df = fetch_all_history(begin=start_date, end=end_date)
        if df.empty:
            logger.error("无法获取历史数据")
            return
        
        # 初始化增强算法
        enhanced_generator = EnhancedSignalGenerator()
        
        results = []
        
        for i, code in enumerate(test_codes):
            try:
                logger.info(f"处理股票 {code} ({i+1}/{len(test_codes)})")
                
                stock_data = df[df['code'] == code].copy()
                if stock_data.empty or len(stock_data) < 30:
                    logger.warning(f"股票 {code} 数据不足，跳过")
                    continue
                
                stock_data = stock_data.sort_values('tradedate').reset_index(drop=True)
                
                # 生成增强版信号
                enhanced_signal = enhanced_generator.generate_enhanced_signal(stock_data)
                
                if enhanced_signal:
                    result = {
                        'code': code,
                        'signal_type': enhanced_signal.signal_type.value,
                        'strength': enhanced_signal.strength.value,
                        'confidence': enhanced_signal.confidence,
                        'current_price': enhanced_signal.entry_price,
                        'stop_loss': enhanced_signal.stop_loss,
                        'take_profit': enhanced_signal.take_profit,
                        'risk_level': enhanced_signal.risk_level.value,
                        'supporting_factors': len(enhanced_signal.supporting_factors),
                        'warning_factors': len(enhanced_signal.warning_factors)
                    }
                    results.append(result)
                    
            except Exception as e:
                logger.warning(f"处理股票 {code} 失败: {e}")
                continue
        
        # 输出结果
        logger.info("=" * 80)
        logger.info("市场扫描结果汇总:")
        logger.info("=" * 80)
        
        if results:
            # 按置信度排序
            results.sort(key=lambda x: x['confidence'], reverse=True)
            
            logger.info(f"共找到 {len(results)} 个有效信号:")
            
            for i, result in enumerate(results[:10]):  # 显示前10个
                logger.info(f"{i+1:2d}. {result['code']} | "
                          f"{result['signal_type']:12s} | "
                          f"强度:{result['strength']:12s} | "
                          f"置信度:{result['confidence']:5.1%} | "
                          f"价格:{result['current_price']:7.2f} | "
                          f"风险:{result['risk_level']:8s}")
        else:
            logger.info("未找到符合条件的交易信号")
        
        logger.info("=" * 80)
        logger.info("市场扫描测试完成")
        logger.info("=" * 80)
        
    except Exception as e:
        logger.error(f"测试市场扫描失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增强算法测试脚本')
    parser.add_argument('--code', type=str, help='股票代码（测试单只股票）')
    parser.add_argument('--days', type=int, default=30, help='测试天数（默认30天）')
    parser.add_argument('--scan', action='store_true', help='市场扫描测试')
    parser.add_argument('--limit', type=int, default=10, help='扫描股票数量限制（默认10只）')
    
    args = parser.parse_args()
    
    logger.info("🚀 增强算法测试开始")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if args.code:
        # 测试单只股票
        test_single_stock(args.code, args.days)
    elif args.scan:
        # 测试市场扫描
        test_market_scan(args.limit)
    else:
        # 默认测试
        logger.info("未指定测试模式，执行默认测试...")
        test_single_stock('000001', 30)  # 测试平安银行
    
    logger.info("✅ 增强算法测试完成")


if __name__ == '__main__':
    main()
