import pandas as pd
import numpy as np
import talib
from typing import Dict, Any, Optional

from config.settings import get_settings
from utils.logger import logger

class TechnicalIndicators:
    """增强版技术指标计算类 - 优化算法版本"""

    def __init__(self):
        self.settings = get_settings()

        # 技术指标参数配置
        self.params = {
            'ma_periods': [5, 10, 20, 30, 60, 120],  # 均线周期
            'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9,  # MACD参数
            'rsi_period': 14,  # RSI周期
            'kdj_period': 9,   # KDJ周期
            'bollinger_period': 20, 'bollinger_std': 2,  # 布林带参数
            'atr_period': 14,  # ATR周期
            'cci_period': 14,  # CCI周期
            'williams_period': 14,  # 威廉指标周期
            'stoch_period': 14,  # 随机指标周期
            'adx_period': 14,  # ADX周期
            'obv_period': 10,  # OBV周期
            'volume_ma_period': 20,  # 成交量均线周期
        }

        # 增强算法参数
        self.enhanced_params = {
            'noise_filter_threshold': 0.005,  # 噪音过滤阈值(0.5%)
            'volume_confirmation_ratio': 1.2,  # 成交量确认倍数
            'trend_strength_threshold': 0.02,  # 趋势强度阈值(2%)
            'divergence_lookback': 20,  # 背离检测回看周期
            'breakout_confirmation_periods': 3,  # 突破确认周期
            'multi_timeframe_weight': 0.3,  # 多周期权重
        }
    
    def calculate_ma_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """增强版均线信号计算 - 引入波动过滤和成交量确认"""
        try:
            if len(df) < 30:
                return {}

            close = df['close'].astype(float)
            volume = df['volume'].astype(float)

            # 计算多条均线
            ma5 = close.rolling(5).mean()
            ma10 = close.rolling(10).mean()
            ma20 = close.rolling(20).mean()
            ma30 = close.rolling(30).mean()
            ma60 = close.rolling(60).mean()

            # 计算成交量均线用于确认
            vol_ma20 = volume.rolling(20).mean()

            current = df.iloc[-1]
            prev = df.iloc[-2]

            signals = {}

            # 增强版均线多头排列判断 - 加入趋势强度过滤
            ma_trend_strength = self._calculate_ma_trend_strength(ma5, ma10, ma20, ma30)

            if (ma5.iloc[-1] > ma10.iloc[-1] > ma20.iloc[-1] > ma30.iloc[-1]):
                # 检查趋势强度是否足够
                if ma_trend_strength > self.enhanced_params['trend_strength_threshold']:
                    signals['ma_bullish'] = True
                    signals['ma_strength'] = min(ma_trend_strength * 10, 1.0)  # 标准化到0-1
                else:
                    signals['ma_bullish'] = None  # 趋势不够强，保持中性
                    signals['ma_strength'] = 0.0
            elif (ma5.iloc[-1] < ma10.iloc[-1] < ma20.iloc[-1] < ma30.iloc[-1]):
                if ma_trend_strength > self.enhanced_params['trend_strength_threshold']:
                    signals['ma_bullish'] = False
                    signals['ma_strength'] = -min(ma_trend_strength * 10, 1.0)
                else:
                    signals['ma_bullish'] = None
                    signals['ma_strength'] = 0.0
            else:
                signals['ma_bullish'] = None
                signals['ma_strength'] = 0.0

            # 增强版金叉死叉信号 - 加入成交量确认和噪音过滤
            ma5_change = abs(ma5.iloc[-1] - ma5.iloc[-2]) / ma5.iloc[-2]
            volume_confirmation = current['volume'] > vol_ma20.iloc[-1] * self.enhanced_params['volume_confirmation_ratio']

            # 金叉信号增强
            if (ma5.iloc[-1] > ma10.iloc[-1] and ma5.iloc[-2] <= ma10.iloc[-2]):
                # 检查是否为有效突破（非噪音）
                if (ma5_change > self.enhanced_params['noise_filter_threshold'] and
                    volume_confirmation):
                    signals['golden_cross'] = True
                    signals['golden_cross_strength'] = 1.0
                else:
                    signals['golden_cross'] = False
                    signals['golden_cross_strength'] = 0.5  # 弱信号
            elif (ma5.iloc[-1] < ma10.iloc[-1] and ma5.iloc[-2] >= ma10.iloc[-2]):
                if (ma5_change > self.enhanced_params['noise_filter_threshold'] and
                    volume_confirmation):
                    signals['death_cross'] = True
                    signals['death_cross_strength'] = 1.0
                else:
                    signals['death_cross'] = False
                    signals['death_cross_strength'] = 0.5
            else:
                signals['golden_cross'] = False
                signals['death_cross'] = False
                signals['golden_cross_strength'] = 0.0
                signals['death_cross_strength'] = 0.0

            # 多周期共振检测
            signals.update(self._detect_multi_timeframe_resonance(df, ma5, ma10, ma20))

            # 价格相对均线位置
            signals['price_vs_ma5'] = current['close'] / ma5.iloc[-1] - 1
            signals['price_vs_ma10'] = current['close'] / ma10.iloc[-1] - 1
            signals['price_vs_ma20'] = current['close'] / ma20.iloc[-1] - 1

            return signals

        except Exception as e:
            logger.error(f"计算增强版均线信号失败: {e}")
            return {}
    
    def calculate_macd_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """增强版MACD信号计算 - 改进背离检测和假信号过滤"""
        try:
            if len(df) < 50:
                return {}

            close = df['close'].astype(float).values
            volume = df['volume'].astype(float).values

            # 计算MACD
            macd, signal, hist = talib.MACD(
                close,
                fastperiod=12,
                slowperiod=26,
                signalperiod=9
            )

            signals = {}

            # 去除NaN值
            valid_indices = ~(np.isnan(macd) | np.isnan(signal))
            if np.sum(valid_indices) < 2:
                return signals

            macd_clean = macd[valid_indices]
            signal_clean = signal[valid_indices]
            hist_clean = hist[valid_indices]
            volume_clean = volume[valid_indices] if len(volume) == len(macd) else volume[-len(macd_clean):]

            if len(macd_clean) < 2:
                return signals

            # 增强版金叉死叉 - 加入强度和成交量确认
            macd_cross_strength = abs(macd_clean[-1] - signal_clean[-1]) / abs(signal_clean[-1]) if signal_clean[-1] != 0 else 0
            volume_ma = np.mean(volume_clean[-20:]) if len(volume_clean) >= 20 else np.mean(volume_clean)
            volume_confirmation = volume_clean[-1] > volume_ma * self.enhanced_params['volume_confirmation_ratio']

            if (macd_clean[-1] > signal_clean[-1] and
                macd_clean[-2] <= signal_clean[-2]):
                # 检查金叉强度和成交量确认
                if macd_cross_strength > 0.1 and volume_confirmation:
                    signals['macd_golden_cross'] = True
                    signals['macd_signal'] = 1
                    signals['macd_cross_strength'] = min(macd_cross_strength * 10, 1.0)
                else:
                    signals['macd_golden_cross'] = False
                    signals['macd_signal'] = 0
                    signals['macd_cross_strength'] = 0.3  # 弱信号
            elif (macd_clean[-1] < signal_clean[-1] and
                  macd_clean[-2] >= signal_clean[-2]):
                if macd_cross_strength > 0.1 and volume_confirmation:
                    signals['macd_death_cross'] = True
                    signals['macd_signal'] = -1
                    signals['macd_cross_strength'] = min(macd_cross_strength * 10, 1.0)
                else:
                    signals['macd_death_cross'] = False
                    signals['macd_signal'] = 0
                    signals['macd_cross_strength'] = 0.3
            else:
                signals['macd_golden_cross'] = False
                signals['macd_death_cross'] = False
                signals['macd_signal'] = 0
                signals['macd_cross_strength'] = 0.0

            # 增强版MACD柱状图分析 - 连续性检测
            if len(hist_clean) >= 5:
                # 检测连续上升/下降趋势
                consecutive_up = all(hist_clean[i] > hist_clean[i-1] for i in range(-3, 0))
                consecutive_down = all(hist_clean[i] < hist_clean[i-1] for i in range(-3, 0))

                if consecutive_up:
                    signals['macd_hist_trend'] = 1
                    signals['macd_momentum_strength'] = 1.0
                elif consecutive_down:
                    signals['macd_hist_trend'] = -1
                    signals['macd_momentum_strength'] = 1.0
                else:
                    signals['macd_hist_trend'] = 0
                    signals['macd_momentum_strength'] = 0.5

            # 增强版MACD背离检测
            signals['macd_divergence'] = self._detect_enhanced_macd_divergence(df, macd_clean)

            # MACD零轴突破检测
            signals.update(self._detect_macd_zero_line_cross(macd_clean, volume_clean))

            return signals

        except Exception as e:
            logger.error(f"计算增强版MACD信号失败: {e}")
            return {}
    
    def calculate_rsi_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """增强版RSI信号计算 - 加入背离检测和动态阈值"""
        try:
            if len(df) < 30:
                return {}

            close = df['close'].astype(float).values
            volume = df['volume'].astype(float).values
            rsi = talib.RSI(close, timeperiod=14)

            signals = {}

            # 去除NaN值
            valid_rsi = rsi[~np.isnan(rsi)]
            if len(valid_rsi) == 0:
                return signals

            current_rsi = valid_rsi[-1]

            # 动态RSI阈值 - 根据市场波动性调整
            volatility = np.std(close[-20:]) / np.mean(close[-20:]) if len(close) >= 20 else 0.02

            # 高波动市场使用更极端的阈值
            if volatility > 0.05:  # 高波动
                overbought_threshold = 85
                oversold_threshold = 15
            elif volatility > 0.03:  # 中等波动
                overbought_threshold = 80
                oversold_threshold = 20
            else:  # 低波动
                overbought_threshold = 75
                oversold_threshold = 25

            # 增强版RSI超买超卖判断
            volume_ma = np.mean(volume[-20:]) if len(volume) >= 20 else np.mean(volume)
            volume_confirmation = volume[-1] > volume_ma * self.enhanced_params['volume_confirmation_ratio']

            if current_rsi > overbought_threshold:
                signals['rsi_overbought'] = True
                signals['rsi_signal'] = -1 if volume_confirmation else 0
                signals['rsi_strength'] = min((current_rsi - overbought_threshold) / 10, 1.0)
            elif current_rsi < oversold_threshold:
                signals['rsi_oversold'] = True
                signals['rsi_signal'] = 1 if volume_confirmation else 0
                signals['rsi_strength'] = min((oversold_threshold - current_rsi) / 10, 1.0)
            else:
                signals['rsi_overbought'] = False
                signals['rsi_oversold'] = False
                signals['rsi_signal'] = 0
                signals['rsi_strength'] = 0.0

            # 增强版RSI趋势分析
            if len(valid_rsi) >= 5:
                # 检测连续趋势
                consecutive_up = all(valid_rsi[i] > valid_rsi[i-1] for i in range(-3, 0))
                consecutive_down = all(valid_rsi[i] < valid_rsi[i-1] for i in range(-3, 0))

                if consecutive_up:
                    signals['rsi_trend'] = 1
                    signals['rsi_momentum'] = 1.0
                elif consecutive_down:
                    signals['rsi_trend'] = -1
                    signals['rsi_momentum'] = 1.0
                else:
                    signals['rsi_trend'] = 0
                    signals['rsi_momentum'] = 0.5

            # RSI背离检测
            signals['rsi_divergence'] = self._detect_rsi_divergence(df, valid_rsi)

            # RSI中线突破检测
            if len(valid_rsi) >= 3:
                if valid_rsi[-1] > 50 and valid_rsi[-2] <= 50:
                    signals['rsi_bullish_cross'] = True
                elif valid_rsi[-1] < 50 and valid_rsi[-2] >= 50:
                    signals['rsi_bearish_cross'] = True
                else:
                    signals['rsi_bullish_cross'] = False
                    signals['rsi_bearish_cross'] = False

            signals['rsi_value'] = current_rsi
            signals['rsi_dynamic_overbought'] = overbought_threshold
            signals['rsi_dynamic_oversold'] = oversold_threshold

            return signals

        except Exception as e:
            logger.error(f"计算增强版RSI信号失败: {e}")
            return {}
    
    def calculate_volume_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算成交量信号"""
        try:
            if len(df) < 20:
                return {}
            
            signals = {}
            
            # 计算成交量均线
            volume = df['volume'].astype(float)
            volume_ma5 = volume.rolling(5).mean()
            volume_ma10 = volume.rolling(10).mean()
            volume_ma20 = volume.rolling(20).mean()
            
            current = df.iloc[-1]
            
            # 放量信号
            if volume.iloc[-1] > volume_ma5.iloc[-1] * 2:
                signals['volume_surge'] = True
                signals['volume_ratio'] = volume.iloc[-1] / volume_ma5.iloc[-1]
            else:
                signals['volume_surge'] = False
                signals['volume_ratio'] = volume.iloc[-1] / volume_ma5.iloc[-1]
            
            # 量价配合
            price_change = current['pctChg']
            if price_change > 0 and signals['volume_surge']:
                signals['volume_price_positive'] = True
            elif price_change < 0 and signals['volume_surge']:
                signals['volume_price_negative'] = True
            else:
                signals['volume_price_positive'] = False
                signals['volume_price_negative'] = False
            
            # OBV指标
            obv = talib.OBV(df['close'].astype(float).values, 
                           df['volume'].astype(float).values)
            if len(obv) >= 2:
                signals['obv_trend'] = 1 if obv[-1] > obv[-2] else -1
            
            return signals
            
        except Exception as e:
            logger.error(f"计算成交量信号失败: {e}")
            return {}
    
    def calculate_kdj_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算KDJ信号"""
        try:
            if len(df) < 30:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            k, d = talib.STOCH(high, low, close, 
                              fastk_period=9, 
                              slowk_period=3, 
                              slowk_matype=0,
                              slowd_period=3, 
                              slowd_matype=0)
            
            signals = {}
            
            # 去除NaN值
            valid_indices = ~(np.isnan(k) | np.isnan(d))
            if np.sum(valid_indices) < 2:
                return signals
            
            k_clean = k[valid_indices]
            d_clean = d[valid_indices]
            
            if len(k_clean) < 2:
                return signals
            
            # 计算J值
            j = 3 * k_clean - 2 * d_clean
            
            current_k = k_clean[-1]
            current_d = d_clean[-1]
            current_j = j[-1]
            
            # KDJ金叉死叉
            if (current_k > current_d and k_clean[-2] <= d_clean[-2]):
                signals['kdj_golden_cross'] = True
                signals['kdj_signal'] = 1
            elif (current_k < current_d and k_clean[-2] >= d_clean[-2]):
                signals['kdj_death_cross'] = True
                signals['kdj_signal'] = -1
            else:
                signals['kdj_golden_cross'] = False
                signals['kdj_death_cross'] = False
                signals['kdj_signal'] = 0
            
            # 超买超卖
            if current_k > 80 and current_d > 80:
                signals['kdj_overbought'] = True
            elif current_k < 20 and current_d < 20:
                signals['kdj_oversold'] = True
            else:
                signals['kdj_overbought'] = False
                signals['kdj_oversold'] = False
            
            signals['k_value'] = current_k
            signals['d_value'] = current_d
            signals['j_value'] = current_j
            
            return signals
            
        except Exception as e:
            logger.error(f"计算KDJ信号失败: {e}")
            return {}
    
    def calculate_support_resistance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算支撑阻力位"""
        try:
            if len(df) < 60:
                return {}
            
            high = df['high'].astype(float)
            low = df['low'].astype(float)
            close = df['close'].astype(float)
            current_price = close.iloc[-1]
            
            signals = {}
            
            # 计算近期高低点
            recent_high = high.tail(20).max()
            recent_low = low.tail(20).min()
            
            # 支撑位和阻力位
            resistance_levels = []
            support_levels = []
            
            # 寻找阻力位（近期高点附近）
            for i in range(len(high) - 20, len(high)):
                if high.iloc[i] >= recent_high * 0.98:
                    resistance_levels.append(high.iloc[i])
            
            # 寻找支撑位（近期低点附近）
            for i in range(len(low) - 20, len(low)):
                if low.iloc[i] <= recent_low * 1.02:
                    support_levels.append(low.iloc[i])
            
            # 去重并排序
            resistance_levels = sorted(list(set(resistance_levels)), reverse=True)
            support_levels = sorted(list(set(support_levels)))
            
            signals['nearest_resistance'] = resistance_levels[0] if resistance_levels else None
            signals['nearest_support'] = support_levels[0] if support_levels else None
            
            # 价格位置判断
            if signals['nearest_resistance'] and signals['nearest_support']:
                resistance_distance = (signals['nearest_resistance'] - current_price) / current_price
                support_distance = (current_price - signals['nearest_support']) / current_price
                
                if resistance_distance < 0.05:  # 接近阻力位
                    signals['near_resistance'] = True
                elif support_distance < 0.05:  # 接近支撑位
                    signals['near_support'] = True
                else:
                    signals['near_resistance'] = False
                    signals['near_support'] = False
            
            return signals
            
        except Exception as e:
            logger.error(f"计算支撑阻力位失败: {e}")
            return {}
    
    def calculate_bollinger_bands(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算布林带信号"""
        try:
            if len(df) < self.params['bollinger_period']:
                return {}
            
            close = df['close'].astype(float).values
            period = self.params['bollinger_period']
            std_dev = self.params['bollinger_std']
            
            # 计算布林带
            upper, middle, lower = talib.BBANDS(
                close, 
                timeperiod=period, 
                nbdevup=std_dev, 
                nbdevdn=std_dev, 
                matype=0
            )
            
            signals = {}
            
            # 去除NaN值
            valid_indices = ~(np.isnan(upper) | np.isnan(middle) | np.isnan(lower))
            if np.sum(valid_indices) < 2:
                return signals
            
            upper_clean = upper[valid_indices]
            middle_clean = middle[valid_indices]
            lower_clean = lower[valid_indices]
            
            if len(upper_clean) < 2:
                return signals
            
            current_price = close[-1]
            current_upper = upper_clean[-1]
            current_middle = middle_clean[-1]
            current_lower = lower_clean[-1]
            
            # 价格位置判断
            if current_price >= current_upper:
                signals['bb_upper_breakout'] = True
                signals['bb_position'] = 'above_upper'
            elif current_price <= current_lower:
                signals['bb_lower_breakout'] = True
                signals['bb_position'] = 'below_lower'
            elif current_price > current_middle:
                signals['bb_position'] = 'above_middle'
            else:
                signals['bb_position'] = 'below_middle'
            
            # 布林带宽度（波动率）
            bb_width = (current_upper - current_lower) / current_middle
            signals['bb_width'] = bb_width
            
            # 布林带收缩/扩张
            if len(upper_clean) >= 5:
                prev_width = (upper_clean[-5] - lower_clean[-5]) / middle_clean[-5]
                if bb_width < prev_width * 0.8:
                    signals['bb_squeeze'] = True  # 布林带收缩
                elif bb_width > prev_width * 1.2:
                    signals['bb_expansion'] = True  # 布林带扩张
            
            return signals
            
        except Exception as e:
            logger.error(f"计算布林带信号失败: {e}")
            return {}
    
    def calculate_atr_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算ATR（平均真实波幅）信号"""
        try:
            if len(df) < self.params['atr_period']:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            # 计算ATR
            atr = talib.ATR(high, low, close, timeperiod=self.params['atr_period'])
            
            signals = {}
            
            # 去除NaN值
            valid_atr = atr[~np.isnan(atr)]
            if len(valid_atr) < 2:
                return signals
            
            current_atr = valid_atr[-1]
            prev_atr = valid_atr[-2]
            
            # ATR变化趋势
            if current_atr > prev_atr:
                signals['atr_increasing'] = True
                signals['atr_trend'] = 1
            elif current_atr < prev_atr:
                signals['atr_decreasing'] = True
                signals['atr_trend'] = -1
            else:
                signals['atr_trend'] = 0
            
            # ATR相对水平
            atr_ma = np.mean(valid_atr[-20:]) if len(valid_atr) >= 20 else np.mean(valid_atr)
            if current_atr > atr_ma * 1.5:
                signals['atr_high_volatility'] = True
            elif current_atr < atr_ma * 0.5:
                signals['atr_low_volatility'] = True
            
            signals['atr_value'] = current_atr
            signals['atr_ratio'] = current_atr / atr_ma if atr_ma > 0 else 1.0
            
            return signals
            
        except Exception as e:
            logger.error(f"计算ATR信号失败: {e}")
            return {}
    
    def calculate_cci_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算CCI（商品通道指数）信号"""
        try:
            if len(df) < self.params['cci_period']:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            # 计算CCI
            cci = talib.CCI(high, low, close, timeperiod=self.params['cci_period'])
            
            signals = {}
            
            # 去除NaN值
            valid_cci = cci[~np.isnan(cci)]
            if len(valid_cci) < 2:
                return signals
            
            current_cci = valid_cci[-1]
            prev_cci = valid_cci[-2]
            
            # CCI超买超卖
            if current_cci > 100:
                signals['cci_overbought'] = True
                signals['cci_signal'] = -1
            elif current_cci < -100:
                signals['cci_oversold'] = True
                signals['cci_signal'] = 1
            else:
                signals['cci_signal'] = 0
            
            # CCI趋势
            if current_cci > prev_cci:
                signals['cci_trend'] = 1
            elif current_cci < prev_cci:
                signals['cci_trend'] = -1
            else:
                signals['cci_trend'] = 0
            
            # CCI背离检测
            if len(valid_cci) >= 10:
                recent_highs = []
                recent_lows = []
                price_highs = []
                price_lows = []
                
                # 确保close数组长度匹配
                close_array = close[-len(valid_cci):]
                
                for i in range(len(valid_cci) - 10, len(valid_cci)):
                    if i > 0 and i < len(valid_cci) - 1:
                        if valid_cci[i] > valid_cci[i-1] and valid_cci[i] > valid_cci[i+1]:
                            recent_highs.append(valid_cci[i])
                            price_highs.append(close_array[i])
                        elif valid_cci[i] < valid_cci[i-1] and valid_cci[i] < valid_cci[i+1]:
                            recent_lows.append(valid_cci[i])
                            price_lows.append(close_array[i])
                
                if len(recent_highs) >= 2 and len(price_highs) >= 2:
                    if recent_highs[-1] < recent_highs[-2] and price_highs[-1] > price_highs[-2]:
                        signals['cci_bearish_divergence'] = True
                
                if len(recent_lows) >= 2 and len(price_lows) >= 2:
                    if recent_lows[-1] > recent_lows[-2] and price_lows[-1] < price_lows[-2]:
                        signals['cci_bullish_divergence'] = True
            
            signals['cci_value'] = current_cci
            
            return signals
            
        except Exception as e:
            logger.error(f"计算CCI信号失败: {e}")
            return {}
    
    def calculate_williams_r(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算威廉指标信号"""
        try:
            if len(df) < self.params['williams_period']:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            # 计算威廉指标
            williams_r = talib.WILLR(high, low, close, timeperiod=self.params['williams_period'])
            
            signals = {}
            
            # 去除NaN值
            valid_wr = williams_r[~np.isnan(williams_r)]
            if len(valid_wr) < 2:
                return signals
            
            current_wr = valid_wr[-1]
            prev_wr = valid_wr[-2]
            
            # 威廉指标超买超卖
            if current_wr > -20:
                signals['williams_overbought'] = True
                signals['williams_signal'] = -1
            elif current_wr < -80:
                signals['williams_oversold'] = True
                signals['williams_signal'] = 1
            else:
                signals['williams_signal'] = 0
            
            # 威廉指标趋势
            if current_wr > prev_wr:
                signals['williams_trend'] = 1
            elif current_wr < prev_wr:
                signals['williams_trend'] = -1
            else:
                signals['williams_trend'] = 0
            
            signals['williams_value'] = current_wr
            
            return signals
            
        except Exception as e:
            logger.error(f"计算威廉指标失败: {e}")
            return {}
    
    def calculate_stochastic_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算随机指标信号"""
        try:
            if len(df) < self.params['stoch_period']:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            # 计算随机指标
            slowk, slowd = talib.STOCH(
                high, low, close,
                fastk_period=self.params['stoch_period'],
                slowk_period=3,
                slowk_matype=0,
                slowd_period=3,
                slowd_matype=0
            )
            
            signals = {}
            
            # 去除NaN值
            valid_indices = ~(np.isnan(slowk) | np.isnan(slowd))
            if np.sum(valid_indices) < 2:
                return signals
            
            slowk_clean = slowk[valid_indices]
            slowd_clean = slowd[valid_indices]
            
            if len(slowk_clean) < 2:
                return signals
            
            current_k = slowk_clean[-1]
            current_d = slowd_clean[-1]
            prev_k = slowk_clean[-2]
            prev_d = slowd_clean[-2]
            
            # 随机指标超买超卖
            if current_k > 80 and current_d > 80:
                signals['stoch_overbought'] = True
                signals['stoch_signal'] = -1
            elif current_k < 20 and current_d < 20:
                signals['stoch_oversold'] = True
                signals['stoch_signal'] = 1
            else:
                signals['stoch_signal'] = 0
            
            # 随机指标金叉死叉
            if current_k > current_d and prev_k <= prev_d:
                signals['stoch_golden_cross'] = True
            elif current_k < current_d and prev_k >= prev_d:
                signals['stoch_death_cross'] = True
            
            signals['stoch_k'] = current_k
            signals['stoch_d'] = current_d
            
            return signals
            
        except Exception as e:
            logger.error(f"计算随机指标失败: {e}")
            return {}
    
    def calculate_adx_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算ADX（平均方向指数）信号"""
        try:
            if len(df) < self.params['adx_period']:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            # 计算ADX
            adx = talib.ADX(high, low, close, timeperiod=self.params['adx_period'])
            plus_di = talib.PLUS_DI(high, low, close, timeperiod=self.params['adx_period'])
            minus_di = talib.MINUS_DI(high, low, close, timeperiod=self.params['adx_period'])
            
            signals = {}
            
            # 去除NaN值
            valid_indices = ~(np.isnan(adx) | np.isnan(plus_di) | np.isnan(minus_di))
            if np.sum(valid_indices) < 2:
                return signals
            
            adx_clean = adx[valid_indices]
            plus_di_clean = plus_di[valid_indices]
            minus_di_clean = minus_di[valid_indices]
            
            if len(adx_clean) < 2:
                return signals
            
            current_adx = adx_clean[-1]
            current_plus_di = plus_di_clean[-1]
            current_minus_di = minus_di_clean[-1]
            
            # ADX趋势强度
            if current_adx > 25:
                signals['adx_strong_trend'] = True
            elif current_adx < 20:
                signals['adx_weak_trend'] = True
            
            # 方向判断
            if current_plus_di > current_minus_di:
                signals['adx_bullish'] = True
                signals['adx_direction'] = 1
            else:
                signals['adx_bearish'] = True
                signals['adx_direction'] = -1
            
            # ADX趋势变化
            if len(adx_clean) >= 3:
                if adx_clean[-1] > adx_clean[-2] > adx_clean[-3]:
                    signals['adx_increasing'] = True
                elif adx_clean[-1] < adx_clean[-2] < adx_clean[-3]:
                    signals['adx_decreasing'] = True
            
            signals['adx_value'] = current_adx
            signals['plus_di'] = current_plus_di
            signals['minus_di'] = current_minus_di
            
            return signals
            
        except Exception as e:
            logger.error(f"计算ADX信号失败: {e}")
            return {}
    
    def calculate_volume_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算成交量相关指标"""
        try:
            if len(df) < 20:
                return {}
            
            volume = df['volume'].astype(float)
            close = df['close'].astype(float)
            high = df['high'].astype(float)
            low = df['low'].astype(float)
            
            signals = {}
            
            # 成交量均线
            volume_ma = volume.rolling(self.params['volume_ma_period']).mean()
            current_volume = volume.iloc[-1]
            current_volume_ma = volume_ma.iloc[-1]
            
            # 成交量比率
            volume_ratio = current_volume / current_volume_ma if current_volume_ma > 0 else 1.0
            signals['volume_ratio'] = volume_ratio
            
            # 放量信号
            if volume_ratio > 2.0:
                signals['volume_surge'] = True
            elif volume_ratio > 1.5:
                signals['volume_above_average'] = True
            elif volume_ratio < 0.5:
                signals['volume_below_average'] = True
            
            # OBV（能量潮）
            obv = talib.OBV(close.values, volume.values)
            if len(obv) >= 2:
                obv_trend = obv[-1] - obv[-2]
                if obv_trend > 0:
                    signals['obv_bullish'] = True
                else:
                    signals['obv_bearish'] = True
                
                # OBV趋势
                if len(obv) >= 10:
                    obv_ma = np.mean(obv[-10:])
                    if obv[-1] > obv_ma:
                        signals['obv_above_ma'] = True
                    else:
                        signals['obv_below_ma'] = True
            
            # 价量配合
            price_change = (close.iloc[-1] - close.iloc[-2]) / close.iloc[-2] if len(close) >= 2 else 0
            if price_change > 0 and volume_ratio > 1.5:
                signals['volume_price_positive'] = True
            elif price_change < 0 and volume_ratio > 1.5:
                signals['volume_price_negative'] = True
            
            # 成交量趋势
            if len(volume) >= 5:
                recent_volume_trend = volume.iloc[-5:].pct_change().mean()
                if recent_volume_trend > 0.1:
                    signals['volume_trend_increasing'] = True
                elif recent_volume_trend < -0.1:
                    signals['volume_trend_decreasing'] = True
            
            return signals
            
        except Exception as e:
            logger.error(f"计算成交量指标失败: {e}")
            return {}
    
    def calculate_momentum_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算动量指标"""
        try:
            if len(df) < 30:
                return {}
            
            close = df['close'].astype(float)
            signals = {}
            
            # 价格动量
            for period in [5, 10, 20, 30]:
                momentum = (close.iloc[-1] - close.iloc[-period]) / close.iloc[-period] if len(close) >= period else 0
                signals[f'momentum_{period}d'] = momentum
            
            # 相对强弱
            for period in [5, 10, 20]:
                if len(close) >= period * 2:
                    gains = close.diff().where(close.diff() > 0, 0).rolling(period).mean()
                    losses = -close.diff().where(close.diff() < 0, 0).rolling(period).mean()
                    
                    # 确保数据有效
                    if not gains.empty and not losses.empty:
                        gain_value = gains.iloc[-1]
                        loss_value = losses.iloc[-1]
                        
                        if loss_value != 0:
                            rs = gain_value / loss_value
                            rsi_custom = 100 - (100 / (1 + rs))
                        else:
                            rsi_custom = 50
                        
                        signals[f'rsi_custom_{period}d'] = rsi_custom
            
            # 价格位置
            if len(close) >= 60:
                high_60 = close.rolling(60).max()
                low_60 = close.rolling(60).min()
                current_price = close.iloc[-1]
                
                if high_60.iloc[-1] > 0 and low_60.iloc[-1] > 0:
                    price_position = (current_price - low_60.iloc[-1]) / (high_60.iloc[-1] - low_60.iloc[-1])
                    signals['price_position_60d'] = price_position
                    
                    if price_position > 0.8:
                        signals['price_near_high'] = True
                    elif price_position < 0.2:
                        signals['price_near_low'] = True
            
            return signals
            
        except Exception as e:
            logger.error(f"计算动量指标失败: {e}")
            return {}
    
    def _detect_macd_divergence(self, df: pd.DataFrame, macd: np.ndarray) -> int:
        """检测MACD背离"""
        try:
            if len(df) < 30 or len(macd) < 10:
                return 0
            
            close = df['close'].astype(float).values
            recent_close = close[-10:]
            recent_macd = macd[-10:]
            
            # 价格创新高但MACD未创新高（顶背离）
            if (recent_close[-1] > recent_close[:-1].max() and 
                recent_macd[-1] < recent_macd[:-1].max()):
                return -1
            
            # 价格创新低但MACD未创新低（底背离）
            elif (recent_close[-1] < recent_close[:-1].min() and 
                  recent_macd[-1] > recent_macd[:-1].min()):
                return 1
            
            return 0
            
        except Exception as e:
            logger.error(f"检测MACD背离失败: {e}")
            return 0

    """技术指标计算类"""

    def __init__(self):
        self.settings = get_settings()

        # 技术指标参数配置
        self.params = {
            'ma_periods': [5, 10, 20, 30, 60, 120],  # 均线周期
            'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9,  # MACD参数
            'rsi_period': 14,  # RSI周期
            'kdj_period': 9,   # KDJ周期
            'bollinger_period': 20, 'bollinger_std': 2,  # 布林带参数
            'atr_period': 14,  # ATR周期
            'cci_period': 14,  # CCI周期
            'williams_period': 14,  # 威廉指标周期
            'stoch_period': 14,  # 随机指标周期
            'adx_period': 14,  # ADX周期
            'obv_period': 10,  # OBV周期
            'volume_ma_period': 20,  # 成交量均线周期
        }

    def calculate_ma_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算均线信号"""
        try:
            if len(df) < 30:
                return {}

            close = df['close'].astype(float)

            # 计算多条均线
            ma5 = close.rolling(5).mean()
            ma10 = close.rolling(10).mean()
            ma20 = close.rolling(20).mean()
            ma30 = close.rolling(30).mean()
            ma60 = close.rolling(60).mean()

            current = df.iloc[-1]
            prev = df.iloc[-2]

            signals = {}

            # 均线多头排列
            if (ma5.iloc[-1] > ma10.iloc[-1] > ma20.iloc[-1] > ma30.iloc[-1]):
                signals['ma_bullish'] = True
                signals['ma_strength'] = 1.0
            # 均线空头排列
            elif (ma5.iloc[-1] < ma10.iloc[-1] < ma20.iloc[-1] < ma30.iloc[-1]):
                signals['ma_bullish'] = False
                signals['ma_strength'] = -1.0
            else:
                signals['ma_bullish'] = None
                signals['ma_strength'] = 0.0

            # 金叉死叉信号
            if (ma5.iloc[-1] > ma10.iloc[-1] and ma5.iloc[-2] <= ma10.iloc[-2]):
                signals['golden_cross'] = True
            elif (ma5.iloc[-1] < ma10.iloc[-1] and ma5.iloc[-2] >= ma10.iloc[-2]):
                signals['death_cross'] = True
            else:
                signals['golden_cross'] = False
                signals['death_cross'] = False

            # 价格相对均线位置
            signals['price_vs_ma5'] = current['close'] / ma5.iloc[-1] - 1
            signals['price_vs_ma10'] = current['close'] / ma10.iloc[-1] - 1
            signals['price_vs_ma20'] = current['close'] / ma20.iloc[-1] - 1

            return signals

        except Exception as e:
            logger.error(f"计算均线信号失败: {e}")
            return {}

    def calculate_macd_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算MACD信号"""
        try:
            if len(df) < 50:
                return {}

            close = df['close'].astype(float).values

            # 计算MACD
            macd, signal, hist = talib.MACD(
                close, 
                fastperiod=12, 
                slowperiod=26, 
                signalperiod=9
            )

            signals = {}

            # 去除NaN值
            valid_indices = ~(np.isnan(macd) | np.isnan(signal))
            if np.sum(valid_indices) < 2:
                return signals

            macd_clean = macd[valid_indices]
            signal_clean = signal[valid_indices]
            hist_clean = hist[valid_indices]

            # 金叉死叉
            if (macd_clean[-1] > signal_clean[-1] and 
                macd_clean[-2] <= signal_clean[-2]):
                signals['macd_golden_cross'] = True
                signals['macd_signal'] = 1
            elif (macd_clean[-1] < signal_clean[-1] and 
                  macd_clean[-2] >= signal_clean[-2]):
                signals['macd_death_cross'] = True
                signals['macd_signal'] = -1
            else:
                signals['macd_golden_cross'] = False
                signals['macd_death_cross'] = False
                signals['macd_signal'] = 0

            # MACD柱状图变化
            if len(hist_clean) >= 3:
                signals['macd_hist_trend'] = (
                    1 if hist_clean[-1] > hist_clean[-2] > hist_clean[-3]
                    else -1 if hist_clean[-1] < hist_clean[-2] < hist_clean[-3]
                    else 0
                )

            # MACD背离检测
            signals['macd_divergence'] = self._detect_macd_divergence(df, macd_clean)

            return signals

        except Exception as e:
            logger.error(f"计算MACD信号失败: {e}")
            return {}

    def calculate_rsi_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算RSI信号"""
        try:
            if len(df) < 30:
                return {}

            close = df['close'].astype(float).values
            rsi = talib.RSI(close, timeperiod=14)

            signals = {}

            # 去除NaN值
            valid_rsi = rsi[~np.isnan(rsi)]
            if len(valid_rsi) == 0:
                return signals

            current_rsi = valid_rsi[-1]

            # RSI超买超卖
            if current_rsi > 80:
                signals['rsi_overbought'] = True
                signals['rsi_signal'] = -1
            elif current_rsi < 20:
                signals['rsi_oversold'] = True
                signals['rsi_signal'] = 1
            else:
                signals['rsi_overbought'] = False
                signals['rsi_oversold'] = False
                signals['rsi_signal'] = 0

            # RSI趋势
            if len(valid_rsi) >= 3:
                if valid_rsi[-1] > valid_rsi[-2] > valid_rsi[-3]:
                    signals['rsi_trend'] = 1
                elif valid_rsi[-1] < valid_rsi[-2] < valid_rsi[-3]:
                    signals['rsi_trend'] = -1
                else:
                    signals['rsi_trend'] = 0

            signals['rsi_value'] = current_rsi

            return signals

        except Exception as e:
            logger.error(f"计算RSI信号失败: {e}")
            return {}

    def calculate_volume_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算成交量信号"""
        try:
            if len(df) < 20:
                return {}

            signals = {}

            # 计算成交量均线
            volume = df['volume'].astype(float)
            volume_ma5 = volume.rolling(5).mean()
            volume_ma10 = volume.rolling(10).mean()
            volume_ma20 = volume.rolling(20).mean()

            current = df.iloc[-1]

            # 放量信号
            if volume.iloc[-1] > volume_ma5.iloc[-1] * 2:
                signals['volume_surge'] = True
                signals['volume_ratio'] = volume.iloc[-1] / volume_ma5.iloc[-1]
            else:
                signals['volume_surge'] = False
                signals['volume_ratio'] = volume.iloc[-1] / volume_ma5.iloc[-1]

            # 量价配合
            price_change = current['pctChg']
            if price_change > 0 and signals['volume_surge']:
                signals['volume_price_positive'] = True
            elif price_change < 0 and signals['volume_surge']:
                signals['volume_price_negative'] = True
            else:
                signals['volume_price_positive'] = False
                signals['volume_price_negative'] = False

            # OBV指标
            obv = talib.OBV(df['close'].astype(float).values, 
                           df['volume'].astype(float).values)
            if len(obv) >= 2:
                signals['obv_trend'] = 1 if obv[-1] > obv[-2] else -1

            return signals

        except Exception as e:
            logger.error(f"计算成交量信号失败: {e}")
            return {}

    def calculate_kdj_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算KDJ信号"""
        try:
            if len(df) < 30:
                return {}

            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values

            k, d = talib.STOCH(high, low, close, 
                              fastk_period=9, 
                              slowk_period=3, 
                              slowk_matype=0,
                              slowd_period=3, 
                              slowd_matype=0)

            signals = {}

            # 去除NaN值
            valid_indices = ~(np.isnan(k) | np.isnan(d))
            if np.sum(valid_indices) < 2:
                return signals

            k_clean = k[valid_indices]
            d_clean = d[valid_indices]

            if len(k_clean) < 2:
                return signals

            # 计算J值
            j = 3 * k_clean - 2 * d_clean

            current_k = k_clean[-1]
            current_d = d_clean[-1]
            current_j = j[-1]

            # KDJ金叉死叉
            if (current_k > current_d and k_clean[-2] <= d_clean[-2]):
                signals['kdj_golden_cross'] = True
                signals['kdj_signal'] = 1
            elif (current_k < current_d and k_clean[-2] >= d_clean[-2]):
                signals['kdj_death_cross'] = True
                signals['kdj_signal'] = -1
            else:
                signals['kdj_golden_cross'] = False
                signals['kdj_death_cross'] = False
                signals['kdj_signal'] = 0

            # 超买超卖
            if current_k > 80 and current_d > 80:
                signals['kdj_overbought'] = True
            elif current_k < 20 and current_d < 20:
                signals['kdj_oversold'] = True
            else:
                signals['kdj_overbought'] = False
                signals['kdj_oversold'] = False

            signals['k_value'] = current_k
            signals['d_value'] = current_d
            signals['j_value'] = current_j

            return signals

        except Exception as e:
            logger.error(f"计算KDJ信号失败: {e}")
            return {}

    def calculate_support_resistance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算支撑阻力位"""
        try:
            if len(df) < 60:
                return {}

            high = df['high'].astype(float)
            low = df['low'].astype(float)
            close = df['close'].astype(float)
            current_price = close.iloc[-1]

            signals = {}

            # 计算近期高低点
            recent_high = high.tail(20).max()
            recent_low = low.tail(20).min()

            # 支撑位和阻力位
            resistance_levels = []
            support_levels = []

            # 寻找阻力位（近期高点附近）
            for i in range(len(high) - 20, len(high)):
                if high.iloc[i] >= recent_high * 0.98:
                    resistance_levels.append(high.iloc[i])

            # 寻找支撑位（近期低点附近）
            for i in range(len(low) - 20, len(low)):
                if low.iloc[i] <= recent_low * 1.02:
                    support_levels.append(low.iloc[i])

            # 去重并排序
            resistance_levels = sorted(list(set(resistance_levels)), reverse=True)
            support_levels = sorted(list(set(support_levels)))

            signals['nearest_resistance'] = resistance_levels[0] if resistance_levels else None
            signals['nearest_support'] = support_levels[0] if support_levels else None

            # 价格位置判断
            if signals['nearest_resistance'] and signals['nearest_support']:
                resistance_distance = (signals['nearest_resistance'] - current_price) / current_price
                support_distance = (current_price - signals['nearest_support']) / current_price

                if resistance_distance < 0.05:  # 接近阻力位
                    signals['near_resistance'] = True
                elif support_distance < 0.05:  # 接近支撑位
                    signals['near_support'] = True
                else:
                    signals['near_resistance'] = False
                    signals['near_support'] = False

            return signals

        except Exception as e:
            logger.error(f"计算支撑阻力位失败: {e}")
            return {}

    def calculate_bollinger_bands(self, df: pd.DataFrame) -> Dict[str, Any]:
        """增强版布林带信号计算 - 加入有效突破判断和形态识别"""
        try:
            if len(df) < self.params['bollinger_period']:
                return {}

            close = df['close'].astype(float).values
            volume = df['volume'].astype(float).values
            period = self.params['bollinger_period']
            std_dev = self.params['bollinger_std']

            # 计算布林带
            upper, middle, lower = talib.BBANDS(
                close,
                timeperiod=period,
                nbdevup=std_dev,
                nbdevdn=std_dev,
                matype=0
            )

            signals = {}

            # 去除NaN值
            valid_indices = ~(np.isnan(upper) | np.isnan(middle) | np.isnan(lower))
            if np.sum(valid_indices) < 2:
                return signals

            upper_clean = upper[valid_indices]
            middle_clean = middle[valid_indices]
            lower_clean = lower[valid_indices]
            close_clean = close[valid_indices] if len(close) == len(upper) else close[-len(upper_clean):]
            volume_clean = volume[valid_indices] if len(volume) == len(upper) else volume[-len(upper_clean):]

            if len(upper_clean) < 3:
                return signals

            current_price = close_clean[-1]
            prev_price = close_clean[-2]
            current_upper = upper_clean[-1]
            current_middle = middle_clean[-1]
            current_lower = lower_clean[-1]

            # 增强版突破判断 - 需要成交量确认和持续性
            volume_ma = np.mean(volume_clean[-10:]) if len(volume_clean) >= 10 else np.mean(volume_clean)
            volume_confirmation = volume_clean[-1] > volume_ma * self.enhanced_params['volume_confirmation_ratio']

            # 检测有效突破
            confirmation_periods = self.enhanced_params['breakout_confirmation_periods']

            # 上轨突破
            if current_price >= current_upper:
                # 检查突破的有效性
                breakout_strength = (current_price - current_upper) / current_upper
                sustained_breakout = all(close_clean[i] >= upper_clean[i] * 0.995
                                       for i in range(-confirmation_periods, 0)
                                       if i >= -len(close_clean))

                if breakout_strength > self.enhanced_params['noise_filter_threshold'] and volume_confirmation:
                    signals['bb_upper_breakout'] = True
                    signals['bb_breakout_strength'] = min(breakout_strength * 100, 1.0)
                    signals['bb_breakout_confirmed'] = sustained_breakout
                else:
                    signals['bb_upper_breakout'] = False
                    signals['bb_breakout_strength'] = 0.3  # 弱突破
                    signals['bb_breakout_confirmed'] = False
                signals['bb_position'] = 'above_upper'

            # 下轨突破
            elif current_price <= current_lower:
                breakout_strength = (current_lower - current_price) / current_lower
                sustained_breakout = all(close_clean[i] <= lower_clean[i] * 1.005
                                       for i in range(-confirmation_periods, 0)
                                       if i >= -len(close_clean))

                if breakout_strength > self.enhanced_params['noise_filter_threshold'] and volume_confirmation:
                    signals['bb_lower_breakout'] = True
                    signals['bb_breakout_strength'] = min(breakout_strength * 100, 1.0)
                    signals['bb_breakout_confirmed'] = sustained_breakout
                else:
                    signals['bb_lower_breakout'] = False
                    signals['bb_breakout_strength'] = 0.3
                    signals['bb_breakout_confirmed'] = False
                signals['bb_position'] = 'below_lower'

            elif current_price > current_middle:
                signals['bb_position'] = 'above_middle'
                signals['bb_upper_breakout'] = False
                signals['bb_lower_breakout'] = False
            else:
                signals['bb_position'] = 'below_middle'
                signals['bb_upper_breakout'] = False
                signals['bb_lower_breakout'] = False

            # 布林带宽度分析（波动率指标）
            bb_width = (current_upper - current_lower) / current_middle
            signals['bb_width'] = bb_width

            # 增强版收缩/扩张检测
            if len(upper_clean) >= 10:
                # 计算历史宽度分位数
                historical_widths = [(upper_clean[i] - lower_clean[i]) / middle_clean[i]
                                   for i in range(-10, 0)]
                width_percentile = np.percentile(historical_widths, 20)

                if bb_width < width_percentile:
                    signals['bb_squeeze'] = True  # 极度收缩
                    signals['bb_squeeze_strength'] = 1.0
                elif bb_width < np.mean(historical_widths) * 0.8:
                    signals['bb_squeeze'] = True  # 一般收缩
                    signals['bb_squeeze_strength'] = 0.7
                else:
                    signals['bb_squeeze'] = False
                    signals['bb_squeeze_strength'] = 0.0

                # 扩张检测
                if bb_width > np.percentile(historical_widths, 80):
                    signals['bb_expansion'] = True
                    signals['bb_expansion_strength'] = 1.0
                else:
                    signals['bb_expansion'] = False
                    signals['bb_expansion_strength'] = 0.0

            # 布林带%B指标
            bb_percent = (current_price - current_lower) / (current_upper - current_lower) if current_upper != current_lower else 0.5
            signals['bb_percent'] = bb_percent

            # 基于%B的信号
            if bb_percent > 0.8:
                signals['bb_percent_overbought'] = True
            elif bb_percent < 0.2:
                signals['bb_percent_oversold'] = True
            else:
                signals['bb_percent_overbought'] = False
                signals['bb_percent_oversold'] = False

            return signals

        except Exception as e:
            logger.error(f"计算增强版布林带信号失败: {e}")
            return {}

    def calculate_atr_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算ATR（平均真实波幅）信号"""
        try:
            if len(df) < self.params['atr_period']:
                return {}

            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values

            # 计算ATR
            atr = talib.ATR(high, low, close, timeperiod=self.params['atr_period'])

            signals = {}

            # 去除NaN值
            valid_atr = atr[~np.isnan(atr)]
            if len(valid_atr) < 2:
                return signals

            current_atr = valid_atr[-1]
            prev_atr = valid_atr[-2]

            # ATR变化趋势
            if current_atr > prev_atr:
                signals['atr_increasing'] = True
                signals['atr_trend'] = 1
            elif current_atr < prev_atr:
                signals['atr_decreasing'] = True
                signals['atr_trend'] = -1
            else:
                signals['atr_trend'] = 0

            # ATR相对水平
            atr_ma = np.mean(valid_atr[-20:]) if len(valid_atr) >= 20 else np.mean(valid_atr)
            if current_atr > atr_ma * 1.5:
                signals['atr_high_volatility'] = True
            elif current_atr < atr_ma * 0.5:
                signals['atr_low_volatility'] = True

            signals['atr_value'] = current_atr
            signals['atr_ratio'] = current_atr / atr_ma if atr_ma > 0 else 1.0

            return signals

        except Exception as e:
            logger.error(f"计算ATR信号失败: {e}")
            return {}

    def calculate_cci_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算CCI（商品通道指数）信号"""
        try:
            if len(df) < self.params['cci_period']:
                return {}

            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values

            # 计算CCI
            cci = talib.CCI(high, low, close, timeperiod=self.params['cci_period'])

            signals = {}

            # 去除NaN值
            valid_cci = cci[~np.isnan(cci)]
            if len(valid_cci) < 2:
                return signals

            current_cci = valid_cci[-1]
            prev_cci = valid_cci[-2]

            # CCI超买超卖
            if current_cci > 100:
                signals['cci_overbought'] = True
                signals['cci_signal'] = -1
            elif current_cci < -100:
                signals['cci_oversold'] = True
                signals['cci_signal'] = 1
            else:
                signals['cci_signal'] = 0

            # CCI趋势
            if current_cci > prev_cci:
                signals['cci_trend'] = 1
            elif current_cci < prev_cci:
                signals['cci_trend'] = -1
            else:
                signals['cci_trend'] = 0

            # CCI背离检测
            if len(valid_cci) >= 10:
                recent_highs = []
                recent_lows = []
                price_highs = []
                price_lows = []

                # 确保close数组长度匹配
                close_array = close[-len(valid_cci):]

                for i in range(len(valid_cci) - 10, len(valid_cci)):
                    if i > 0 and i < len(valid_cci) - 1:
                        if valid_cci[i] > valid_cci[i-1] and valid_cci[i] > valid_cci[i+1]:
                            recent_highs.append(valid_cci[i])
                            price_highs.append(close_array[i])
                        elif valid_cci[i] < valid_cci[i-1] and valid_cci[i] < valid_cci[i+1]:
                            recent_lows.append(valid_cci[i])
                            price_lows.append(close_array[i])

                if len(recent_highs) >= 2 and len(price_highs) >= 2:
                    if recent_highs[-1] < recent_highs[-2] and price_highs[-1] > price_highs[-2]:
                        signals['cci_bearish_divergence'] = True

                if len(recent_lows) >= 2 and len(price_lows) >= 2:
                    if recent_lows[-1] > recent_lows[-2] and price_lows[-1] < price_lows[-2]:
                        signals['cci_bullish_divergence'] = True

            signals['cci_value'] = current_cci

            return signals

        except Exception as e:
            logger.error(f"计算CCI信号失败: {e}")
            return {}

    def calculate_williams_r(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算威廉指标信号"""
        try:
            if len(df) < self.params['williams_period']:
                return {}

            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values

            # 计算威廉指标
            williams_r = talib.WILLR(high, low, close, timeperiod=self.params['williams_period'])

            signals = {}

            # 去除NaN值
            valid_wr = williams_r[~np.isnan(williams_r)]
            if len(valid_wr) < 2:
                return signals

            current_wr = valid_wr[-1]
            prev_wr = valid_wr[-2]

            # 威廉指标超买超卖
            if current_wr > -20:
                signals['williams_overbought'] = True
                signals['williams_signal'] = -1
            elif current_wr < -80:
                signals['williams_oversold'] = True
                signals['williams_signal'] = 1
            else:
                signals['williams_signal'] = 0

            # 威廉指标趋势
            if current_wr > prev_wr:
                signals['williams_trend'] = 1
            elif current_wr < prev_wr:
                signals['williams_trend'] = -1
            else:
                signals['williams_trend'] = 0

            signals['williams_value'] = current_wr

            return signals

        except Exception as e:
            logger.error(f"计算威廉指标失败: {e}")
            return {}

    def calculate_stochastic_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算随机指标信号"""
        try:
            if len(df) < self.params['stoch_period']:
                return {}

            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values

            # 计算随机指标
            slowk, slowd = talib.STOCH(
                high, low, close,
                fastk_period=self.params['stoch_period'],
                slowk_period=3,
                slowk_matype=0,
                slowd_period=3,
                slowd_matype=0
            )

            signals = {}

            # 去除NaN值
            valid_indices = ~(np.isnan(slowk) | np.isnan(slowd))
            if np.sum(valid_indices) < 2:
                return signals

            slowk_clean = slowk[valid_indices]
            slowd_clean = slowd[valid_indices]

            if len(slowk_clean) < 2:
                return signals

            current_k = slowk_clean[-1]
            current_d = slowd_clean[-1]
            prev_k = slowk_clean[-2]
            prev_d = slowd_clean[-2]

            # 随机指标超买超卖
            if current_k > 80 and current_d > 80:
                signals['stoch_overbought'] = True
                signals['stoch_signal'] = -1
            elif current_k < 20 and current_d < 20:
                signals['stoch_oversold'] = True
                signals['stoch_signal'] = 1
            else:
                signals['stoch_signal'] = 0

            # 随机指标金叉死叉
            if current_k > current_d and prev_k <= prev_d:
                signals['stoch_golden_cross'] = True
            elif current_k < current_d and prev_k >= prev_d:
                signals['stoch_death_cross'] = True

            signals['stoch_k'] = current_k
            signals['stoch_d'] = current_d

            return signals

        except Exception as e:
            logger.error(f"计算随机指标失败: {e}")
            return {}

    def calculate_adx_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """增强版ADX信号计算 - 改进趋势强度判别和方向确认"""
        try:
            if len(df) < self.params['adx_period']:
                return {}

            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            volume = df['volume'].astype(float).values

            # 计算ADX
            adx = talib.ADX(high, low, close, timeperiod=self.params['adx_period'])
            plus_di = talib.PLUS_DI(high, low, close, timeperiod=self.params['adx_period'])
            minus_di = talib.MINUS_DI(high, low, close, timeperiod=self.params['adx_period'])

            signals = {}

            # 去除NaN值
            valid_indices = ~(np.isnan(adx) | np.isnan(plus_di) | np.isnan(minus_di))
            if np.sum(valid_indices) < 2:
                return signals

            adx_clean = adx[valid_indices]
            plus_di_clean = plus_di[valid_indices]
            minus_di_clean = minus_di[valid_indices]
            volume_clean = volume[valid_indices] if len(volume) == len(adx) else volume[-len(adx_clean):]

            if len(adx_clean) < 3:
                return signals

            current_adx = adx_clean[-1]
            prev_adx = adx_clean[-2]
            current_plus_di = plus_di_clean[-1]
            current_minus_di = minus_di_clean[-1]

            # 增强版ADX趋势强度分级
            if current_adx > 40:
                signals['adx_trend_strength'] = 'very_strong'
                signals['adx_strength_score'] = 1.0
            elif current_adx > 30:
                signals['adx_trend_strength'] = 'strong'
                signals['adx_strength_score'] = 0.8
            elif current_adx > 25:
                signals['adx_trend_strength'] = 'moderate'
                signals['adx_strength_score'] = 0.6
            elif current_adx > 20:
                signals['adx_trend_strength'] = 'weak'
                signals['adx_strength_score'] = 0.4
            else:
                signals['adx_trend_strength'] = 'very_weak'
                signals['adx_strength_score'] = 0.2

            # 传统强弱判断
            signals['adx_strong_trend'] = current_adx > 25
            signals['adx_weak_trend'] = current_adx < 20

            # 增强版方向判断 - 加入DI差值分析
            di_difference = abs(current_plus_di - current_minus_di)
            di_separation_threshold = 5  # DI线分离度阈值

            # 成交量确认
            volume_ma = np.mean(volume_clean[-10:]) if len(volume_clean) >= 10 else np.mean(volume_clean)
            volume_confirmation = volume_clean[-1] > volume_ma * self.enhanced_params['volume_confirmation_ratio']

            if current_plus_di > current_minus_di:
                signals['adx_bullish'] = True
                signals['adx_bearish'] = False
                signals['adx_direction'] = 1
                # 判断信号强度
                if di_difference > di_separation_threshold and volume_confirmation:
                    signals['adx_direction_strength'] = 1.0
                elif di_difference > di_separation_threshold:
                    signals['adx_direction_strength'] = 0.7
                else:
                    signals['adx_direction_strength'] = 0.4
            else:
                signals['adx_bullish'] = False
                signals['adx_bearish'] = True
                signals['adx_direction'] = -1
                if di_difference > di_separation_threshold and volume_confirmation:
                    signals['adx_direction_strength'] = 1.0
                elif di_difference > di_separation_threshold:
                    signals['adx_direction_strength'] = 0.7
                else:
                    signals['adx_direction_strength'] = 0.4

            # 增强版ADX趋势变化分析
            if len(adx_clean) >= 5:
                # 检测ADX连续上升（趋势加强）
                consecutive_rising = all(adx_clean[i] > adx_clean[i-1] for i in range(-3, 0))
                consecutive_falling = all(adx_clean[i] < adx_clean[i-1] for i in range(-3, 0))

                if consecutive_rising and current_adx > 25:
                    signals['adx_strengthening'] = True
                    signals['adx_trend_momentum'] = 1.0
                elif consecutive_falling and current_adx < 30:
                    signals['adx_weakening'] = True
                    signals['adx_trend_momentum'] = -1.0
                else:
                    signals['adx_strengthening'] = False
                    signals['adx_weakening'] = False
                    signals['adx_trend_momentum'] = 0.0

            # DI线交叉检测
            if len(plus_di_clean) >= 2 and len(minus_di_clean) >= 2:
                prev_plus_di = plus_di_clean[-2]
                prev_minus_di = minus_di_clean[-2]

                # +DI上穿-DI（多头信号）
                if (current_plus_di > current_minus_di and
                    prev_plus_di <= prev_minus_di and
                    current_adx > 20):
                    signals['di_bullish_cross'] = True
                    signals['di_cross_strength'] = min(current_adx / 25, 1.0)
                # -DI上穿+DI（空头信号）
                elif (current_minus_di > current_plus_di and
                      prev_minus_di <= prev_plus_di and
                      current_adx > 20):
                    signals['di_bearish_cross'] = True
                    signals['di_cross_strength'] = min(current_adx / 25, 1.0)
                else:
                    signals['di_bullish_cross'] = False
                    signals['di_bearish_cross'] = False
                    signals['di_cross_strength'] = 0.0

            # ADX极值检测（可能的趋势转折点）
            if len(adx_clean) >= 10:
                recent_adx_max = np.max(adx_clean[-10:])
                if current_adx >= recent_adx_max and current_adx > 30:
                    signals['adx_peak'] = True  # ADX达到近期高点
                else:
                    signals['adx_peak'] = False

            signals['adx_value'] = current_adx
            signals['plus_di'] = current_plus_di
            signals['minus_di'] = current_minus_di
            signals['di_difference'] = di_difference

            return signals

        except Exception as e:
            logger.error(f"计算增强版ADX信号失败: {e}")
            return {}

    def calculate_volume_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算成交量相关指标"""
        try:
            if len(df) < 20:
                return {}

            volume = df['volume'].astype(float)
            close = df['close'].astype(float)
            high = df['high'].astype(float)
            low = df['low'].astype(float)

            signals = {}

            # 成交量均线
            volume_ma = volume.rolling(self.params['volume_ma_period']).mean()
            current_volume = volume.iloc[-1]
            current_volume_ma = volume_ma.iloc[-1]

            # 成交量比率
            volume_ratio = current_volume / current_volume_ma if current_volume_ma > 0 else 1.0
            signals['volume_ratio'] = volume_ratio

            # 放量信号
            if volume_ratio > 2.0:
                signals['volume_surge'] = True
            elif volume_ratio > 1.5:
                signals['volume_above_average'] = True
            elif volume_ratio < 0.5:
                signals['volume_below_average'] = True

            # OBV（能量潮）
            obv = talib.OBV(close.values, volume.values)
            if len(obv) >= 2:
                obv_trend = obv[-1] - obv[-2]
                if obv_trend > 0:
                    signals['obv_bullish'] = True
                else:
                    signals['obv_bearish'] = True

                # OBV趋势
                if len(obv) >= 10:
                    obv_ma = np.mean(obv[-10:])
                    if obv[-1] > obv_ma:
                        signals['obv_above_ma'] = True
                    else:
                        signals['obv_below_ma'] = True

            # 价量配合
            price_change = (close.iloc[-1] - close.iloc[-2]) / close.iloc[-2] if len(close) >= 2 else 0
            if price_change > 0 and volume_ratio > 1.5:
                signals['volume_price_positive'] = True
            elif price_change < 0 and volume_ratio > 1.5:
                signals['volume_price_negative'] = True

            # 成交量趋势
            if len(volume) >= 5:
                recent_volume_trend = volume.iloc[-5:].pct_change().mean()
                if recent_volume_trend > 0.1:
                    signals['volume_trend_increasing'] = True
                elif recent_volume_trend < -0.1:
                    signals['volume_trend_decreasing'] = True

            return signals

        except Exception as e:
            logger.error(f"计算成交量指标失败: {e}")
            return {}

    def calculate_momentum_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算动量指标"""
        try:
            if len(df) < 30:
                return {}

            close = df['close'].astype(float)
            signals = {}

            # 价格动量
            for period in [5, 10, 20, 30]:
                momentum = (close.iloc[-1] - close.iloc[-period]) / close.iloc[-period] if len(close) >= period else 0
                signals[f'momentum_{period}d'] = momentum

            # 相对强弱
            for period in [5, 10, 20]:
                if len(close) >= period * 2:
                    gains = close.diff().where(close.diff() > 0, 0).rolling(period).mean()
                    losses = -close.diff().where(close.diff() < 0, 0).rolling(period).mean()

                    # 确保数据有效
                    if not gains.empty and not losses.empty:
                        gain_value = gains.iloc[-1]
                        loss_value = losses.iloc[-1]

                        if loss_value != 0:
                            rs = gain_value / loss_value
                            rsi_custom = 100 - (100 / (1 + rs))
                        else:
                            rsi_custom = 50

                        signals[f'rsi_custom_{period}d'] = rsi_custom

            # 价格位置
            if len(close) >= 60:
                high_60 = close.rolling(60).max()
                low_60 = close.rolling(60).min()
                current_price = close.iloc[-1]

                if high_60.iloc[-1] > 0 and low_60.iloc[-1] > 0:
                    price_position = (current_price - low_60.iloc[-1]) / (high_60.iloc[-1] - low_60.iloc[-1])
                    signals['price_position_60d'] = price_position

                    if price_position > 0.8:
                        signals['price_near_high'] = True
                    elif price_position < 0.2:
                        signals['price_near_low'] = True

            return signals

        except Exception as e:
            logger.error(f"计算动量指标失败: {e}")
            return {}

    def _detect_macd_divergence(self, df: pd.DataFrame, macd: np.ndarray) -> int:
        """检测MACD背离"""
        try:
            if len(df) < 30 or len(macd) < 10:
                return 0

            close = df['close'].astype(float).values
            recent_close = close[-10:]
            recent_macd = macd[-10:]

            # 价格创新高但MACD未创新高（顶背离）
            if (recent_close[-1] > recent_close[:-1].max() and 
                recent_macd[-1] < recent_macd[:-1].max()):
                return -1

            # 价格创新低但MACD未创新低（底背离）
            elif (recent_close[-1] < recent_close[:-1].min() and 
                  recent_macd[-1] > recent_macd[:-1].min()):
                return 1

            return 0

        except Exception as e:
            logger.error(f"检测MACD背离失败: {e}")
            return 0

    def _calculate_ma_trend_strength(self, ma5, ma10, ma20, ma30):
        """计算均线趋势强度"""
        try:
            # 计算各均线间的相对距离
            ma_distances = [
                abs(ma5.iloc[-1] - ma10.iloc[-1]) / ma10.iloc[-1],
                abs(ma10.iloc[-1] - ma20.iloc[-1]) / ma20.iloc[-1],
                abs(ma20.iloc[-1] - ma30.iloc[-1]) / ma30.iloc[-1]
            ]
            return np.mean(ma_distances)
        except:
            return 0.0

    def _detect_multi_timeframe_resonance(self, df, ma5, ma10, ma20):
        """检测多周期共振"""
        try:
            signals = {}
            if len(df) < 60:
                return signals

            # 计算不同周期的趋势方向
            short_trend = 1 if ma5.iloc[-1] > ma10.iloc[-1] else -1
            medium_trend = 1 if ma10.iloc[-1] > ma20.iloc[-1] else -1

            # 计算长周期趋势（使用更长的均线）
            if len(df) >= 60:
                ma60 = df['close'].rolling(60).mean()
                long_trend = 1 if ma20.iloc[-1] > ma60.iloc[-1] else -1

                # 三个周期同向为强共振
                if short_trend == medium_trend == long_trend:
                    signals['multi_timeframe_resonance'] = True
                    signals['resonance_strength'] = 1.0
                elif short_trend == medium_trend or medium_trend == long_trend:
                    signals['multi_timeframe_resonance'] = True
                    signals['resonance_strength'] = 0.7
                else:
                    signals['multi_timeframe_resonance'] = False
                    signals['resonance_strength'] = 0.0

            return signals
        except:
            return {}

    def _detect_enhanced_macd_divergence(self, df: pd.DataFrame, macd: np.ndarray) -> int:
        """增强版MACD背离检测 - 更严格的条件"""
        try:
            lookback = self.enhanced_params['divergence_lookback']
            if len(df) < lookback or len(macd) < lookback:
                return 0

            close = df['close'].astype(float).values[-lookback:]
            recent_macd = macd[-lookback:]

            # 寻找价格和MACD的局部极值点
            price_peaks = []
            price_troughs = []
            macd_peaks = []
            macd_troughs = []

            for i in range(2, len(close) - 2):
                # 价格峰值
                if close[i] > close[i-1] and close[i] > close[i+1] and close[i] > close[i-2] and close[i] > close[i+2]:
                    price_peaks.append((i, close[i]))
                # 价格谷值
                if close[i] < close[i-1] and close[i] < close[i+1] and close[i] < close[i-2] and close[i] < close[i+2]:
                    price_troughs.append((i, close[i]))

                # MACD峰值
                if recent_macd[i] > recent_macd[i-1] and recent_macd[i] > recent_macd[i+1]:
                    macd_peaks.append((i, recent_macd[i]))
                # MACD谷值
                if recent_macd[i] < recent_macd[i-1] and recent_macd[i] < recent_macd[i+1]:
                    macd_troughs.append((i, recent_macd[i]))

            # 检测顶背离（价格新高，MACD未新高）
            if len(price_peaks) >= 2 and len(macd_peaks) >= 2:
                latest_price_peak = max(price_peaks, key=lambda x: x[0])
                latest_macd_peak = max(macd_peaks, key=lambda x: x[0])

                # 找到倒数第二个峰值
                prev_price_peaks = [p for p in price_peaks if p[0] < latest_price_peak[0]]
                prev_macd_peaks = [p for p in macd_peaks if p[0] < latest_macd_peak[0]]

                if prev_price_peaks and prev_macd_peaks:
                    prev_price_peak = max(prev_price_peaks, key=lambda x: x[1])
                    prev_macd_peak = max(prev_macd_peaks, key=lambda x: x[1])

                    # 价格创新高但MACD未创新高
                    if (latest_price_peak[1] > prev_price_peak[1] and
                        latest_macd_peak[1] < prev_macd_peak[1]):
                        return -1

            # 检测底背离（价格新低，MACD未新低）
            if len(price_troughs) >= 2 and len(macd_troughs) >= 2:
                latest_price_trough = min(price_troughs, key=lambda x: x[1])
                latest_macd_trough = min(macd_troughs, key=lambda x: x[1])

                prev_price_troughs = [p for p in price_troughs if p[0] < latest_price_trough[0]]
                prev_macd_troughs = [p for p in macd_troughs if p[0] < latest_macd_trough[0]]

                if prev_price_troughs and prev_macd_troughs:
                    prev_price_trough = min(prev_price_troughs, key=lambda x: x[1])
                    prev_macd_trough = min(prev_macd_troughs, key=lambda x: x[1])

                    # 价格创新低但MACD未创新低
                    if (latest_price_trough[1] < prev_price_trough[1] and
                        latest_macd_trough[1] > prev_macd_trough[1]):
                        return 1

            return 0

        except Exception as e:
            logger.error(f"检测增强版MACD背离失败: {e}")
            return 0

    def _detect_macd_zero_line_cross(self, macd: np.ndarray, volume: np.ndarray):
        """检测MACD零轴突破"""
        try:
            signals = {}
            if len(macd) < 3:
                return signals

            # 零轴上穿
            if macd[-1] > 0 and macd[-2] <= 0:
                volume_ma = np.mean(volume[-10:]) if len(volume) >= 10 else np.mean(volume)
                volume_confirmation = volume[-1] > volume_ma * 1.2
                signals['macd_zero_cross_up'] = True
                signals['macd_zero_cross_confirmed'] = volume_confirmation
            # 零轴下穿
            elif macd[-1] < 0 and macd[-2] >= 0:
                volume_ma = np.mean(volume[-10:]) if len(volume) >= 10 else np.mean(volume)
                volume_confirmation = volume[-1] > volume_ma * 1.2
                signals['macd_zero_cross_down'] = True
                signals['macd_zero_cross_confirmed'] = volume_confirmation
            else:
                signals['macd_zero_cross_up'] = False
                signals['macd_zero_cross_down'] = False
                signals['macd_zero_cross_confirmed'] = False

            return signals
        except:
            return {}

    def _detect_rsi_divergence(self, df: pd.DataFrame, rsi: np.ndarray) -> int:
        """检测RSI背离"""
        try:
            lookback = min(self.enhanced_params['divergence_lookback'], len(df))
            if lookback < 10 or len(rsi) < 10:
                return 0

            close = df['close'].astype(float).values[-lookback:]
            recent_rsi = rsi[-lookback:]

            # 寻找价格和RSI的局部极值点
            price_peaks = []
            price_troughs = []
            rsi_peaks = []
            rsi_troughs = []

            for i in range(2, len(close) - 2):
                # 价格峰值
                if close[i] > close[i-1] and close[i] > close[i+1] and close[i] > close[i-2] and close[i] > close[i+2]:
                    price_peaks.append((i, close[i]))
                # 价格谷值
                if close[i] < close[i-1] and close[i] < close[i+1] and close[i] < close[i-2] and close[i] < close[i+2]:
                    price_troughs.append((i, close[i]))

                # RSI峰值
                if recent_rsi[i] > recent_rsi[i-1] and recent_rsi[i] > recent_rsi[i+1]:
                    rsi_peaks.append((i, recent_rsi[i]))
                # RSI谷值
                if recent_rsi[i] < recent_rsi[i-1] and recent_rsi[i] < recent_rsi[i+1]:
                    rsi_troughs.append((i, recent_rsi[i]))

            # 检测顶背离（价格新高，RSI未新高）
            if len(price_peaks) >= 2 and len(rsi_peaks) >= 2:
                latest_price_peak = max(price_peaks, key=lambda x: x[0])
                latest_rsi_peak = max(rsi_peaks, key=lambda x: x[0])

                prev_price_peaks = [p for p in price_peaks if p[0] < latest_price_peak[0]]
                prev_rsi_peaks = [p for p in rsi_peaks if p[0] < latest_rsi_peak[0]]

                if prev_price_peaks and prev_rsi_peaks:
                    prev_price_peak = max(prev_price_peaks, key=lambda x: x[1])
                    prev_rsi_peak = max(prev_rsi_peaks, key=lambda x: x[1])

                    if (latest_price_peak[1] > prev_price_peak[1] and
                        latest_rsi_peak[1] < prev_rsi_peak[1]):
                        return -1

            # 检测底背离（价格新低，RSI未新低）
            if len(price_troughs) >= 2 and len(rsi_troughs) >= 2:
                latest_price_trough = min(price_troughs, key=lambda x: x[1])
                latest_rsi_trough = min(rsi_troughs, key=lambda x: x[1])

                prev_price_troughs = [p for p in price_troughs if p[0] < latest_price_trough[0]]
                prev_rsi_troughs = [p for p in rsi_troughs if p[0] < latest_rsi_trough[0]]

                if prev_price_troughs and prev_rsi_troughs:
                    prev_price_trough = min(prev_price_troughs, key=lambda x: x[1])
                    prev_rsi_trough = min(prev_rsi_troughs, key=lambda x: x[1])

                    if (latest_price_trough[1] < prev_price_trough[1] and
                        latest_rsi_trough[1] > prev_rsi_trough[1]):
                        return 1

            return 0

        except Exception as e:
            logger.error(f"检测RSI背离失败: {e}")
            return 0