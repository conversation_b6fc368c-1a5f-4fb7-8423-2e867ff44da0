"""
股票技术分析器

提供全面的股票技术分析功能，包括：
- 技术指标计算（MACD、RSI、布林带等）
- 形态识别（三连阳、底部反转等）
- 动量分析（量价齐升、趋势判断）
- 综合评分系统

Author: Stock Analysis Team
Version: 2.0
"""

import numpy as np
import pandas as pd
from scipy import stats
import talib as ta
import statsmodels.api as sm
from typing import Dict, Any, Optional, Tuple, Union
import warnings
from dataclasses import dataclass, field

# 尝试导入数据库相关模块
try:
    from quant.db.fetch import fetch_quant
except (ImportError, ValueError) as e:
    warnings.warn(f"无法导入fetch_quant函数: {e}. 将使用默认决策日计算逻辑。")
    fetch_quant = None

# 配置警告过滤
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

# ==================== 工具函数 ====================

def safe_float(value: Any, default: float = 0.0) -> float:
    """安全转换为浮点数

    处理各种异常值：NaN、None、空字符串、'-'等

    Args:
        value: 待转换的值
        default: 转换失败时的默认值

    Returns:
        float: 转换后的浮点数
    """
    if value is None or value == '-' or value == '' or pd.isna(value):
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_divide(numerator: Union[int, float], denominator: Union[int, float],
                default: float = 0.0) -> float:
    """安全除法运算

    避免除零错误和NaN值传播

    Args:
        numerator: 分子
        denominator: 分母
        default: 除法失败时的默认值

    Returns:
        float: 除法结果
    """
    if denominator == 0 or pd.isna(denominator) or pd.isna(numerator):
        return default
    try:
        return float(numerator) / float(denominator)
    except (ValueError, TypeError, ZeroDivisionError):
        return default


def validate_data_length(data: Optional[pd.DataFrame], min_length: int,
                        operation_name: str = "操作") -> bool:
    """验证数据长度是否满足要求

    Args:
        data: 待验证的数据
        min_length: 最小长度要求
        operation_name: 操作名称，用于错误提示

    Returns:
        bool: 是否满足长度要求
    """
    if data is None or len(data) < min_length:
        current_length = len(data) if data is not None else 0
        warnings.warn(f"{operation_name}需要至少{min_length}条数据，当前只有{current_length}条")
        return False
    return True


def clean_numeric_series(series: pd.Series, force_float64: bool = False) -> pd.Series:
    """清理数值序列

    移除异常值、无穷大值，转换为标准数值格式

    Args:
        series: 待清理的序列
        force_float64: 是否强制转换为 float64 类型（用于 talib 兼容性）

    Returns:
        pd.Series: 清理后的数值序列
    """
    if series is None or len(series) == 0:
        return pd.Series(dtype=float)

    # 转换为数值类型，无法转换的设为NaN
    numeric_series = pd.to_numeric(series, errors='coerce')

    # 移除无穷大值
    numeric_series = numeric_series.replace([np.inf, -np.inf], np.nan)

    # 如果需要强制转换为 float64（用于 talib 兼容性）
    if force_float64:
        numeric_series = numeric_series.astype(np.float64)

    return numeric_series


def ensure_talib_compatible(data: Union[pd.Series, np.ndarray]) -> np.ndarray:
    """确保数据与 talib 兼容

    talib 要求所有输入数据为 float64 类型

    Args:
        data: 输入数据（Series 或 ndarray）

    Returns:
        np.ndarray: float64 类型的数组
    """
    if isinstance(data, pd.Series):
        return data.astype(np.float64).values
    elif isinstance(data, np.ndarray):
        return data.astype(np.float64)
    else:
        return np.array(data, dtype=np.float64)

# ==================== 配置类 ====================

@dataclass
class AnalysisConfig:
    """股票分析配置类

    集中管理所有分析参数，包括数据列名、技术指标参数、
    阈值设置和信号权重等
    """

    # ========== 数据列名配置 ==========
    date_column: str = 'date'
    close_column: str = 'close'
    high_column: str = 'high'
    low_column: str = 'low'
    volume_column: str = 'volume'
    open_column: str = 'open'
    amount_column: str = 'amount'

    # ========== 技术指标参数 ==========
    # RSI参数
    rsi_period: int = 14
    rsi_oversold: float = 30.0
    rsi_overbought: float = 70.0

    # MACD参数
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal: int = 9

    # 移动平均线参数
    ma_short: int = 5
    ma_medium: int = 10
    ma_long: int = 20
    ma_extra_long: int = 30

    # ========== 阈值参数 ==========
    # 成交量相关
    volume_threshold: float = 1.5          # 放量阈值倍数
    heavy_volume_threshold: float = 1.5    # 重量成交量阈值

    # 价格动量相关
    momentum_threshold: float = 0.002      # 动量阈值
    price_gain_threshold: float = 0.03     # 涨幅阈值
    body_ratio_threshold: float = 0.6      # 实体占比阈值

    # 动量计算阈值
    momentum_5d_threshold: float = 0.02    # 5日涨幅门槛
    momentum_10d_threshold: float = 0.03   # 10日涨幅门槛

    # ========== 信号权重配置 ==========
    signal_weights: Dict[str, float] = field(default_factory=dict)

    # 技术面评分权重
    technical_weights: Dict[str, float] = field(default_factory=dict)

    def __post_init__(self):
        """初始化默认权重配置"""
        if not self.signal_weights:
            self.signal_weights = {
                'strong_trend': 0.35,           # 强势趋势权重（提升）
                'breakout_platform': 0.35,     # 平台突破权重（提升）
                'big_divergence': 0.30,        # 大分歧权重（提升）
            }

        if not self.technical_weights:
            self.technical_weights = {
                'momentum': 0.20,               # 动量权重
                'macd': 0.25,                  # MACD权重（提升）
                'ma': 0.20,                    # 均线权重
                'emv': 0.10,                   # EMV权重
                'strong_trend': 0.15,          # 强势趋势权重
                'three_up': 0.10               # 三连阳权重
            }

# ==================== 主分析类 ====================

class StockOldAnalyzer:
    """股票技术分析器

    提供全面的股票技术分析功能，包括：
    - 技术指标计算（MACD、RSI、布林带、KDJ等）
    - 形态识别（三连阳、底部反转、锤子线等）
    - 动量分析（量价齐升、趋势判断）
    - 综合评分系统（技术面、基本面、资金面、情绪面）

    主要特性：
    1. 参数化配置，支持灵活定制
    2. 完善的异常处理和数据验证
    3. 基于TA-Lib的标准化技术指标
    4. 多维度综合评分体系
    """

    def __init__(self, data: Optional[pd.DataFrame] = None,
                 config: Optional[Dict[str, Any]] = None):
        """初始化股票分析器

        Args:
            data: 股票OHLCV数据，包含开高低收量等字段
            config: 自定义配置参数，用于覆盖默认设置

        Raises:
            ValueError: 当数据格式不正确或缺少必要字段时
        """
        self.data = data
        self.config = AnalysisConfig(**config) if config else AnalysisConfig()
        self.code: Optional[str] = None

        # 数据验证
        self._validate_data()

        # 向后兼容性支持
        self._setup_legacy_attributes()

    def _validate_data(self) -> None:
        """验证输入数据的有效性

        检查数据格式、必要字段和数据质量

        Raises:
            ValueError: 数据格式错误或缺少必要字段
        """
        if self.data is None:
            return

        # 检查数据类型
        if not isinstance(self.data, pd.DataFrame):
            raise ValueError("数据必须是pandas DataFrame格式")

        # 检查必要字段
        required_columns = [
            self.config.close_column,
            self.config.high_column,
            self.config.low_column,
            self.config.volume_column,
            self.config.open_column
        ]

        missing_columns = [col for col in required_columns
                          if col not in self.data.columns]
        if missing_columns:
            raise ValueError(f"缺少必要的数据列: {missing_columns}")

        # 检查数据量
        if len(self.data) == 0:
            raise ValueError("数据为空")
        elif len(self.data) < 30:
            warnings.warn("数据量不足30条，可能影响分析准确性", UserWarning)

        # 数据质量检查
        self._check_data_quality()

    def _check_data_quality(self) -> None:
        """检查数据质量"""
        if self.data is None:
            return

        # 检查价格数据的合理性
        price_columns = [self.config.open_column, self.config.high_column,
                        self.config.low_column, self.config.close_column]

        for col in price_columns:
            if col in self.data.columns:
                # 检查负值
                negative_count = (self.data[col] <= 0).sum()
                if negative_count > 0:
                    warnings.warn(f"{col}列存在{negative_count}个非正值")

                # 检查缺失值
                null_count = self.data[col].isnull().sum()
                if null_count > 0:
                    warnings.warn(f"{col}列存在{null_count}个缺失值")

    def _setup_legacy_attributes(self) -> None:
        """设置向后兼容的属性名"""
        self.date_column = self.config.date_column
        self.close_column = self.config.close_column
        self.high_column = self.config.high_column
        self.low_column = self.config.low_column
        self.volume_column = self.config.volume_column
        self.open_column = self.config.open_column
        self.amount_column = self.config.amount_column
        # 清理数据
        self._clean_data()

    def _clean_data(self) -> None:
        """清理数据中的异常值和格式问题

        执行以下清理操作：
        1. 创建数据副本避免引用问题
        2. 清理价格数据（移除非正值）
        3. 清理成交量数据（移除负值）
        4. 重置索引保证连续性
        """
        if self.data is None:
            return

        try:
            # 创建深度副本避免数据引用问题
            self.data = self.data.copy(deep=True).reset_index(drop=True)
            original_length = len(self.data)

            # 清理价格数据
            self._clean_price_data()

            # 清理成交量数据
            self._clean_volume_data()

            # 清理成交额数据（可选）
            self._clean_amount_data()

            # 最终整理
            self.data = self.data.reset_index(drop=True)

            # 记录清理结果
            final_length = len(self.data)
            if final_length < original_length:
                removed_count = original_length - final_length
                warnings.warn(f"数据清理完成，移除了{removed_count}条异常数据")

        except Exception as e:
            warnings.warn(f"数据清理过程中出现错误: {e}")

    def _clean_price_data(self) -> None:
        """清理价格数据"""
        price_columns = [
            self.config.open_column, self.config.high_column,
            self.config.low_column, self.config.close_column
        ]

        for col in price_columns:
            if col in self.data.columns:
                # 转换为数值格式
                self.data[col] = clean_numeric_series(self.data[col])

                # 移除非正值
                valid_mask = self.data[col] > 0
                self.data = self.data[valid_mask].copy().reset_index(drop=True)

    def _clean_volume_data(self) -> None:
        """清理成交量数据"""
        if self.config.volume_column in self.data.columns:
            # 转换为数值格式，强制使用 float64 以兼容 talib
            self.data[self.config.volume_column] = clean_numeric_series(
                self.data[self.config.volume_column], force_float64=True
            )

            # 移除负值（成交量可以为0）
            valid_mask = self.data[self.config.volume_column] >= 0
            self.data = self.data[valid_mask].copy().reset_index(drop=True)

    def _clean_amount_data(self) -> None:
        """清理成交额数据"""
        if self.config.amount_column in self.data.columns:
            self.data[self.config.amount_column] = clean_numeric_series(
                self.data[self.config.amount_column]
            )

    # ==================== 基础指标计算 ====================

    def _cal_limit_num(self) -> int:
        """计算连续涨停板天数

        识别从最近交易日开始向前的连续涨停天数，支持：
        1. 普通股票（10%涨停）和科创板/创业板（20%涨停）
        2. 一字涨停和普通涨停的识别
        3. 停牌股票的过滤

        Returns:
            int: 连续涨停天数，最多统计10天
        """
        if not validate_data_length(self.data, 2, "计算连板数"):
            return 0

        try:
            limit_count = 0
            max_check_days = min(10, len(self.data) - 1)

            # 从最新交易日开始向前检查
            for i in range(max_check_days):
                today_idx = len(self.data) - 1 - i
                prev_idx = today_idx - 1

                if prev_idx < 0:
                    break

                # 获取交易数据
                today_data = self.data.iloc[today_idx]
                prev_data = self.data.iloc[prev_idx]

                # 检查是否为涨停
                if self._is_limit_up(today_data, prev_data):
                    limit_count += 1
                else:
                    break

            return limit_count

        except Exception as e:
            warnings.warn(f"计算连板数失败: {e}")
            return 0

    def _is_limit_up(self, today_data: pd.Series, prev_data: pd.Series) -> bool:
        """判断是否为涨停

        Args:
            today_data: 当日交易数据
            prev_data: 前一日交易数据

        Returns:
            bool: 是否为涨停
        """
        # 提取价格数据
        today_open = safe_float(today_data[self.config.open_column])
        today_high = safe_float(today_data[self.config.high_column])
        today_low = safe_float(today_data[self.config.low_column])
        today_close = safe_float(today_data[self.config.close_column])
        prev_close = safe_float(prev_data[self.config.close_column])

        # 数据有效性检查
        if any(price <= 0 for price in [today_close, prev_close, today_high, today_low]):
            return False

        # 检查是否停牌
        if self._is_suspended(today_open, today_high, today_low, today_close, prev_close):
            return False

        # 计算涨幅
        price_change_ratio = safe_divide(today_close - prev_close, prev_close)

        # 必须是上涨
        if price_change_ratio <= 0:
            return False

        # 确定涨停阈值
        limit_threshold = self._get_limit_threshold()

        # 判断涨停条件
        # 1. 收盘价接近最高价
        close_near_high = abs(today_close - today_high) < today_close * 0.002

        # 2. 一字涨停（最高价=最低价）
        is_one_line = abs(today_high - today_low) < today_close * 0.001

        # 3. 涨幅达到涨停标准
        sufficient_gain = price_change_ratio > limit_threshold

        return sufficient_gain and (close_near_high or is_one_line)

    def _is_suspended(self, open_price: float, high: float, low: float,
                     close: float, prev_close: float) -> bool:
        """判断是否停牌"""
        price_tolerance = close * 0.001
        change_tolerance = prev_close * 0.001

        # 四价相等且无涨跌幅
        prices_equal = (abs(open_price - high) < price_tolerance and
                       abs(open_price - low) < price_tolerance and
                       abs(open_price - close) < price_tolerance)

        no_change = abs(close - prev_close) < change_tolerance

        return prices_equal and no_change

    def _get_limit_threshold(self) -> float:
        """获取涨停阈值"""
        # 科创板(688)和创业板(300)为20%，其他为10%
        if self.code and (self.code.startswith('688') or self.code.startswith('300')):
            return 0.195  # 19.5%，略低于20%以容忍精度误差
        return 0.095      # 9.5%，略低于10%以容忍精度误差



    def _cal_price_momentum(self, period: int) -> float:
        """计算指定周期的价格动量

        计算从N个交易日前到当前的收益率，用于衡量价格变化趋势

        Args:
            period: 回看的交易日数

        Returns:
            float: 收益率，正值表示上涨，负值表示下跌
        """
        if period <= 0:
            return 0.0

        if not validate_data_length(self.data, period + 1, f"计算{period}日价格动量"):
            return 0.0

        try:
            close_data = clean_numeric_series(self.data[self.config.close_column])

            if len(close_data) < period + 1:
                return 0.0

            # 获取价格数据
            current_price = safe_float(close_data.iloc[-1])
            past_price = safe_float(close_data.iloc[-(period + 1)])

            # 计算收益率
            if past_price > 0 and current_price > 0:
                return safe_divide(current_price - past_price, past_price)

            return 0.0

        except Exception as e:
            warnings.warn(f"计算{period}日价格动量失败: {e}")
            return 0.0

    def _cal_bottom_return(self) -> float:
        """计算从当前数据的最低点到目前的收益率

        计算当前数据集中的历史最低价到当前收盘价的收益率，
        用于衡量股票从底部反弹的幅度

        Returns:
            float: 从历史最低点到当前的收益率
        """
        if not validate_data_length(self.data, 2, "计算底部收益率"):
            return 0.0

        try:
            # 获取并清理价格数据
            close_data = clean_numeric_series(self.data[self.config.close_column])
            low_data = clean_numeric_series(self.data[self.config.low_column])

            if len(close_data) < 2 or len(low_data) < 2:
                return 0.0

            # 计算历史最低价和当前价格
            historical_min_price = safe_float(low_data.min())
            current_price = safe_float(close_data.iloc[-1])

            # 计算从历史最低点到当前的收益率
            if historical_min_price > 0 and current_price > 0:
                return safe_divide(current_price - historical_min_price, historical_min_price)
            else:
                return 0.0

        except Exception as e:
            warnings.warn(f"计算底部收益率失败: {e}")
            return 0.0

    def _cal_momentum(self) -> bool:
        """判断股票动量趋势

        识别量价齐升的走趋势个股，核心逻辑：
        1. 计算多周期平均日收益率，要求短期 > 长期（加速上涨）
        2. 设置最低涨幅门槛，过滤微弱信号
        3. 成交量必须配合，确保量价齐升

        Returns:
            bool: True表示具备动量趋势特征
        """
        if not validate_data_length(self.data, 21, "计算动量"):
            return False

        try:
            # 计算多周期收益率
            ret5 = self._cal_price_momentum(self.config.ma_short)
            ret10 = self._cal_price_momentum(self.config.ma_medium)
            ret20 = self._cal_price_momentum(self.config.ma_long)

            # 计算平均日收益率（消除周期长度影响）
            daily_ret5 = safe_divide(ret5, self.config.ma_short) if ret5 > 0 else 0
            daily_ret10 = safe_divide(ret10, self.config.ma_medium) if ret10 > 0 else 0
            daily_ret20 = safe_divide(ret20, self.config.ma_long) if ret20 > 0 else 0

            # 动量加速判断
            momentum_accelerating = (
                daily_ret5 > daily_ret10 > daily_ret20 > 0 and
                ret5 > self.config.momentum_5d_threshold and
                ret10 > self.config.momentum_10d_threshold
            )

            # 成交量配合检查
            volume_support = self._check_volume_momentum()

            return momentum_accelerating and volume_support

        except Exception as e:
            warnings.warn(f"计算动量失败: {e}")
            return False

    def _check_volume_momentum(self) -> bool:
        """检查成交量是否支持价格动量

        使用OBV指标和成交量对比来判断量价配合情况

        Returns:
            bool: True表示成交量支持价格动量
        """
        if not validate_data_length(self.data, 20, "检查成交量动量"):
            return True  # 数据不足时不作为否决条件

        try:
            # 获取并清理数据
            volume_data = clean_numeric_series(self.data[self.config.volume_column])
            close_data = clean_numeric_series(self.data[self.config.close_column])

            if len(volume_data) < 20 or len(close_data) < 20:
                return True

            # 方法1：使用OBV指标判断
            if self._check_obv_trend(close_data, volume_data):
                return True

            # 方法2：使用成交量对比判断
            return self._check_volume_increase(volume_data)

        except Exception as e:
            warnings.warn(f"检查成交量动量失败: {e}")
            return True

    def _check_obv_trend(self, close_data: pd.Series, volume_data: pd.Series) -> bool:
        """使用OBV指标检查趋势"""
        try:
            # 确保数据类型兼容 talib
            close_compatible = ensure_talib_compatible(close_data)
            volume_compatible = ensure_talib_compatible(volume_data)
            obv_values = ta.OBV(close_compatible, volume_compatible)

            if len(obv_values) < 10:
                return False

            # 比较最近5天与前5天的OBV均值
            recent_obv = safe_float(np.mean(obv_values[-5:]))
            prev_obv = safe_float(np.mean(obv_values[-10:-5]))

            if prev_obv != 0:
                obv_trend = safe_divide(recent_obv - prev_obv, abs(prev_obv))
                return obv_trend > 0.02  # OBV上升超过2%

            return False
        except Exception:
            return False

    def _check_volume_increase(self, volume_data: pd.Series) -> bool:
        """检查成交量是否放大"""
        try:
            recent_volume = safe_float(volume_data.iloc[-5:].mean())
            prev_volume = safe_float(volume_data.iloc[-10:-5].mean())

            if prev_volume <= 0:
                return True

            # 成交量温和放大
            volume_ratio = safe_divide(recent_volume, prev_volume)
            return volume_ratio > self.config.volume_threshold

        except Exception:
            return True

    def _cal_bollinger_bands_signal(self) -> bool:
        """使用 talib 布林带计算买入信号"""
        try:
            close_col = self.config.close_column
            if not validate_data_length(self.data, 20, "计算布林带"):
                return False

            close_data = clean_numeric_series(self.data[close_col])
            if len(close_data) < 20:
                return False

            # 使用 talib 计算布林带
            close_compatible = ensure_talib_compatible(close_data)
            upper, middle, lower = ta.BBANDS(close_compatible, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)

            if len(upper) > 0 and len(lower) > 0 and len(middle) > 0:
                current_price = safe_float(close_data.iloc[-1])
                current_upper = safe_float(upper[-1])
                current_lower = safe_float(lower[-1])
                current_middle = safe_float(middle[-1])

                if not any(pd.isna([current_price, current_upper, current_lower, current_middle])):
                    # 布林带买入信号：价格从下轨反弹或在中轨附近
                    band_width = current_upper - current_lower
                    if band_width > 0:
                        price_position = (current_price - current_lower) / band_width
                        # 价格在下轨附近(0-0.3)或中轨附近(0.4-0.6)
                        return 0.0 <= price_position <= 0.3 or 0.4 <= price_position <= 0.6

            return False
        except Exception as e:
            warnings.warn(f"计算布林带信号失败: {e}")
            return False

    def _cal_stochastic_signal(self) -> bool:
        """使用 talib 随机指标(KDJ)计算买入信号"""
        try:
            if not validate_data_length(self.data, 14, "计算随机指标"):
                return False

            high_data = clean_numeric_series(self.data[self.config.high_column])
            low_data = clean_numeric_series(self.data[self.config.low_column])
            close_data = clean_numeric_series(self.data[self.config.close_column])

            if len(high_data) < 14 or len(low_data) < 14 or len(close_data) < 14:
                return False

            # 使用 talib 计算随机指标
            high_compatible = ensure_talib_compatible(high_data)
            low_compatible = ensure_talib_compatible(low_data)
            close_compatible = ensure_talib_compatible(close_data)
            slowk, slowd = ta.STOCH(high_compatible, low_compatible, close_compatible,
                                   fastk_period=14, slowk_period=3, slowk_matype=0,
                                   slowd_period=3, slowd_matype=0)

            if len(slowk) >= 2 and len(slowd) >= 2:
                current_k = safe_float(slowk[-1])
                current_d = safe_float(slowd[-1])
                prev_k = safe_float(slowk[-2])
                prev_d = safe_float(slowd[-2])

                if not any(pd.isna([current_k, current_d, prev_k, prev_d])):
                    # KDJ 金叉信号：K线上穿D线且都在超卖区域
                    golden_cross = current_k > current_d and prev_k <= prev_d
                    oversold_area = current_k < 30 and current_d < 30
                    return golden_cross and oversold_area

            return False
        except Exception as e:
            warnings.warn(f"计算随机指标信号失败: {e}")
            return False

    def _cal_decision_date_return(self) -> float:
        """计算决策日到当前的收益率

        优化：
        1. 使用系统配置的决策日期（如果可用）
        2. 考虑交易日历
        3. 添加回退机制，确保在各种情况下都能正常工作
        4. 修正数据格式处理

        Returns:
            决策日收益率
        """
        if not validate_data_length(self.data, 5, "计算决策日收益率"):
            return 0.0

        try:
            close_col = self.config.close_column

            # 尝试使用系统配置的决策日期，如果fetch_quant可用
            if fetch_quant is not None:
                try:
                    # 从系统配置获取决策日期
                    _, _, decision_date, _, _, _ = fetch_quant()

                    if decision_date and hasattr(self.config, 'date_column'):
                        date_col = self.config.date_column

                        # 检查日期列是否存在
                        if date_col not in self.data.columns:
                            raise ValueError(f"日期列 {date_col} 不存在")

                        # 确保日期列格式正确
                        try:
                            if not pd.api.types.is_datetime64_any_dtype(self.data[date_col]):
                                self.data[date_col] = pd.to_datetime(self.data[date_col], errors='coerce')
                        except:
                            raise ValueError("日期列转换失败")

                        # 查找决策日期对应的数据
                        try:
                            decision_date_dt = pd.to_datetime(decision_date, errors='coerce')
                            if pd.isna(decision_date_dt):
                                raise ValueError(f"无效的决策日期: {decision_date}")
                        except:
                            raise ValueError(f"决策日期转换失败: {decision_date}")

                        # 找到最接近且不晚于决策日期的交易日
                        try:
                            mask = self.data[date_col] <= decision_date_dt
                            mask = mask & ~pd.isna(self.data[date_col])  # 排除NaN日期

                            # 确保mask和数据索引对齐
                            if len(mask) != len(self.data):
                                mask = mask.reindex(self.data.index, fill_value=False)
                        except:
                            raise ValueError("日期比较失败")

                        if mask.any():
                            try:
                                # 找到决策日期对应的价格，确保索引安全
                                valid_indices = self.data[mask].index
                                if len(valid_indices) > 0:
                                    decision_idx = valid_indices[-1]
                                    # 确保索引在有效范围内
                                    if decision_idx >= 0 and decision_idx < len(self.data) and len(self.data) > 0:
                                        decision_price = safe_float(self.data.loc[decision_idx, close_col])
                                        if len(self.data[close_col]) > 0:
                                            current_price = safe_float(self.data[close_col].iloc[-1])
                                            if decision_price > 0 and current_price > 0:
                                                return safe_divide(current_price - decision_price, decision_price)
                            except Exception as idx_e:
                                raise ValueError(f"索引访问失败: {idx_e}")
                except Exception as e:
                    warnings.warn(f"使用系统配置的决策日期失败: {e}. 将回退到默认逻辑。")

            # 默认逻辑：使用动态决策日
            decision_days = min(5, max(1, len(self.data) // 10))

            # 确保有足够的数据
            if len(self.data) <= decision_days:
                return 0.0

            # 确保索引不越界
            # 确保决策日索引安全
            if len(self.data) < decision_days + 1:
                return 0.0

            decision_price = safe_float(self.data[close_col].iloc[-(decision_days + 1)])
            current_price = safe_float(self.data[close_col].iloc[-1])

            if decision_price <= 0 or current_price <= 0:
                return 0.0

            return safe_divide(current_price - decision_price, decision_price)
        except Exception as e:
            warnings.warn(f"计算决策日收益率失败: {e}")
            return 0.0

    def _cal_isTup(self) -> bool:
        """判断是否出现三连阳形态 - 使用 talib CDL3WHITESOLDIERS

        使用 talib 的标准三白兵形态识别

        Returns:
            bool: True表示检测到三白兵形态
        """
        if not validate_data_length(self.data, 3, "计算三连阳"):
            return False

        try:
            # 清理数据并确保 talib 兼容性
            open_data = clean_numeric_series(self.data[self.config.open_column], force_float64=True)
            high_data = clean_numeric_series(self.data[self.config.high_column], force_float64=True)
            low_data = clean_numeric_series(self.data[self.config.low_column], force_float64=True)
            close_data = clean_numeric_series(self.data[self.config.close_column], force_float64=True)

            if len(open_data) < 3 or len(high_data) < 3 or len(low_data) < 3 or len(close_data) < 3:
                return False

            # 使用 talib 的三白兵形态识别
            three_white_soldiers = ta.CDL3WHITESOLDIERS(
                open_data.values,
                high_data.values,
                low_data.values,
                close_data.values
            )

            # 检查是否识别为三白兵形态
            if len(three_white_soldiers) > 0:
                # talib 返回 100 表示看涨形态，0 表示无形态
                recent_pattern = three_white_soldiers[-1]
                return recent_pattern > 0  # 检测到三白兵形态

            return False
        except Exception as e:
            warnings.warn(f"计算三连阳失败: {e}")
            return False

    def _cal_emv(self) -> bool:
        """计算优化的EMV（能量潮）指标

        优化：
        1. 改进EMV计算公式
        2. 增加多周期确认
        3. 考虑成交量异常值处理
        4. 修正数据格式处理

        Returns:
            EMV是否显示买入信号
        """
        if not validate_data_length(self.data, 20, "计算EMV"):
            return False

        try:
            high_col = self.config.high_column
            low_col = self.config.low_column
            volume_col = self.config.volume_column

            # 清理数据
            high = clean_numeric_series(self.data[high_col])
            low = clean_numeric_series(self.data[low_col])
            volume = clean_numeric_series(self.data[volume_col])

            if len(high) < 20 or len(low) < 20 or len(volume) < 20:
                return False

            # 标准 EMV 计算公式
            # EMV = Distance Moved * Volume / Scale Factor
            # Distance Moved = ((H + L)/2 - (H_prev + L_prev)/2)
            # Scale Factor = Volume / (H - L)

            # 计算价格中点
            mid_point = (high + low) / 2
            distance_move = mid_point.diff()

            # 计算高低价差，避免除零
            price_range = high - low
            # 用均值替换零值，避免除零错误
            mean_range = safe_float(price_range.mean())
            if mean_range <= 0:
                mean_range = 0.01
            price_range = price_range.replace(0, mean_range)

            # 标准 EMV 计算：Distance Moved * Volume / (High - Low)
            # 使用百万为单位来标准化成交量
            volume_millions = volume / 1000000
            emv = distance_move * volume_millions / price_range

            # 移除无效值
            emv = emv.replace([np.inf, -np.inf], np.nan).dropna()

            if len(emv) < 14:
                return False

            # 多周期EMV均线
            emv_short = emv.rolling(7).mean()
            emv_long = emv.rolling(14).mean()

            if len(emv_short) < 2 or len(emv_long) < 2 or len(emv) < 1:
                return False

            # 信号确认：短期EMV上穿长期EMV且都为正
            current_emv = safe_float(emv.iloc[-1]) if len(emv) >= 1 else 0.0
            current_short = safe_float(emv_short.iloc[-1]) if len(emv_short) >= 1 else 0.0
            current_long = safe_float(emv_long.iloc[-1]) if len(emv_long) >= 1 else 0.0
            prev_short = safe_float(emv_short.iloc[-2]) if len(emv_short) >= 2 else 0.0
            prev_long = safe_float(emv_long.iloc[-2]) if len(emv_long) >= 2 else 0.0

            # EMV金叉且趋势向上
            golden_cross = (current_short > current_long and prev_short <= prev_long)
            positive_trend = current_emv > 0 and current_short > 0

            return golden_cross and positive_trend
        except Exception as e:
            warnings.warn(f"计算EMV失败: {e}")
            return False

    def _cal_k(self) -> str:
        """判断当前K线形态 - 使用 talib 蜡烛图形态识别

        使用 talib 的标准蜡烛图形态识别，更加准确和标准化

        Returns:
            K线形态描述
        """
        if not validate_data_length(self.data, 5, "计算K线形态"):  # 需要更多数据用于形态识别
            return ''

        try:
            # 清理数据
            open_data = clean_numeric_series(self.data[self.config.open_column])
            high_data = clean_numeric_series(self.data[self.config.high_column])
            low_data = clean_numeric_series(self.data[self.config.low_column])
            close_data = clean_numeric_series(self.data[self.config.close_column])

            if len(open_data) < 5 or len(high_data) < 5 or len(low_data) < 5 or len(close_data) < 5:
                return ''

            # 使用 talib 的多种蜡烛图形态识别
            patterns = {
                '锤头': ta.CDLHAMMER(open_data.values, high_data.values, low_data.values, close_data.values),
                '倒锤头': ta.CDLINVERTEDHAMMER(open_data.values, high_data.values, low_data.values, close_data.values),
                '十字星': ta.CDLDOJI(open_data.values, high_data.values, low_data.values, close_data.values),
                '长腿十字': ta.CDLLONGLEGGEDDOJI(open_data.values, high_data.values, low_data.values, close_data.values),
                '蜻蜓十字': ta.CDLDRAGONFLYDOJI(open_data.values, high_data.values, low_data.values, close_data.values),
                '墓碑十字': ta.CDLGRAVESTONEDOJI(open_data.values, high_data.values, low_data.values, close_data.values),
                '大阳线': ta.CDLMARUBOZU(open_data.values, high_data.values, low_data.values, close_data.values),
                '纺锤线': ta.CDLSPINNINGTOP(open_data.values, high_data.values, low_data.values, close_data.values),
                '上吊线': ta.CDLHANGINGMAN(open_data.values, high_data.values, low_data.values, close_data.values),
                '流星': ta.CDLSHOOTINGSTAR(open_data.values, high_data.values, low_data.values, close_data.values)
            }

            # 检查最新的K线形态
            for pattern_name, pattern_values in patterns.items():
                if len(pattern_values) > 0:
                    latest_signal = pattern_values[-1]
                    if latest_signal != 0:  # 非零表示检测到形态
                        if latest_signal > 0:
                            return f'{pattern_name}(看涨)'
                        else:
                            return f'{pattern_name}(看跌)'

            # 如果没有检测到特殊形态，使用基本分类
            today = self.data.iloc[-1]
            close_price = safe_float(today[self.config.close_column])
            open_price = safe_float(today[self.config.open_column])
            high_price = safe_float(today[self.config.high_column])
            low_price = safe_float(today[self.config.low_column])

            if close_price <= 0 or open_price <= 0 or high_price <= 0 or low_price <= 0:
                return ''

            # 基本分类
            body_size = abs(close_price - open_price)
            total_range = high_price - low_price

            if total_range <= 0:
                return '一字线'

            body_ratio = safe_divide(body_size, total_range)

            if close_price > open_price:
                if body_ratio > 0.7:
                    return '大阳线'
                elif body_ratio > 0.4:
                    return '中阳线'
                else:
                    return '小阳线'
            elif close_price < open_price:
                if body_ratio > 0.7:
                    return '大阴线'
                elif body_ratio > 0.4:
                    return '中阴线'
                else:
                    return '小阴线'
            else:
                return '平盘'

        except Exception as e:
            warnings.warn(f"计算K线形态失败: {e}")
            return ''

    def analyze_stock(self, code: str, df: Optional[pd.DataFrame] = None) -> 'StockOldAnalysis':
        """分析股票并返回StockOldAnalysis对象

        优化：
        1. 增加数据验证和错误处理
        2. 优化计算顺序，避免重复计算
        3. 改进评分算法
        4. 增加缓存机制

        Args:
            code: 股票代码
            df: 股票数据DataFrame，如不提供则使用实例数据

        Returns:
            包含股票分析结果的对象
        """
        try:
            try:
                from .data_models import StockOldAnalysis
            except ImportError:
                from data_models import StockOldAnalysis

            # 数据准备和验证
            if df is not None:
                self.data = df
                self._validate_data()

            if self.data is None or len(self.data) < 10:
                return self._create_empty_analysis(code)

            # 保存当前分析的股票代码
            self.code = code

            # 基础指标计算（按依赖关系排序）
            limit = self._cal_limit_num()
            ret10 = self._cal_price_momentum(10)
            ret20 = self._cal_price_momentum(20)
            ret100 = self._cal_bottom_return()
            decisionPercent = self._cal_decision_date_return()

            # 技术形态指标
            momentum = self._cal_momentum()
            isTup = self._cal_isTup()
            kline = self._cal_k()

            # 复杂技术指标
            isBottomInversion = self._cal_bottom_inversion()
            isHeavyVolume = self._cal_isHeavyVolume()
            macd = self._cal_macd()
            ma = self._cal_ma()
            max30 = self._cal_high_max()
            emv = self._cal_emv()

            # 高级量化信号
            isStrongTrend = self._cal_strong_trend()
            isBreakoutPlatform = self._cal_breakout_platform()
            isBigDivergence = self._cal_big_divergence()

            # CAPM模型计算
            capm_result = self._cal_CAPM()

            # 综合买入信号（优化后的算法）
            buySignal = self._cal_buy_signal_enhanced(
                isStrongTrend, isBreakoutPlatform, isBigDivergence, isTup,
                momentum, macd, ma, emv
            )

            # 计算优化的评分
            scores = self._calculate_comprehensive_scores(
                momentum, isBottomInversion, isHeavyVolume, macd, ma, emv,
                isStrongTrend, isBreakoutPlatform, isBigDivergence, isTup
            )

            # 创建并返回StockOldAnalysis对象，确保所有数值都经过安全处理
            return StockOldAnalysis(
                code=str(code) if code else '',
                limit=int(limit) if limit is not None else 0,
                ret10=round(safe_float(ret10), 4),
                ret20=round(safe_float(ret20), 4),
                ret100=round(safe_float(ret100), 4),
                momentum=bool(momentum),
                isBottomInversion=bool(isBottomInversion),
                decisionPercent=round(safe_float(decisionPercent), 4),
                isTup=bool(isTup),
                isHeavyVolume=bool(isHeavyVolume),
                macd=bool(macd),
                kline=str(kline) if kline else '',
                ret=round(safe_float(capm_result.get('ret', 0)), 6),
                md=round(safe_float(capm_result.get('max_drawdown', 0)), 6),
                alpha=round(safe_float(capm_result.get('alpha', 0)), 4),
                beta=round(safe_float(capm_result.get('beta', 1)), 4),
                emv=bool(emv),
                score1=round(safe_float(scores['fundamental']), 2),
                score2=round(safe_float(scores['technical']), 2),
                score3=round(safe_float(scores['liquidity']), 2),
                score4=round(safe_float(scores['sentiment']), 2),
                ma=bool(ma),
                max30=bool(max30),
                strongTrend=bool(isStrongTrend),
                breakoutPlatform=bool(isBreakoutPlatform),
                bigDivergence=bool(isBigDivergence),
                buySignal=bool(buySignal)
            )
        except Exception as e:
            warnings.warn(f"分析股票 {code} 失败: {e}")
            return self._create_empty_analysis(code)

    def _create_empty_analysis(self, code: str) -> 'StockOldAnalysis':
        """创建空的分析结果对象，确保所有字段都有正确的默认值"""
        try:
            from .data_models import StockOldAnalysis
        except ImportError:
            from data_models import StockOldAnalysis
        return StockOldAnalysis(
            code=str(code) if code else '',
            limit=0,
            ret10=0.0,
            ret20=0.0,
            ret100=0.0,
            momentum=False,
            isBottomInversion=False,
            decisionPercent=0.0,
            isTup=False,
            isHeavyVolume=False,
            macd=False,
            kline='',
            ret=0.0,
            md=0.0,
            alpha=0.0,
            beta=1.0,
            emv=False,
            score1=0.0,
            score2=0.0,
            score3=0.0,
            score4=0.0,
            ma=False,
            max30=False,
            strongTrend=False,
            breakoutPlatform=False,
            bigDivergence=False,
            fundAccumulation=False,
            buySignal=False
        )

    def _calculate_comprehensive_scores(self, momentum: bool, isBottomInversion: bool,
                                      isHeavyVolume: bool, macd: bool, ma: bool, emv: bool,
                                      isStrongTrend: bool, isBreakoutPlatform: bool,
                                      isBigDivergence: bool, isTup: bool) -> Dict[str, float]:
        """计算综合评分 - 优化版本

        优化要点：
        1. 增加资金吸筹指标的权重
        2. 改进各维度评分算法
        3. 增加指标间的协同效应

        Args:
            各种技术指标的布尔值

        Returns:
            包含四个维度评分的字典
        """
        # 技术面评分 (0-100) - 优化权重分配
        technical_signals = [momentum, macd, ma, emv, isStrongTrend, isTup]
        technical_weights = [0.2, 0.25, 0.2, 0.1, 0.15, 0.1]  # 给MACD和强势趋势更高权重，添加三连阳权重
        technical_score = sum(signal * weight for signal, weight in zip(technical_signals, technical_weights)) * 100

        # 基本面评分 - 改进算法
        ret10 = self._cal_price_momentum(10)
        ret20 = self._cal_price_momentum(20)
        ret5 = self._cal_price_momentum(5)

        # 多时间周期动量评分
        ret5 = safe_float(self._cal_price_momentum(5))
        ret10 = safe_float(self._cal_price_momentum(10))
        ret20 = safe_float(self._cal_price_momentum(20))

        momentum_score = (ret5 * 0.3 + ret10 * 0.4 + ret20 * 0.3) * 500 + 50
        fundamental_score = min(100, max(0, safe_float(momentum_score)))

        # 资金面评分 - 基于成交量分析
        if isHeavyVolume:
            # 放量时给予高分
            liquidity_score = 80
        else:
            # 无放量时给予低分
            liquidity_score = 30

        # 风口面评分 - 优化权重分配
        sentiment_signals = [isBottomInversion, isBreakoutPlatform, isBigDivergence]
        sentiment_weights = [0.3, 0.4, 0.3]  # 给平台突破更高权重
        sentiment_score = sum(signal * weight for signal, weight in zip(sentiment_signals, sentiment_weights)) * 100

        # 综合协同效应调整
        synergy_bonus = 0

        # 技术面 + 资金面协同
        if technical_score > 60 and isHeavyVolume:
            synergy_bonus += 5

        # 技术面 + 风口面协同
        if technical_score > 60 and (isBreakoutPlatform or isBigDivergence):
            synergy_bonus += 5

        # 全面协同（所有维度都较强）
        if technical_score > 60 and fundamental_score > 60 and liquidity_score > 70 and sentiment_score > 60:
            synergy_bonus += 10

        # 应用协同加分
        technical_score = min(100, technical_score + synergy_bonus * 0.3)
        fundamental_score = min(100, fundamental_score + synergy_bonus * 0.2)
        liquidity_score = min(100, liquidity_score + synergy_bonus * 0.3)
        sentiment_score = min(100, sentiment_score + synergy_bonus * 0.2)

        return {
            'technical': round(safe_float(technical_score), 2),
            'fundamental': round(safe_float(fundamental_score), 2),
            'liquidity': round(safe_float(liquidity_score), 2),
            'sentiment': round(safe_float(sentiment_score), 2)
        }

    def _cal_isHeavyVolume(self) -> bool:
        """判断是否出现放量突破

        识别放量突破的关键特征：
        1. 阳线且实体较大（实体占比>60%）
        2. 涨幅超过3%
        3. 突破昨日最高价
        4. 成交量放大1.5倍以上

        Returns:
            bool: True表示出现放量突破
        """
        if not validate_data_length(self.data, 21, "计算放量突破"):
            return False

        try:
            # 获取当日和昨日数据
            today = self.data.iloc[-1]
            yesterday = self.data.iloc[-2] if len(self.data) >= 2 else today

            # 提取当日价格和成交量数据
            close_price = safe_float(today[self.config.close_column])
            open_price = safe_float(today[self.config.open_column])
            high_price = safe_float(today[self.config.high_column])
            low_price = safe_float(today[self.config.low_column])
            volume = safe_float(today[self.config.volume_column])
            yesterday_high = safe_float(yesterday[self.config.high_column])

            # 数据有效性检查
            if any(x <= 0 for x in [close_price, open_price, high_price, low_price, volume]):
                return False

            # 计算20日成交量均线
            volume_data = clean_numeric_series(self.data[self.config.volume_column])
            if len(volume_data) < 20:
                return False

            volume_ma20 = ta.SMA(volume_data.values, timeperiod=20)
            if len(volume_ma20) == 0 or pd.isna(volume_ma20[-1]):
                return False

            avg_volume = safe_float(volume_ma20[-1])

            # 放量突破条件判断
            # 1. 阳线且实体较大
            is_bullish = close_price > open_price
            body_ratio = safe_divide(close_price - open_price, high_price - low_price)

            # 2. 涨幅超过3%
            price_gain = safe_divide(close_price - open_price, open_price)

            # 3. 突破昨日最高价
            resistance_break = close_price > yesterday_high

            # 4. 成交量放大1.5倍以上
            volume_amplified = volume > avg_volume * 1.5 if avg_volume > 0 else False

            # 综合判断
            return (is_bullish and body_ratio > 0.6 and price_gain > 0.03 and
                   resistance_break and volume_amplified)

        except Exception as e:
            warnings.warn(f"计算放量突破失败: {e}")
            return False

    def _cal_bottom_inversion(self) -> bool:
        """判断是否出现底部反转信号

        使用技术分析指标识别底部反转机会：
        1. RSI超卖（< 30）
        2. 底部反转K线形态（锤子线、十字星）
        3. 成交量放大确认

        Returns:
            bool: True表示出现底部反转信号
        """
        if not validate_data_length(self.data, 20, "计算底部反转"):
            return False

        try:
            # 获取并清理价格数据
            open_data = clean_numeric_series(self.data[self.config.open_column])
            high_data = clean_numeric_series(self.data[self.config.high_column])
            low_data = clean_numeric_series(self.data[self.config.low_column])
            close_data = clean_numeric_series(self.data[self.config.close_column])
            volume_data = clean_numeric_series(self.data[self.config.volume_column])

            if len(close_data) < 20:
                return False

            # 1. RSI超卖判断
            rsi = ta.RSI(close_data.values, timeperiod=14)
            if len(rsi) == 0:
                return False

            current_rsi = safe_float(rsi[-1])
            oversold = current_rsi < 30

            # 2. 底部反转K线形态识别
            hammer = ta.CDLHAMMER(open_data.values, high_data.values,
                                 low_data.values, close_data.values)
            doji = ta.CDLDOJI(open_data.values, high_data.values,
                             low_data.values, close_data.values)

            reversal_pattern = False
            if len(hammer) > 0 and len(doji) > 0:
                reversal_pattern = hammer[-1] > 0 or doji[-1] > 0

            # 3. 成交量放大确认
            volume_ma = ta.SMA(volume_data.values, timeperiod=10)
            volume_support = False
            if len(volume_ma) > 0:
                current_volume = safe_float(volume_data.iloc[-1])
                avg_volume = safe_float(volume_ma[-1])
                volume_support = current_volume > avg_volume * 1.5 if avg_volume > 0 else False

            # 综合判断：超卖 + 反转形态 + 成交量放大
            return oversold and reversal_pattern and volume_support

        except Exception as e:
            warnings.warn(f"计算底部反转失败: {e}")
            return False
    def _max_drawdown(self):
        """计算最大回撤

        Returns:
            float: 最大回撤值
        """
        try:
            cumulative_return = (1 + self.data['daily_return']).cumprod()
            peak = cumulative_return.expanding(min_periods=1).max()
            drawdown = (cumulative_return / peak) - 1
            max_drawdown = drawdown.min()
            return max_drawdown
        except Exception as e:
            warnings.warn(f"计算最大回撤失败: {e}")
            return 0

    def _cal_high_max(self) -> bool:
        """判断收盘价是否接近最高价 - 精简版"""
        if not validate_data_length(self.data, 1, "计算高价最大值"):
            return False

        try:
            today = self.data.iloc[-1]
            close_price = safe_float(today[self.config.close_column])
            high_price = safe_float(today[self.config.high_column])

            if close_price <= 0 or high_price <= 0:
                return False

            # 收盘价接近最高价（98%以上）
            return close_price / high_price > 0.98
        except Exception as e:
            warnings.warn(f"计算高价最大值失败: {e}")
            return False

    def _cal_CAPM(self) -> dict:
        """计算CAPM模型相关指标 - 精简版使用 talib ROC"""
        if not validate_data_length(self.data, 30, "计算CAPM"):
            return {'ret': 0.0, 'alpha': 0.0, 'beta': 1.0, 'max_drawdown': 0.0}

        try:
            close_data = clean_numeric_series(self.data[self.config.close_column])
            if len(close_data) < 30:
                return {'ret': 0.0, 'alpha': 0.0, 'beta': 1.0, 'max_drawdown': 0.0}

            # 使用 talib ROC 计算收益率
            returns = ta.ROC(close_data.values, timeperiod=1)
            if len(returns) == 0:
                return {'ret': 0.0, 'alpha': 0.0, 'beta': 1.0, 'max_drawdown': 0.0}

            # 过滤无效值
            valid_returns = returns[~np.isnan(returns)]
            if len(valid_returns) < 10:
                return {'ret': 0.0, 'alpha': 0.0, 'beta': 1.0, 'max_drawdown': 0.0}

            # 简化的CAPM计算
            total_return = safe_float(close_data.iloc[-1] / close_data.iloc[0] - 1)
            volatility = np.std(valid_returns) if len(valid_returns) > 1 else 0.0

            # 简化的最大回撤计算
            cumulative = np.cumprod(1 + valid_returns / 100)  # ROC返回百分比
            peak = np.maximum.accumulate(cumulative)
            drawdown = (cumulative / peak - 1).min()

            return {
                'ret': total_return,
                'alpha': total_return - 0.03,  # 简化：假设无风险利率3%
                'beta': min(max(volatility / 0.2, 0.5), 2.0),  # 简化：相对标准波动率
                'max_drawdown': drawdown
            }

        except Exception as e:
            warnings.warn(f"计算CAPM失败: {e}")
            return {'ret': 0.0, 'alpha': 0.0, 'beta': 1.0, 'max_drawdown': 0.0}


    def _cal_macd(self) -> bool:
        """计算优化的MACD指标并判断买入信号

        优化：
        1. 改进底背离识别算法
        2. 增加多重确认机制
        3. 考虑成交量配合
        4. 优化参数设置
        5. 修正数据格式处理

        Returns:
            是否出现MACD买入信号
        """
        if not validate_data_length(self.data, 60, "计算MACD"):
            return False

        try:
            close_col = self.config.close_column
            close_series = clean_numeric_series(self.data[close_col])

            if len(close_series) < 60:
                return False

            close = close_series.values

            # 使用配置的MACD参数
            dif, dea, hist = ta.MACD(
                close,
                fastperiod=self.config.macd_fast,
                slowperiod=self.config.macd_slow,
                signalperiod=self.config.macd_signal
            )

            # 数据清理和验证
            valid_indices = ~(np.isnan(dif) | np.isnan(dea) | np.isnan(hist))
            if np.sum(valid_indices) < 30:
                return False

            dif_clean = dif[valid_indices]
            dea_clean = dea[valid_indices]
            hist_clean = hist[valid_indices]
            close_clean = close[valid_indices]

            if len(dif_clean) < 30:
                return False

            # 优化的底背离识别
            def find_enhanced_bottom_divergence():
                """改进的底背离识别算法"""
                lookback_period = min(40, len(close_clean) - 10)
                if lookback_period < 20:
                    return False

                price_recent = close_clean[-lookback_period:]
                dif_recent = dif_clean[-lookback_period:]

                # 使用滚动窗口找低点，更稳健
                window_size = 5
                price_lows = []
                dif_lows = []

                for i in range(window_size, len(price_recent) - window_size):
                    # 价格低点：窗口内最低点
                    if price_recent[i] == min(price_recent[i-window_size:i+window_size+1]):
                        # 确保是显著低点
                        if i > 0 and price_recent[i] < price_recent[i-1] * 0.995:
                            price_lows.append((i, safe_float(price_recent[i])))
                            dif_lows.append((i, safe_float(dif_recent[i])))

                if len(price_lows) < 2:
                    return False

                # 分析最近的两个低点
                price_low1 = price_lows[-2]
                price_low2 = price_lows[-1]
                dif_low1 = dif_lows[-2]
                dif_low2 = dif_lows[-1]

                # 底背离条件：价格新低但MACD不创新低，且有一定幅度
                price_decline = safe_divide(price_low2[1] - price_low1[1], price_low1[1])
                dif_improvement = safe_divide(dif_low2[1] - dif_low1[1], abs(dif_low1[1])) if dif_low1[1] != 0 else 0

                return price_decline < -0.02 and dif_improvement > 0.1  # 价格跌2%以上，MACD改善10%以上

            # 优化的金叉信号检查
            def check_enhanced_golden_cross():
                """改进的金叉信号检查"""
                if len(dif_clean) < 5:
                    return False

                # 当前和前几天的MACD值
                current_dif = safe_float(dif_clean[-1])
                current_dea = safe_float(dea_clean[-1])
                prev_dif = safe_float(dif_clean[-2])
                prev_dea = safe_float(dea_clean[-2])

                # 金叉确认：DIF上穿DEA
                golden_cross = current_dif > current_dea and prev_dif <= prev_dea

                # 位置确认：在零轴下方或刚突破零轴
                position_ok = current_dif < 0.1  # 允许轻微突破零轴

                # 趋势确认：MACD柱状线改善
                hist_improving = (len(hist_clean) >= 3 and
                                safe_float(hist_clean[-1]) > safe_float(hist_clean[-3]))

                # 强度确认：金叉角度不能太小
                cross_strength = abs(current_dif - current_dea) > abs(prev_dif - prev_dea)

                return golden_cross and position_ok and hist_improving and cross_strength

            # 增强的成交量确认
            def check_enhanced_volume_confirmation():
                """改进的成交量确认"""
                try:
                    volume_col = self.config.volume_column
                    if not validate_data_length(self.data, 15, "成交量确认"):
                        return True

                    # 清理成交量数据
                    volume_data = clean_numeric_series(self.data[volume_col])

                    if len(volume_data) < 20:
                        return True

                    # 多周期成交量分析
                    recent_3d = safe_float(volume_data.iloc[-3:].mean())
                    recent_7d = safe_float(volume_data.iloc[-7:].mean())
                    historical_20d = safe_float(volume_data.iloc[-20:-7].mean())

                    if historical_20d <= 0:
                        return True

                    # 成交量温和放大（避免过度放量）
                    volume_ratio_3d = safe_divide(recent_3d, historical_20d)
                    volume_ratio_7d = safe_divide(recent_7d, historical_20d)

                    # 理想的成交量放大：1.2-2.5倍
                    return (1.2 <= volume_ratio_3d <= 2.5 and
                           1.1 <= volume_ratio_7d <= 2.0)
                except:
                    return True

            # 综合信号评估
            has_bottom_divergence = find_enhanced_bottom_divergence()
            has_golden_cross = check_enhanced_golden_cross()
            volume_confirmed = check_enhanced_volume_confirmation()

            # 多级信号确认策略
            if has_bottom_divergence and has_golden_cross and volume_confirmed:
                return True  # 最强信号：底背离+金叉+量能配合

            # 次级信号：金叉+量能配合（无底背离）
            if has_golden_cross and volume_confirmed:
                # 额外检查：确保不是假突破
                if len(hist_clean) >= 5:
                    # HIST连续改善，确保索引安全
                    try:
                        if isinstance(hist_clean, np.ndarray):
                            hist_trend = all(hist_clean[-i] >= hist_clean[-i-1] for i in range(1, min(4, len(hist_clean))))
                        else:
                            hist_trend = all(hist_clean.iloc[-i] >= hist_clean.iloc[-i-1] for i in range(1, min(4, len(hist_clean))))
                        if hist_trend:
                            return True
                    except (IndexError, ValueError):
                        pass  # 索引越界时跳过这个检查

            # 保守信号：仅金叉但位置较好
            if has_golden_cross and len(dif_clean) > 0 and len(dea_clean) > 0:
                # 在相对低位的金叉，确保安全访问numpy数组
                try:
                    if isinstance(dif_clean, np.ndarray):
                        current_dif = safe_float(dif_clean[-1])
                    else:
                        current_dif = safe_float(dif_clean.iloc[-1])

                    if isinstance(dea_clean, np.ndarray):
                        current_dea = safe_float(dea_clean[-1])
                    else:
                        current_dea = safe_float(dea_clean.iloc[-1])

                    if current_dif < -0.05 and current_dea < -0.05:  # 深度超卖后的金叉
                        return True
                except (IndexError, AttributeError):
                    pass

            return False
        except Exception as e:
            warnings.warn(f"MACD计算失败: {e}")
            return False

    def _cal_ma(self) -> bool:
        """计算优化的均线信号

        优化：
        1. 动态均线周期选择
        2. 多重确认机制
        3. 趋势强度量化
        4. 假突破过滤
        5. 修正数据格式处理

        Returns:
            是否满足均线强势条件
        """
        if not validate_data_length(self.data, 35, "计算均线信号"):
            return False

        try:
            close_col = self.config.close_column
            volume_col = self.config.volume_column
            close = clean_numeric_series(self.data[close_col])

            if len(close) < 35:
                return False

            # 使用 talib 的 SMA 计算均线，更加准确
            ma_short_values = ta.SMA(close.values, timeperiod=self.config.ma_short)
            ma_medium_values = ta.SMA(close.values, timeperiod=self.config.ma_medium)
            ma_long_values = ta.SMA(close.values, timeperiod=self.config.ma_long)
            ma_extra_long_values = ta.SMA(close.values, timeperiod=self.config.ma_extra_long)

            # 转换为 pandas Series 以保持兼容性
            ma_short = pd.Series(ma_short_values, index=close.index)
            ma_medium = pd.Series(ma_medium_values, index=close.index)
            ma_long = pd.Series(ma_long_values, index=close.index)
            ma_extra_long = pd.Series(ma_extra_long_values, index=close.index)

            # 数据有效性检查
            if any(len(ma) < 10 for ma in [ma_short, ma_medium, ma_long, ma_extra_long]):
                return False

            # 获取最新和历史数据，确保索引安全
            current_price = safe_float(close.iloc[-1]) if len(close) >= 1 else 0.0
            current_ma_short = safe_float(ma_short.iloc[-1]) if len(ma_short) >= 1 else 0.0
            current_ma_medium = safe_float(ma_medium.iloc[-1]) if len(ma_medium) >= 1 else 0.0
            current_ma_long = safe_float(ma_long.iloc[-1]) if len(ma_long) >= 1 else 0.0
            current_ma_extra_long = safe_float(ma_extra_long.iloc[-1]) if len(ma_extra_long) >= 1 else 0.0

            prev_ma_short = safe_float(ma_short.iloc[-2]) if len(ma_short) >= 2 else 0.0
            prev_ma_medium = safe_float(ma_medium.iloc[-2]) if len(ma_medium) >= 2 else 0.0
            prev_ma_long = safe_float(ma_long.iloc[-2]) if len(ma_long) >= 2 else 0.0

            # 检查数据有效性
            if any(x <= 0 for x in [current_price, current_ma_short, current_ma_medium,
                                   current_ma_long, current_ma_extra_long]):
                return False

            # 条件1: 多头排列强度评估
            bullish_alignment = (current_price > current_ma_short >
                               current_ma_medium > current_ma_long > current_ma_extra_long)

            # 计算排列强度（均线间距）
            if current_ma_extra_long > 0:
                alignment_strength = safe_divide(current_ma_short - current_ma_extra_long, current_ma_extra_long)
                strong_alignment = alignment_strength > 0.05  # 5%以上的均线展开
            else:
                strong_alignment = False

            # 条件2: 均线交叉信号（更严格的确认）
            cross_signals = []

            # 短期均线上穿中期均线
            if current_ma_short > current_ma_medium and prev_ma_short <= prev_ma_medium:
                cross_signals.append('short_cross_medium')

            # 中期均线上穿长期均线
            if current_ma_medium > current_ma_long and prev_ma_medium <= prev_ma_long:
                cross_signals.append('medium_cross_long')

            has_meaningful_cross = len(cross_signals) > 0

            # 条件3: 均线斜率和趋势强度
            def calculate_ma_slope(ma_series, periods=3):
                """计算均线斜率"""
                if len(ma_series) < periods + 1:
                    return 0
                # 确保索引安全
                start_idx = min(periods + 1, len(ma_series))
                if start_idx > len(ma_series):
                    return 0
                start_value = safe_float(ma_series.iloc[-start_idx])
                end_value = safe_float(ma_series.iloc[-1])
                return safe_divide(end_value - start_value, start_value)

            ma_short_slope = calculate_ma_slope(ma_short)
            ma_medium_slope = calculate_ma_slope(ma_medium)
            ma_long_slope = calculate_ma_slope(ma_long)

            # 均线斜率递增且都向上
            positive_slopes = (ma_short_slope > self.config.momentum_threshold and
                             ma_medium_slope > self.config.momentum_threshold * 0.5 and
                             ma_long_slope > 0)

            slope_acceleration = ma_short_slope > ma_medium_slope > ma_long_slope

            # 条件4: 价格相对位置优化
            price_above_ma = current_price > current_ma_short

            # 价格距离均线的合理性（避免过度偏离）
            if current_ma_short > 0:
                price_deviation = safe_divide(current_price - current_ma_short, current_ma_short)
                reasonable_deviation = 0.001 < price_deviation < 0.06  # 0.1%-6%之间
            else:
                reasonable_deviation = False

            # 条件5: 增强的成交量分析
            volume_confirmation = self._analyze_volume_pattern()

            # 条件6: 趋势持续性验证
            trend_sustainability = self._check_trend_sustainability(close, ma_short, ma_medium)

            # 条件7: 假突破过滤
            false_breakout_filter = self._filter_false_breakout(close, ma_short)

            # 综合评分系统
            score = 0
            if bullish_alignment: score += 25
            if strong_alignment: score += 15
            if has_meaningful_cross: score += 20
            if positive_slopes: score += 15
            if slope_acceleration: score += 10
            if price_above_ma and reasonable_deviation: score += 10
            if volume_confirmation: score += 15
            if trend_sustainability: score += 10
            if false_breakout_filter: score += 10

            # 降低阈值，使信号更容易触发（从70降到50）
            return score >= 50
        except Exception as e:
            warnings.warn(f"计算均线信号失败: {e}")
            return False

    def _analyze_volume_pattern(self) -> bool:
        """分析成交量模式"""
        try:
            volume_col = self.config.volume_column
            if len(self.data) < 25:
                return True

            # 多周期成交量对比
            recent_5d = self.data[volume_col].iloc[-5:].mean()
            recent_10d = self.data[volume_col].iloc[-10:].mean()
            historical_20d = self.data[volume_col].iloc[-25:-5].mean()

            if historical_20d <= 0:
                return True

            # 成交量温和放大且持续
            volume_growth = recent_5d > recent_10d > historical_20d
            moderate_increase = 1.2 <= recent_5d / historical_20d <= 2.5

            return volume_growth and moderate_increase
        except:
            return True

    def _check_trend_sustainability(self, close, ma_short, ma_medium) -> bool:
        """检查趋势可持续性"""
        try:
            if len(close) < 20:
                return True

            # 检查最近的回调幅度
            recent_high = close.iloc[-10:].max()
            recent_low = close.iloc[-5:].min()
            current_price = close.iloc[-1]

            if recent_high > 0:
                pullback_ratio = (recent_high - recent_low) / recent_high
                # 回调幅度适中（不超过8%）
                moderate_pullback = pullback_ratio < 0.08

                # 当前价格接近高点
                near_high = current_price > recent_high * 0.95

                return moderate_pullback and near_high

            return True
        except:
            return True

    def _filter_false_breakout(self, close, ma_short) -> bool:
        """过滤假突破"""
        try:
            if len(close) < 10:
                return True

            # 检查突破的持续性
            days_above_ma = 0
            for i in range(1, min(6, len(close))):
                if close.iloc[-i] > ma_short.iloc[-i]:
                    days_above_ma += 1
                else:
                    break

            # 至少持续3天在均线上方
            return days_above_ma >= 3
        except:
            return True
            
    def _cal_strong_trend(self) -> bool:
        """计算强势趋势指标 - 精简版使用 talib"""
        if not validate_data_length(self.data, 30, "计算强势趋势"):
            return False

        try:
            close_data = clean_numeric_series(self.data[self.config.close_column])
            volume_data = clean_numeric_series(self.data[self.config.volume_column])

            if len(close_data) < 30:
                return False

            # 使用 talib 计算均线
            ma5 = ta.SMA(close_data.values, timeperiod=5)
            ma10 = ta.SMA(close_data.values, timeperiod=10)
            ma20 = ta.SMA(close_data.values, timeperiod=20)

            # 使用 talib 计算 RSI
            rsi = ta.RSI(close_data.values, timeperiod=14)

            # 使用 talib 计算 ROC (动量)
            roc10 = ta.ROC(close_data.values, timeperiod=10)

            if (len(ma5) == 0 or len(ma10) == 0 or len(ma20) == 0 or
                len(rsi) == 0 or len(roc10) == 0):
                return False

            # 获取最新值
            current_price = safe_float(close_data.iloc[-1])
            current_ma5 = safe_float(ma5[-1])
            current_ma10 = safe_float(ma10[-1])
            current_ma20 = safe_float(ma20[-1])
            current_rsi = safe_float(rsi[-1])
            current_roc = safe_float(roc10[-1])

            if any(pd.isna([current_price, current_ma5, current_ma10, current_ma20, current_rsi])):
                return False

            # 精简的强势趋势判断
            # 1. 多头排列：价格 > MA5 > MA10 > MA20
            bullish_alignment = current_price > current_ma5 > current_ma10 > current_ma20

            # 2. RSI 在强势区间但未超买
            rsi_strong = 50 < current_rsi < 80

            # 3. 动量为正（价格上涨）
            momentum_positive = current_roc > 0

            # 4. 成交量配合（使用 OBV）
            volume_support = self._check_volume_momentum()

            # 综合判断
            return bullish_alignment and rsi_strong and momentum_positive and volume_support
        except Exception as e:
            warnings.warn(f"计算强势趋势失败: {e}")
            return False






        except Exception as e:
            warnings.warn(f"计算强势趋势股失败: {e}")
            return False
            
    def _cal_breakout_platform(self) -> bool:
        """计算平台突破指标

        平台突破定义：
        1. 至少识别两个波峰和两个波谷
        2. 确定阻力线（连接波峰）和支撑线（连接波谷）
        3. 当前价格突破最近的波峰（阻力线）
        4. 伴随成交量放大

        Returns:
            bool: True表示发生平台突破
        """
        if not validate_data_length(self.data, 50, "计算平台突破"):
            return False

        try:
            # 获取价格和成交量数据
            close_data = clean_numeric_series(self.data[self.config.close_column])
            high_data = clean_numeric_series(self.data[self.config.high_column])
            low_data = clean_numeric_series(self.data[self.config.low_column])
            volume_data = clean_numeric_series(self.data[self.config.volume_column])

            if len(close_data) < 50:
                return False

            # 1. 识别波峰和波谷
            peaks, valleys = self._find_peaks_and_valleys(high_data, low_data)

            # 2. 检查是否有足够的波峰波谷
            if len(peaks) < 2 or len(valleys) < 2:
                return False

            # 3. 计算阻力线和支撑线
            resistance_level = self._calculate_resistance_level(peaks, high_data)
            support_level = self._calculate_support_level(valleys, low_data)

            if resistance_level is None or support_level is None:
                return False

            # 4. 判断平台特征
            if not self._is_valid_platform(resistance_level, support_level):
                return False

            # 5. 判断是否突破波峰
            current_price = safe_float(close_data.iloc[-1])
            breakout_confirmed = self._check_breakout(current_price, resistance_level)

            # 6. 检查成交量配合
            volume_support = self._check_volume_support(volume_data)

            return breakout_confirmed and volume_support

        except Exception as e:
            warnings.warn(f"计算平台突破失败: {e}")
            return False

    def _find_peaks_and_valleys(self, high_data: pd.Series, low_data: pd.Series) -> tuple:
        """识别波峰和波谷

        Args:
            high_data: 最高价序列
            low_data: 最低价序列

        Returns:
            tuple: (peaks_indices, valleys_indices) 波峰和波谷的索引列表
        """
        try:
            # 使用滑动窗口识别局部极值
            window = 5  # 5日窗口
            peaks = []
            valleys = []

            for i in range(window, len(high_data) - window):
                # 识别波峰：当前高点是窗口内的最高点
                window_highs = high_data.iloc[i-window:i+window+1]
                if high_data.iloc[i] == window_highs.max() and high_data.iloc[i] > high_data.iloc[i-1] and high_data.iloc[i] > high_data.iloc[i+1]:
                    peaks.append(i)

                # 识别波谷：当前低点是窗口内的最低点
                window_lows = low_data.iloc[i-window:i+window+1]
                if low_data.iloc[i] == window_lows.min() and low_data.iloc[i] < low_data.iloc[i-1] and low_data.iloc[i] < low_data.iloc[i+1]:
                    valleys.append(i)

            # 过滤过于接近的极值点
            peaks = self._filter_close_extremes(peaks, high_data, min_distance=10)
            valleys = self._filter_close_extremes(valleys, low_data, min_distance=10)

            return peaks, valleys

        except Exception as e:
            warnings.warn(f"识别波峰波谷失败: {e}")
            return [], []

    def _filter_close_extremes(self, indices: list, data: pd.Series, min_distance: int = 10) -> list:
        """过滤过于接近的极值点

        Args:
            indices: 极值点索引列表
            data: 价格数据
            min_distance: 最小距离（天数）

        Returns:
            list: 过滤后的极值点索引
        """
        if len(indices) <= 1:
            return indices

        filtered = [indices[0]]

        for i in range(1, len(indices)):
            # 如果距离足够远，直接添加
            if indices[i] - filtered[-1] >= min_distance:
                filtered.append(indices[i])
            else:
                # 如果距离太近，保留更极端的值
                if abs(data.iloc[indices[i]]) > abs(data.iloc[filtered[-1]]):
                    filtered[-1] = indices[i]

        return filtered

    def _calculate_resistance_level(self, peaks: list, high_data: pd.Series) -> float:
        """计算阻力线水平

        Args:
            peaks: 波峰索引列表
            high_data: 最高价数据

        Returns:
            float: 阻力线水平，None表示无法计算
        """
        if len(peaks) < 2:
            return None

        try:
            # 取最近的两个波峰
            recent_peaks = sorted(peaks)[-2:]
            peak_prices = [high_data.iloc[i] for i in recent_peaks]

            # 简单的阻力线：取两个波峰的平均值
            # 也可以用线性回归计算斜率，这里简化处理
            resistance = sum(peak_prices) / len(peak_prices)

            return safe_float(resistance)

        except Exception as e:
            warnings.warn(f"计算阻力线失败: {e}")
            return None

    def _calculate_support_level(self, valleys: list, low_data: pd.Series) -> float:
        """计算支撑线水平

        Args:
            valleys: 波谷索引列表
            low_data: 最低价数据

        Returns:
            float: 支撑线水平，None表示无法计算
        """
        if len(valleys) < 2:
            return None

        try:
            # 取最近的两个波谷
            recent_valleys = sorted(valleys)[-2:]
            valley_prices = [low_data.iloc[i] for i in recent_valleys]

            # 简单的支撑线：取两个波谷的平均值
            support = sum(valley_prices) / len(valley_prices)

            return safe_float(support)

        except Exception as e:
            warnings.warn(f"计算支撑线失败: {e}")
            return None

    def _is_valid_platform(self, resistance_level: float, support_level: float) -> bool:
        """判断是否形成有效的平台

        Args:
            resistance_level: 阻力线水平
            support_level: 支撑线水平

        Returns:
            bool: True表示形成有效平台
        """
        try:
            if resistance_level <= support_level:
                return False

            # 计算平台的相对高度
            platform_height = (resistance_level - support_level) / support_level

            # 平台高度应该在合理范围内（5%-30%）
            # 太小说明没有明显的整理，太大说明不是平台而是趋势
            return 0.05 <= platform_height <= 0.30

        except Exception as e:
            warnings.warn(f"判断平台有效性失败: {e}")
            return False

    def _check_breakout(self, current_price: float, resistance_level: float) -> bool:
        """检查是否突破阻力线

        Args:
            current_price: 当前价格
            resistance_level: 阻力线水平

        Returns:
            bool: True表示突破确认
        """
        try:
            # 突破确认：当前价格超过阻力线的2%
            breakout_threshold = resistance_level * 1.02
            return current_price > breakout_threshold

        except Exception as e:
            warnings.warn(f"检查突破失败: {e}")
            return False

    def _check_volume_support(self, volume_data: pd.Series) -> bool:
        """检查成交量是否支持突破

        Args:
            volume_data: 成交量数据

        Returns:
            bool: True表示成交量支持
        """
        try:
            if len(volume_data) < 20:
                return False

            # 计算20日平均成交量
            recent_volume = volume_data.iloc[-5:].mean()  # 最近5日平均
            avg_volume = volume_data.iloc[-20:].mean()    # 20日平均

            # 成交量放大：最近5日平均成交量 > 20日平均成交量的1.5倍
            return recent_volume > avg_volume * 1.5 if avg_volume > 0 else False

        except Exception as e:
            warnings.warn(f"检查成交量支持失败: {e}")
            return False

    def _cal_big_divergence(self) -> bool:
        """计算大分歧形态

        大分歧定义：最后两天分别有不同类型的长影线
        - 一天是长上影线（上影线长度 > 实体长度）
        - 另一天是长下影线（下影线长度 > 实体长度）
        - 顺序不限，只要两天的影线类型不同即可

        Returns:
            bool: True表示符合大分歧形态
        """
        if not validate_data_length(self.data, 2, "计算大分歧"):
            return False

        try:
            # 获取最后两天的数据
            last_day = self.data.iloc[-1]
            second_last_day = self.data.iloc[-2]

            # 获取每天的主要影线类型（只能是上影线或下影线中的一种）
            second_last_type = self._get_dominant_shadow_type(second_last_day)
            last_day_type = self._get_dominant_shadow_type(last_day)

            # 大分歧形态：两天的主要影线类型不同且都有长影线
            has_different_shadows = (
                second_last_type is not None and
                last_day_type is not None and
                second_last_type != last_day_type
            )

            return has_different_shadows

        except Exception as e:
            warnings.warn(f"计算大分歧失败: {e}")
            return False

    def _get_dominant_shadow_type(self, day_data: pd.Series) -> str:
        """获取主要影线类型

        判断一天中哪种影线更突出，返回主要的影线类型

        Args:
            day_data: 单日交易数据

        Returns:
            str: 'upper' 表示长上影线，'lower' 表示长下影线，None 表示无明显长影线
        """
        try:
            open_price = safe_float(day_data[self.config.open_column])
            high_price = safe_float(day_data[self.config.high_column])
            low_price = safe_float(day_data[self.config.low_column])
            close_price = safe_float(day_data[self.config.close_column])

            # 数据有效性检查
            if any(price <= 0 for price in [open_price, high_price, low_price, close_price]):
                return None

            # 计算实体长度
            body_length = abs(close_price - open_price)

            # 计算上影线和下影线长度
            body_top = max(open_price, close_price)
            body_bottom = min(open_price, close_price)
            upper_shadow_length = high_price - body_top
            lower_shadow_length = body_bottom - low_price

            # 设置最小实体长度阈值，避免十字星的问题
            min_body_threshold = (high_price - low_price) * 0.1  # 全日振幅的10%
            effective_body_length = max(body_length, min_body_threshold)

            # 判断是否有长影线
            has_long_upper = upper_shadow_length > effective_body_length
            has_long_lower = lower_shadow_length > effective_body_length

            # 确定主要影线类型
            if has_long_upper and has_long_lower:
                # 如果两种影线都长，选择更长的那个
                if upper_shadow_length > lower_shadow_length:
                    return 'upper'
                else:
                    return 'lower'
            elif has_long_upper:
                return 'upper'
            elif has_long_lower:
                return 'lower'
            else:
                return None

        except Exception:
            return None

    def _has_long_upper_shadow(self, day_data: pd.Series) -> bool:
        """判断是否为长上影线

        长上影线定义：上影线长度 > 实体长度

        Args:
            day_data: 单日交易数据

        Returns:
            bool: True表示是长上影线
        """
        try:
            open_price = safe_float(day_data[self.config.open_column])
            high_price = safe_float(day_data[self.config.high_column])
            low_price = safe_float(day_data[self.config.low_column])
            close_price = safe_float(day_data[self.config.close_column])

            # 数据有效性检查
            if any(price <= 0 for price in [open_price, high_price, low_price, close_price]):
                return False

            # 计算实体长度（开盘价和收盘价之间的距离）
            body_length = abs(close_price - open_price)

            # 计算上影线长度（最高价到实体上端的距离）
            body_top = max(open_price, close_price)
            upper_shadow_length = high_price - body_top

            # 长上影线判断：上影线长度 > 实体长度
            return upper_shadow_length > body_length

        except Exception:
            return False

    def _has_long_lower_shadow(self, day_data: pd.Series) -> bool:
        """判断是否为长下影线

        长下影线定义：下影线长度 > 实体长度

        Args:
            day_data: 单日交易数据

        Returns:
            bool: True表示是长下影线
        """
        try:
            open_price = safe_float(day_data[self.config.open_column])
            high_price = safe_float(day_data[self.config.high_column])
            low_price = safe_float(day_data[self.config.low_column])
            close_price = safe_float(day_data[self.config.close_column])

            # 数据有效性检查
            if any(price <= 0 for price in [open_price, high_price, low_price, close_price]):
                return False

            # 计算实体长度（开盘价和收盘价之间的距离）
            body_length = abs(close_price - open_price)

            # 计算下影线长度（实体下端到最低价的距离）
            body_bottom = min(open_price, close_price)
            lower_shadow_length = body_bottom - low_price

            # 长下影线判断：下影线长度 > 实体长度
            return lower_shadow_length > body_length

        except Exception:
            return False
            





    def _cal_buy_signal_enhanced(self, isStrongTrend: bool, isBreakoutPlatform: bool,
                               isBigDivergence: bool, isTup: bool,
                               momentum: bool, macd: bool, ma: bool, emv: bool) -> bool:
        """计算增强的买入信号 - 完全独立的智能分析

        不依赖任何现有方法，直接基于 self.data 进行全面技术分析，
        识别最具潜力的买入机会

        Returns:
            bool: True表示强烈推荐买入，False表示不推荐
        """
        try:
            # 数据验证
            if self.data is None or len(self.data) < 30:
                return False

            # 获取必要的价格和成交量数据
            close_prices = clean_numeric_series(self.data[self.config.close_column])
            high_prices = clean_numeric_series(self.data[self.config.high_column])
            low_prices = clean_numeric_series(self.data[self.config.low_column])
            volumes = clean_numeric_series(self.data[self.config.volume_column])

            if len(close_prices) < 30:
                return False

            # 计算综合买入评分 (总分100分)
            total_score = 0.0

            # 1. 趋势分析 (30分)
            trend_score = self._analyze_trend_strength(close_prices)
            total_score += trend_score

            # 2. 技术指标分析 (25分)
            technical_score = self._analyze_technical_indicators(close_prices, high_prices, low_prices)
            total_score += technical_score

            # 3. 量价关系分析 (20分)
            volume_score = self._analyze_volume_price_relationship(close_prices, volumes)
            total_score += volume_score

            # 4. 支撑阻力分析 (15分)
            support_resistance_score = self._analyze_support_resistance(close_prices, high_prices, low_prices)
            total_score += support_resistance_score

            # 5. 风险评估 (10分)
            risk_score = self._analyze_risk_factors(close_prices, volumes)
            total_score += risk_score

            # 6. 趋势持续性验证 (额外检查，防止假突破)
            trend_sustainability = self._check_enhanced_trend_sustainability(close_prices, volumes)

            # 记录详细评分信息（如果有logger的话）
            try:
                from utils.logger import logger
                logger.debug(f"独立买入信号评分 - 趋势:{trend_score:.1f}, 技术:{technical_score:.1f}, "
                            f"量价:{volume_score:.1f}, 支撑:{support_resistance_score:.1f}, "
                            f"风险:{risk_score:.1f}, 持续性:{trend_sustainability}, 总分:{total_score:.1f}")
            except ImportError:
                pass  # 如果没有logger，跳过日志记录

            # 综合判断：评分65分以上 且 趋势持续性良好
            return total_score >= 65.0 and trend_sustainability

        except Exception as e:
            try:
                from utils.logger import logger
                logger.error(f"计算增强买入信号失败: {e}")
            except ImportError:
                pass  # 如果没有logger，跳过日志记录
            return False

    def _analyze_trend_strength(self, close_prices: pd.Series) -> float:
        """分析趋势强度 (满分30分)"""
        try:
            score = 0.0
            recent_20 = close_prices.tail(20)
            recent_5 = close_prices.tail(5)

            if len(recent_20) < 20:
                return 0.0

            # 1. 短期趋势 (10分) - 最近5天的趋势
            if len(recent_5) >= 5:
                short_trend = (recent_5.iloc[-1] - recent_5.iloc[0]) / recent_5.iloc[0]
                if short_trend > 0.05:  # 5天涨幅超过5%
                    score += 10.0
                elif short_trend > 0.02:  # 5天涨幅超过2%
                    score += 7.0
                elif short_trend > 0:  # 5天上涨
                    score += 4.0

            # 2. 中期趋势 (10分) - 最近20天的趋势
            medium_trend = (recent_20.iloc[-1] - recent_20.iloc[0]) / recent_20.iloc[0]
            if medium_trend > 0.15:  # 20天涨幅超过15%
                score += 10.0
            elif medium_trend > 0.08:  # 20天涨幅超过8%
                score += 7.0
            elif medium_trend > 0:  # 20天上涨
                score += 4.0

            # 3. 趋势一致性 (10分) - 短期和中期趋势是否一致
            if len(recent_5) >= 5:
                short_slope = (recent_5.iloc[-1] - recent_5.iloc[0]) / 5
                medium_slope = (recent_20.iloc[-1] - recent_20.iloc[0]) / 20

                if short_slope > 0 and medium_slope > 0:  # 都是上涨趋势
                    if short_slope > medium_slope * 0.8:  # 短期趋势强于中期
                        score += 10.0
                    else:
                        score += 6.0

            return min(score, 30.0)
        except Exception:
            return 0.0

    def _analyze_technical_indicators(self, close_prices: pd.Series,
                                    high_prices: pd.Series, low_prices: pd.Series) -> float:
        """分析技术指标 (满分25分)"""
        try:
            score = 0.0

            if len(close_prices) < 20:
                return 0.0

            # 1. RSI分析 (8分) - 相对强弱指标
            try:
                rsi_values = ta.RSI(ensure_talib_compatible(close_prices), timeperiod=14)
                if len(rsi_values) > 0 and not pd.isna(rsi_values[-1]):
                    current_rsi = rsi_values[-1]
                    if 30 <= current_rsi <= 70:  # RSI在合理区间
                        if 40 <= current_rsi <= 60:  # 最佳区间
                            score += 8.0
                        else:
                            score += 5.0
                    elif current_rsi < 30:  # 超卖后的反弹机会
                        score += 6.0
            except Exception:
                pass

            # 2. MACD分析 (9分) - 趋势跟踪指标
            try:
                macd_line, signal_line, histogram = ta.MACD(ensure_talib_compatible(close_prices))
                if len(macd_line) > 1 and not pd.isna(macd_line[-1]):
                    current_macd = macd_line[-1]
                    prev_macd = macd_line[-2]
                    current_signal = signal_line[-1]

                    # MACD金叉
                    if current_macd > current_signal and prev_macd <= signal_line[-2]:
                        score += 9.0
                    # MACD在零轴上方且上升
                    elif current_macd > 0 and current_macd > prev_macd:
                        score += 6.0
                    # MACD上升趋势
                    elif current_macd > prev_macd:
                        score += 3.0
            except Exception:
                pass

            # 3. 布林带分析 (8分) - 波动性指标
            try:
                upper, middle, lower = ta.BBANDS(ensure_talib_compatible(close_prices))
                if len(upper) > 0 and not pd.isna(upper[-1]):
                    current_price = close_prices.iloc[-1]
                    current_upper = upper[-1]
                    current_lower = lower[-1]
                    current_middle = middle[-1]

                    # 价格位置分析
                    bb_position = (current_price - current_lower) / (current_upper - current_lower)

                    if 0.2 <= bb_position <= 0.8:  # 在布林带中部，适合买入
                        if current_price > current_middle:  # 在中轨上方
                            score += 8.0
                        else:
                            score += 5.0
                    elif bb_position < 0.2:  # 接近下轨，超卖
                        score += 6.0
                    elif bb_position > 0.8:  # 突破上轨，强势
                        score += 7.0
            except Exception:
                # 如果布林带计算失败，给一个基础分数
                score += 3.0

            return min(score, 25.0)
        except Exception:
            return 0.0

    def _analyze_volume_price_relationship(self, close_prices: pd.Series, volumes: pd.Series) -> float:
        """分析量价关系 (满分20分)"""
        try:
            score = 0.0

            if len(close_prices) < 10 or len(volumes) < 10:
                return 0.0

            recent_10_prices = close_prices.tail(10)
            recent_10_volumes = volumes.tail(10)

            # 1. 放量上涨 (10分)
            price_changes = recent_10_prices.pct_change().dropna()
            volume_avg = recent_10_volumes.mean()

            # 检查最近5天的量价配合 (扩大检查范围)
            for i in range(-5, 0):
                if i < len(price_changes) and abs(i) <= len(recent_10_volumes):
                    price_change = price_changes.iloc[i]
                    volume = recent_10_volumes.iloc[i]

                    if price_change > 0.02 and volume > volume_avg * 1.2:  # 涨幅>2%且放量
                        score += 2.5
                    elif price_change > 0.01 and volume > volume_avg * 1.1:  # 涨幅>1%且温和放量
                        score += 1.8
                    elif price_change > 0 and volume > volume_avg:  # 上涨且放量
                        score += 1.2

            # 2. 成交量趋势 (5分)
            recent_5_vol = recent_10_volumes.tail(5)
            prev_5_vol = recent_10_volumes.head(5)

            if len(recent_5_vol) == 5 and len(prev_5_vol) == 5:
                recent_vol_avg = recent_5_vol.mean()
                prev_vol_avg = prev_5_vol.mean()

                if recent_vol_avg > prev_vol_avg * 1.3:  # 成交量明显放大
                    score += 5.0
                elif recent_vol_avg > prev_vol_avg:  # 成交量温和放大
                    score += 3.0

            # 3. OBV指标 (5分) - 能量潮指标
            try:
                obv_values = ta.OBV(ensure_talib_compatible(close_prices), ensure_talib_compatible(volumes))
                if len(obv_values) > 5:
                    recent_obv = obv_values[-5:]
                    if len(recent_obv) >= 5:
                        obv_trend = (recent_obv[-1] - recent_obv[0]) / abs(recent_obv[0]) if recent_obv[0] != 0 else 0
                        if obv_trend > 0.1:  # OBV上升趋势
                            score += 5.0
                        elif obv_trend > 0:
                            score += 3.0
            except Exception:
                pass

            return min(score, 20.0)
        except Exception:
            return 0.0

    def _analyze_support_resistance(self, close_prices: pd.Series,
                                  high_prices: pd.Series, low_prices: pd.Series) -> float:
        """分析支撑阻力位 (满分15分)"""
        try:
            score = 0.0

            if len(close_prices) < 20:
                return 0.0

            current_price = close_prices.iloc[-1]
            recent_20_high = high_prices.tail(20)
            recent_20_low = low_prices.tail(20)

            # 1. 突破阻力位 (8分)
            # 寻找最近的阻力位（前期高点）
            resistance_levels = []
            for i in range(1, len(recent_20_high) - 1):
                if (recent_20_high.iloc[i] > recent_20_high.iloc[i-1] and
                    recent_20_high.iloc[i] > recent_20_high.iloc[i+1]):
                    resistance_levels.append(recent_20_high.iloc[i])

            if resistance_levels:
                nearest_resistance = min(resistance_levels, key=lambda x: abs(x - current_price))
                if current_price > nearest_resistance * 1.02:  # 突破阻力位2%以上
                    score += 8.0
                elif current_price > nearest_resistance:  # 刚突破阻力位
                    score += 5.0
                elif current_price > nearest_resistance * 0.98:  # 接近阻力位
                    score += 3.0

            # 2. 支撑位强度 (7分)
            # 寻找支撑位（前期低点）
            support_levels = []
            for i in range(1, len(recent_20_low) - 1):
                if (recent_20_low.iloc[i] < recent_20_low.iloc[i-1] and
                    recent_20_low.iloc[i] < recent_20_low.iloc[i+1]):
                    support_levels.append(recent_20_low.iloc[i])

            if support_levels:
                nearest_support = max([s for s in support_levels if s < current_price],
                                    default=recent_20_low.min())
                support_distance = (current_price - nearest_support) / current_price

                if 0.05 <= support_distance <= 0.15:  # 距离支撑位5%-15%，安全区域
                    score += 7.0
                elif 0.02 <= support_distance <= 0.20:  # 距离支撑位2%-20%
                    score += 4.0
                elif support_distance > 0:  # 在支撑位之上
                    score += 2.0

            return min(score, 15.0)
        except Exception:
            return 0.0

    def _analyze_risk_factors(self, close_prices: pd.Series, volumes: pd.Series) -> float:
        """分析风险因素 (满分10分)"""
        try:
            score = 10.0  # 从满分开始，发现风险就扣分

            if len(close_prices) < 20:
                return 5.0  # 数据不足，给中等分数

            recent_20 = close_prices.tail(20)
            current_price = close_prices.iloc[-1]

            # 1. 波动率风险检查 (扣分项)
            daily_returns = recent_20.pct_change().dropna()
            if len(daily_returns) > 0:
                volatility = daily_returns.std()
                if volatility > 0.08:  # 日波动率超过8%，风险较高
                    score -= 3.0
                elif volatility > 0.05:  # 日波动率超过5%
                    score -= 1.5

            # 2. 价格位置风险 (扣分项)
            recent_high = recent_20.max()
            recent_low = recent_20.min()
            price_position = (current_price - recent_low) / (recent_high - recent_low) if recent_high != recent_low else 0.5

            if price_position > 0.9:  # 价格在高位，风险较高
                score -= 2.5
            elif price_position > 0.8:  # 价格偏高
                score -= 1.0

            # 3. 连续上涨风险 (扣分项)
            recent_5 = close_prices.tail(5)
            if len(recent_5) == 5:
                consecutive_up_days = 0
                for i in range(1, len(recent_5)):
                    if recent_5.iloc[i] > recent_5.iloc[i-1]:
                        consecutive_up_days += 1
                    else:
                        break

                if consecutive_up_days >= 4:  # 连续4天以上上涨
                    score -= 2.0
                elif consecutive_up_days >= 3:  # 连续3天上涨
                    score -= 1.0

            # 4. 成交量异常 (扣分项)
            if len(volumes) >= 10:
                recent_vol = volumes.tail(5).mean()
                prev_vol = volumes.tail(10).head(5).mean()

                if recent_vol > prev_vol * 3:  # 成交量暴增，可能是出货
                    score -= 1.5

            return max(score, 0.0)  # 确保分数不为负
        except Exception:
            return 5.0  # 异常情况给中等分数

    def _check_enhanced_trend_sustainability(self, close_prices: pd.Series, volumes: pd.Series) -> bool:
        """检查增强的趋势持续性，防止假突破"""
        try:
            if len(close_prices) < 10:
                return True  # 数据不足时给予通过

            recent_10 = close_prices.tail(10)
            recent_5 = close_prices.tail(5)
            recent_3 = close_prices.tail(3)

            sustainability_score = 0

            # 1. 最近3天趋势检查 (3分)
            consecutive_down = 0
            for i in range(1, len(recent_3)):
                if recent_3.iloc[i] < recent_3.iloc[i-1]:
                    consecutive_down += 1
                else:
                    break

            if consecutive_down == 0:  # 没有连续下跌
                sustainability_score += 3
            elif consecutive_down == 1:  # 只有1天下跌
                sustainability_score += 1
            # 连续2天以上下跌不加分

            # 2. 最近5天整体趋势 (3分)
            if len(recent_5) >= 5:
                trend_5 = (recent_5.iloc[-1] - recent_5.iloc[0]) / recent_5.iloc[0]
                if trend_5 > 0.02:  # 5天涨幅超过2%
                    sustainability_score += 3
                elif trend_5 > 0:  # 5天上涨
                    sustainability_score += 2
                elif trend_5 > -0.01:  # 5天跌幅小于1%
                    sustainability_score += 1

            # 3. 成交量活跃度 (2分)
            if len(volumes) >= 10:
                recent_vol = volumes.tail(3).mean()
                prev_vol = volumes.tail(10).head(7).mean()

                if recent_vol > prev_vol * 1.1:  # 成交量放大
                    sustainability_score += 2
                elif recent_vol > prev_vol * 0.8:  # 成交量保持
                    sustainability_score += 1

            # 4. 价格位置合理性 (2分)
            if len(recent_10) >= 10:
                recent_high = recent_10.max()
                recent_low = recent_10.min()
                current_price = close_prices.iloc[-1]

                price_position = (current_price - recent_low) / (recent_high - recent_low) if recent_high != recent_low else 0.5

                if 0.6 <= price_position <= 0.9:  # 在合理的高位
                    sustainability_score += 2
                elif 0.4 <= price_position <= 0.95:  # 在可接受范围
                    sustainability_score += 1

            # 总分10分，6分以上认为趋势可持续
            return sustainability_score >= 6
        except Exception:
            return True  # 异常情况给予通过，避免过于严格

    def _calculate_technical_score(self, isStrongTrend: bool, isBreakoutPlatform: bool,
                                 isBigDivergence: bool, isTup: bool) -> float:
        """计算核心技术信号评分 (满分40分)"""
        try:
            score = 0.0

            # 强趋势信号 (15分) - 最重要的信号
            if isStrongTrend:
                score += 15.0

            # 平台突破信号 (12分) - 关键突破点
            if isBreakoutPlatform:
                score += 12.0

            # 大分歧信号 (8分) - 市场分歧后的机会
            if isBigDivergence:
                score += 8.0

            # 三白兵信号 (5分) - 连续上涨确认
            if isTup:
                score += 5.0

            return score
        except Exception:
            return 0.0

    def _calculate_volume_price_score(self) -> float:
        """计算量价关系评分 (满分25分)"""
        try:
            score = 0.0
            recent_data = self.data.tail(10)

            if len(recent_data) < 5:
                return 0.0

            # 1. 放量上涨确认 (10分)
            volume_increase_score = self._check_volume_price_coordination(recent_data)
            score += volume_increase_score

            # 2. 成交量趋势 (8分)
            volume_trend_score = self._check_volume_trend(recent_data)
            score += volume_trend_score

            # 3. 价格动能 (7分)
            price_momentum_score = self._check_price_momentum(recent_data)
            score += price_momentum_score

            return min(score, 25.0)  # 确保不超过满分
        except Exception:
            return 0.0

    def _calculate_trend_strength_score(self, momentum: bool, macd: bool, ma: bool) -> float:
        """计算趋势强度评分 (满分20分)"""
        try:
            score = 0.0

            # 动量信号 (8分)
            if momentum:
                score += 8.0

            # MACD信号 (7分)
            if macd:
                score += 7.0

            # 均线信号 (5分)
            if ma:
                score += 5.0

            # 额外的趋势确认
            trend_confirmation = self._check_trend_confirmation()
            score += trend_confirmation  # 最多额外5分

            return min(score, 20.0)
        except Exception:
            return 0.0

    def _calculate_risk_control_score(self, emv: bool) -> float:
        """计算风险控制评分 (满分15分)"""
        try:
            score = 0.0

            # EMV信号 (5分)
            if emv:
                score += 5.0

            # 波动率检查 (5分)
            volatility_score = self._check_volatility_risk()
            score += volatility_score

            # 支撑位检查 (5分)
            support_score = self._check_support_level()
            score += support_score

            return min(score, 15.0)
        except Exception:
            return 0.0




