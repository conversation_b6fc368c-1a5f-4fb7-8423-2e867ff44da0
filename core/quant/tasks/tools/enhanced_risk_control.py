# -*- coding: UTF-8 -*-
"""
增强版风险控制模块
Enhanced Risk Control Module

主要功能:
1. 动态止损止盈策略
2. ATR波动止盈策略  
3. 多因子风险评估
4. 仓位管理建议
5. 风险预警系统

作者: AI量化分析师
版本: v1.0
"""

import pandas as pd
import numpy as np
import talib
from typing import Dict, Any, Tuple, Optional
from enum import Enum

from utils.logger import logger


class RiskLevel(Enum):
    """风险等级枚举"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


class PositionSizeMethod(Enum):
    """仓位计算方法枚举"""
    FIXED_RATIO = "fixed_ratio"
    ATR_BASED = "atr_based"
    VOLATILITY_BASED = "volatility_based"
    KELLY_CRITERION = "kelly_criterion"


class EnhancedRiskControl:
    """增强版风险控制器"""
    
    def __init__(self):
        # 风险控制参数
        self.risk_params = {
            'max_position_size': 0.1,  # 最大单笔仓位10%
            'max_total_risk': 0.2,     # 最大总风险20%
            'atr_multiplier': 2.0,     # ATR止损倍数
            'profit_atr_multiplier': 3.0,  # ATR止盈倍数
            'max_drawdown_threshold': 0.15,  # 最大回撤阈值15%
            'volatility_lookback': 20,  # 波动率计算周期
            'risk_free_rate': 0.03,    # 无风险利率3%
        }
        
        # 动态调整参数
        self.dynamic_params = {
            'market_volatility_threshold': 0.25,  # 市场波动率阈值
            'trend_strength_threshold': 0.6,      # 趋势强度阈值
            'volume_confirmation_ratio': 1.5,     # 成交量确认倍数
        }
    
    def calculate_dynamic_stop_loss(self, df: pd.DataFrame, entry_price: float, 
                                  signal_type: str, atr_period: int = 14) -> float:
        """
        计算动态止损位
        
        Args:
            df: 股票数据
            entry_price: 入场价格
            signal_type: 信号类型 ('buy' or 'sell')
            atr_period: ATR计算周期
            
        Returns:
            止损价格
        """
        try:
            if len(df) < atr_period:
                # 数据不足时使用固定百分比
                if signal_type.lower() == 'buy':
                    return entry_price * 0.95  # 5%止损
                else:
                    return entry_price * 1.05
            
            # 计算ATR
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            atr = talib.ATR(high, low, close, timeperiod=atr_period)
            current_atr = atr[-1] if not np.isnan(atr[-1]) else np.nanmean(atr[-5:])
            
            if np.isnan(current_atr):
                # ATR计算失败，使用价格波动率
                returns = pd.Series(close).pct_change().dropna()
                volatility = returns.std() * np.sqrt(252)  # 年化波动率
                current_atr = entry_price * volatility / 16  # 近似ATR
            
            # 根据市场状况调整ATR倍数
            market_volatility = self._calculate_market_volatility(df)
            atr_multiplier = self._adjust_atr_multiplier(market_volatility)
            
            if signal_type.lower() == 'buy':
                stop_loss = entry_price - (current_atr * atr_multiplier)
            else:
                stop_loss = entry_price + (current_atr * atr_multiplier)
            
            return max(stop_loss, 0)  # 确保止损价格为正
            
        except Exception as e:
            logger.error(f"计算动态止损失败: {e}")
            # 返回保守的固定止损
            if signal_type.lower() == 'buy':
                return entry_price * 0.95
            else:
                return entry_price * 1.05
    
    def calculate_dynamic_take_profit(self, df: pd.DataFrame, entry_price: float,
                                    signal_type: str, atr_period: int = 14) -> float:
        """
        计算动态止盈位 - 基于ATR和趋势强度
        
        Args:
            df: 股票数据
            entry_price: 入场价格
            signal_type: 信号类型
            atr_period: ATR计算周期
            
        Returns:
            止盈价格
        """
        try:
            if len(df) < atr_period:
                # 数据不足时使用固定倍数
                if signal_type.lower() == 'buy':
                    return entry_price * 1.1  # 10%止盈
                else:
                    return entry_price * 0.9
            
            # 计算ATR
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            atr = talib.ATR(high, low, close, timeperiod=atr_period)
            current_atr = atr[-1] if not np.isnan(atr[-1]) else np.nanmean(atr[-5:])
            
            if np.isnan(current_atr):
                returns = pd.Series(close).pct_change().dropna()
                volatility = returns.std() * np.sqrt(252)
                current_atr = entry_price * volatility / 16
            
            # 根据趋势强度调整止盈倍数
            trend_strength = self._calculate_trend_strength(df)
            profit_multiplier = self._adjust_profit_multiplier(trend_strength)
            
            if signal_type.lower() == 'buy':
                take_profit = entry_price + (current_atr * profit_multiplier)
            else:
                take_profit = entry_price - (current_atr * profit_multiplier)
            
            return max(take_profit, 0)
            
        except Exception as e:
            logger.error(f"计算动态止盈失败: {e}")
            if signal_type.lower() == 'buy':
                return entry_price * 1.1
            else:
                return entry_price * 0.9
    
    def calculate_position_size(self, account_value: float, entry_price: float,
                              stop_loss: float, risk_per_trade: float = 0.02,
                              method: PositionSizeMethod = PositionSizeMethod.ATR_BASED) -> int:
        """
        计算建议仓位大小
        
        Args:
            account_value: 账户总价值
            entry_price: 入场价格
            stop_loss: 止损价格
            risk_per_trade: 单笔交易风险比例
            method: 仓位计算方法
            
        Returns:
            建议股数
        """
        try:
            if method == PositionSizeMethod.FIXED_RATIO:
                # 固定比例法
                position_value = account_value * self.risk_params['max_position_size']
                shares = int(position_value / entry_price)
                
            elif method == PositionSizeMethod.ATR_BASED:
                # 基于ATR的风险控制
                risk_per_share = abs(entry_price - stop_loss)
                if risk_per_share > 0:
                    max_risk_amount = account_value * risk_per_trade
                    shares = int(max_risk_amount / risk_per_share)
                else:
                    shares = 0
                    
            elif method == PositionSizeMethod.VOLATILITY_BASED:
                # 基于波动率的仓位计算
                # 这里需要历史数据，简化处理
                volatility_adjustment = min(abs(entry_price - stop_loss) / entry_price, 0.1)
                base_position = account_value * self.risk_params['max_position_size']
                adjusted_position = base_position * (1 - volatility_adjustment * 5)
                shares = int(adjusted_position / entry_price)
                
            else:  # KELLY_CRITERION 或其他
                # 凯利公式（简化版）
                win_rate = 0.55  # 假设胜率55%
                avg_win = 0.08   # 假设平均盈利8%
                avg_loss = 0.04  # 假设平均亏损4%
                
                kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
                kelly_fraction = max(min(kelly_fraction, 0.25), 0.01)  # 限制在1%-25%之间
                
                position_value = account_value * kelly_fraction
                shares = int(position_value / entry_price)
            
            # 应用最大仓位限制
            max_shares = int(account_value * self.risk_params['max_position_size'] / entry_price)
            shares = min(shares, max_shares)
            
            return max(shares, 0)
            
        except Exception as e:
            logger.error(f"计算仓位大小失败: {e}")
            # 返回保守的仓位
            return int(account_value * 0.05 / entry_price)
    
    def assess_risk_level(self, df: pd.DataFrame, signals: Dict[str, Any]) -> RiskLevel:
        """
        评估风险等级
        
        Args:
            df: 股票数据
            signals: 技术指标信号
            
        Returns:
            风险等级
        """
        try:
            risk_score = 0
            
            # 1. 波动率风险 (0-30分)
            volatility = self._calculate_market_volatility(df)
            if volatility > 0.4:
                risk_score += 30
            elif volatility > 0.3:
                risk_score += 20
            elif volatility > 0.2:
                risk_score += 10
            
            # 2. 趋势一致性风险 (0-25分)
            trend_consistency = self._calculate_trend_consistency(signals)
            if trend_consistency < 0.3:
                risk_score += 25
            elif trend_consistency < 0.5:
                risk_score += 15
            elif trend_consistency < 0.7:
                risk_score += 5
            
            # 3. 成交量风险 (0-20分)
            volume_risk = self._calculate_volume_risk(df)
            risk_score += volume_risk
            
            # 4. 技术指标背离风险 (0-15分)
            divergence_risk = self._calculate_divergence_risk(signals)
            risk_score += divergence_risk
            
            # 5. 市场结构风险 (0-10分)
            structure_risk = self._calculate_structure_risk(df)
            risk_score += structure_risk
            
            # 根据总分确定风险等级
            if risk_score >= 80:
                return RiskLevel.VERY_HIGH
            elif risk_score >= 60:
                return RiskLevel.HIGH
            elif risk_score >= 40:
                return RiskLevel.MEDIUM
            elif risk_score >= 20:
                return RiskLevel.LOW
            else:
                return RiskLevel.VERY_LOW
                
        except Exception as e:
            logger.error(f"评估风险等级失败: {e}")
            return RiskLevel.MEDIUM  # 默认中等风险

    def _calculate_market_volatility(self, df: pd.DataFrame) -> float:
        """计算市场波动率"""
        try:
            if len(df) < 20:
                return 0.02  # 默认2%日波动率

            returns = df['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(252)  # 年化波动率
            return volatility
        except:
            return 0.02

    def _adjust_atr_multiplier(self, market_volatility: float) -> float:
        """根据市场波动率调整ATR倍数"""
        base_multiplier = self.risk_params['atr_multiplier']

        if market_volatility > self.dynamic_params['market_volatility_threshold']:
            # 高波动市场，增加止损距离
            return base_multiplier * 1.5
        elif market_volatility < 0.15:
            # 低波动市场，减少止损距离
            return base_multiplier * 0.8
        else:
            return base_multiplier

    def _calculate_trend_strength(self, df: pd.DataFrame) -> float:
        """计算趋势强度"""
        try:
            if len(df) < 20:
                return 0.5

            close = df['close'].astype(float)
            ma20 = close.rolling(20).mean()

            # 计算价格相对于均线的位置
            price_position = (close.iloc[-1] - ma20.iloc[-1]) / ma20.iloc[-1]

            # 计算均线斜率
            ma_slope = (ma20.iloc[-1] - ma20.iloc[-5]) / ma20.iloc[-5] if len(ma20) >= 5 else 0

            # 综合趋势强度
            trend_strength = abs(price_position) + abs(ma_slope) * 5
            return min(trend_strength, 1.0)
        except:
            return 0.5

    def _adjust_profit_multiplier(self, trend_strength: float) -> float:
        """根据趋势强度调整止盈倍数"""
        base_multiplier = self.risk_params['profit_atr_multiplier']

        if trend_strength > self.dynamic_params['trend_strength_threshold']:
            # 强趋势，扩大止盈目标
            return base_multiplier * 1.5
        elif trend_strength < 0.3:
            # 弱趋势，缩小止盈目标
            return base_multiplier * 0.7
        else:
            return base_multiplier

    def _calculate_trend_consistency(self, signals: Dict[str, Any]) -> float:
        """计算趋势一致性"""
        try:
            # 统计各指标的信号方向
            bullish_signals = 0
            bearish_signals = 0
            total_signals = 0

            # 检查主要指标信号
            indicators = ['ma_bullish', 'macd_signal', 'rsi_signal', 'adx_direction']

            for indicator in indicators:
                if indicator in signals:
                    value = signals[indicator]
                    if value == 1 or value is True:
                        bullish_signals += 1
                    elif value == -1 or value is False:
                        bearish_signals += 1
                    total_signals += 1

            if total_signals == 0:
                return 0.5

            # 计算一致性（同向信号比例）
            max_direction = max(bullish_signals, bearish_signals)
            consistency = max_direction / total_signals

            return consistency
        except:
            return 0.5

    def _calculate_volume_risk(self, df: pd.DataFrame) -> int:
        """计算成交量风险评分 (0-20分)"""
        try:
            if len(df) < 10:
                return 10  # 数据不足，中等风险

            volume = df['volume'].astype(float)
            volume_ma = volume.rolling(20).mean()

            current_volume = volume.iloc[-1]
            avg_volume = volume_ma.iloc[-1]

            if avg_volume <= 0:
                return 15

            volume_ratio = current_volume / avg_volume

            if volume_ratio < 0.3:  # 极度缩量
                return 20
            elif volume_ratio < 0.5:  # 缩量
                return 15
            elif volume_ratio > 5.0:  # 极度放量
                return 15
            elif volume_ratio > 3.0:  # 放量
                return 10
            else:  # 正常量能
                return 5
        except:
            return 10

    def _calculate_divergence_risk(self, signals: Dict[str, Any]) -> int:
        """计算背离风险评分 (0-15分)"""
        try:
            risk_score = 0

            # 检查各种背离信号
            divergence_indicators = [
                'macd_divergence', 'rsi_divergence', 'cci_bullish_divergence',
                'cci_bearish_divergence'
            ]

            for indicator in divergence_indicators:
                if indicator in signals:
                    value = signals[indicator]
                    if value == -1 or indicator.endswith('bearish_divergence'):
                        risk_score += 5  # 顶背离增加风险
                    elif value == 1 or indicator.endswith('bullish_divergence'):
                        risk_score += 3  # 底背离也有一定风险

            return min(risk_score, 15)
        except:
            return 5

    def _calculate_structure_risk(self, df: pd.DataFrame) -> int:
        """计算市场结构风险评分 (0-10分)"""
        try:
            if len(df) < 30:
                return 5

            close = df['close'].astype(float)
            high = df['high'].astype(float)
            low = df['low'].astype(float)

            # 计算近期高低点
            recent_high = high.tail(20).max()
            recent_low = low.tail(20).min()
            current_price = close.iloc[-1]

            # 价格位置风险
            price_range = recent_high - recent_low
            if price_range <= 0:
                return 5

            price_position = (current_price - recent_low) / price_range

            # 接近极值位置风险较高
            if price_position > 0.9 or price_position < 0.1:
                return 10
            elif price_position > 0.8 or price_position < 0.2:
                return 7
            else:
                return 3
        except:
            return 5
