# -*- coding: UTF-8 -*-
"""
增强版信号生成器
Enhanced Signal Generator

主要功能:
1. 多因子验证信号生成
2. 假信号过滤
3. 信号强度评估
4. 多周期共振检测
5. 智能信号合成

优化特性:
- 改进均线金叉/死叉判定逻辑，引入波动过滤和成交量确认
- 增加趋势强度判别指标（ADX、RSI背离、布林带宽度）
- 优化形态识别算法（突破有效性、K线组合过滤）
- 引入多周期验证（日线+周线共振）
- 增加风险控制因子（止损线、ATR波动止盈策略）

作者: AI量化分析师
版本: v1.0
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from enum import Enum
from dataclasses import dataclass

from utils.logger import logger
from .technical_indicators import TechnicalIndicators
from .enhanced_risk_control import EnhancedRiskControl, RiskLevel


class SignalType(Enum):
    """信号类型枚举"""
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    HOLD = "hold"
    SELL = "sell"
    STRONG_SELL = "strong_sell"


class SignalStrength(Enum):
    """信号强度枚举"""
    VERY_WEAK = "very_weak"
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"


@dataclass
class EnhancedTradingSignal:
    """增强版交易信号数据类"""
    signal_type: SignalType
    strength: SignalStrength
    confidence: float  # 0-1之间的置信度
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: int
    risk_level: RiskLevel
    supporting_factors: List[str]  # 支持该信号的因子列表
    warning_factors: List[str]     # 风险警告因子列表
    multi_timeframe_confirmed: bool  # 是否多周期确认
    volume_confirmed: bool         # 是否成交量确认
    breakout_confirmed: bool       # 是否突破确认
    divergence_warning: bool       # 是否存在背离警告
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'signal_type': self.signal_type.value,
            'strength': self.strength.value,
            'confidence': self.confidence,
            'entry_price': self.entry_price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'position_size': self.position_size,
            'risk_level': self.risk_level.value,
            'supporting_factors': self.supporting_factors,
            'warning_factors': self.warning_factors,
            'multi_timeframe_confirmed': self.multi_timeframe_confirmed,
            'volume_confirmed': self.volume_confirmed,
            'breakout_confirmed': self.breakout_confirmed,
            'divergence_warning': self.divergence_warning
        }


class EnhancedSignalGenerator:
    """增强版信号生成器"""
    
    def __init__(self):
        self.technical_indicators = TechnicalIndicators()
        self.risk_control = EnhancedRiskControl()
        
        # 信号权重配置
        self.signal_weights = {
            'trend_indicators': 0.35,      # 趋势指标权重35%
            'momentum_indicators': 0.25,   # 动量指标权重25%
            'volume_indicators': 0.20,     # 成交量指标权重20%
            'volatility_indicators': 0.20  # 波动率指标权重20%
        }
        
        # 信号阈值配置
        self.signal_thresholds = {
            'strong_buy_threshold': 75,    # 强买信号阈值
            'buy_threshold': 60,           # 买入信号阈值
            'sell_threshold': 40,          # 卖出信号阈值
            'strong_sell_threshold': 25,   # 强卖信号阈值
            'min_confidence': 0.6,         # 最小置信度
            'volume_confirmation_ratio': 1.5,  # 成交量确认倍数
        }
    
    def generate_enhanced_signal(self, df: pd.DataFrame, account_value: float = 100000) -> Optional[EnhancedTradingSignal]:
        """
        生成增强版交易信号
        
        Args:
            df: 股票历史数据
            account_value: 账户价值（用于仓位计算）
            
        Returns:
            增强版交易信号或None
        """
        try:
            if len(df) < 30:
                logger.warning("数据不足，无法生成信号")
                return None
            
            # 1. 计算所有技术指标
            signals = self._calculate_all_indicators(df)
            
            # 2. 多因子验证和信号合成
            composite_score, supporting_factors, warning_factors = self._calculate_composite_score(signals)
            
            # 3. 确定信号类型和强度
            signal_type, signal_strength = self._determine_signal_type_and_strength(composite_score)
            
            # 4. 计算置信度
            confidence = self._calculate_confidence(signals, composite_score)
            
            # 5. 过滤弱信号
            if confidence < self.signal_thresholds['min_confidence'] or signal_type == SignalType.HOLD:
                return None
            
            # 6. 计算入场价格、止损止盈
            entry_price = float(df.iloc[-1]['close'])
            signal_direction = 'buy' if signal_type in [SignalType.BUY, SignalType.STRONG_BUY] else 'sell'
            
            stop_loss = self.risk_control.calculate_dynamic_stop_loss(df, entry_price, signal_direction)
            take_profit = self.risk_control.calculate_dynamic_take_profit(df, entry_price, signal_direction)
            
            # 7. 计算建议仓位
            position_size = self.risk_control.calculate_position_size(
                account_value, entry_price, stop_loss, risk_per_trade=0.02
            )
            
            # 8. 评估风险等级
            risk_level = self.risk_control.assess_risk_level(df, signals)
            
            # 9. 检查各种确认条件
            confirmations = self._check_confirmations(signals, df)
            
            # 10. 创建增强版交易信号
            enhanced_signal = EnhancedTradingSignal(
                signal_type=signal_type,
                strength=signal_strength,
                confidence=confidence,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                position_size=position_size,
                risk_level=risk_level,
                supporting_factors=supporting_factors,
                warning_factors=warning_factors,
                multi_timeframe_confirmed=confirmations['multi_timeframe'],
                volume_confirmed=confirmations['volume'],
                breakout_confirmed=confirmations['breakout'],
                divergence_warning=confirmations['divergence']
            )
            
            return enhanced_signal
            
        except Exception as e:
            logger.error(f"生成增强版信号失败: {e}")
            return None
    
    def _calculate_all_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算所有技术指标"""
        signals = {}
        
        # 趋势指标
        signals.update(self.technical_indicators.calculate_ma_signals(df))
        signals.update(self.technical_indicators.calculate_adx_signals(df))
        
        # 动量指标
        signals.update(self.technical_indicators.calculate_macd_signals(df))
        signals.update(self.technical_indicators.calculate_rsi_signals(df))
        signals.update(self.technical_indicators.calculate_kdj_signals(df))
        signals.update(self.technical_indicators.calculate_cci_signals(df))
        signals.update(self.technical_indicators.calculate_williams_r(df))
        signals.update(self.technical_indicators.calculate_stochastic_signals(df))
        
        # 成交量指标
        signals.update(self.technical_indicators.calculate_volume_indicators(df))
        
        # 波动率指标
        signals.update(self.technical_indicators.calculate_bollinger_bands(df))
        signals.update(self.technical_indicators.calculate_atr_signals(df))
        
        # 支撑阻力
        signals.update(self.technical_indicators.calculate_support_resistance(df))
        
        # 动量指标
        signals.update(self.technical_indicators.calculate_momentum_indicators(df))
        
        return signals
    
    def _calculate_composite_score(self, signals: Dict[str, Any]) -> Tuple[float, List[str], List[str]]:
        """
        计算综合评分
        
        Returns:
            (综合评分, 支持因子列表, 警告因子列表)
        """
        try:
            trend_score = 0
            momentum_score = 0
            volume_score = 0
            volatility_score = 0
            
            supporting_factors = []
            warning_factors = []
            
            # 1. 趋势指标评分
            if signals.get('ma_bullish') is True:
                trend_score += 30
                supporting_factors.append("均线多头排列")
            elif signals.get('ma_bullish') is False:
                trend_score -= 30
                warning_factors.append("均线空头排列")
            
            if signals.get('golden_cross'):
                trend_score += 20
                supporting_factors.append("均线金叉")
            elif signals.get('death_cross'):
                trend_score -= 20
                warning_factors.append("均线死叉")
            
            # ADX趋势强度
            if signals.get('adx_strong_trend') and signals.get('adx_bullish'):
                trend_score += 25
                supporting_factors.append("ADX强势多头")
            elif signals.get('adx_strong_trend') and signals.get('adx_bearish'):
                trend_score -= 25
                warning_factors.append("ADX强势空头")
            
            # 多周期共振
            if signals.get('multi_timeframe_resonance'):
                resonance_strength = signals.get('resonance_strength', 0.5)
                trend_score += 15 * resonance_strength
                supporting_factors.append("多周期共振")
            
            # 2. 动量指标评分
            if signals.get('macd_golden_cross'):
                momentum_score += 20
                supporting_factors.append("MACD金叉")
            elif signals.get('macd_death_cross'):
                momentum_score -= 20
                warning_factors.append("MACD死叉")
            
            # RSI信号
            rsi_signal = signals.get('rsi_signal', 0)
            if rsi_signal == 1:
                momentum_score += 15
                supporting_factors.append("RSI超卖")
            elif rsi_signal == -1:
                momentum_score -= 15
                warning_factors.append("RSI超买")
            
            # 背离警告
            if signals.get('macd_divergence') == -1 or signals.get('rsi_divergence') == -1:
                momentum_score -= 20
                warning_factors.append("顶背离警告")
            elif signals.get('macd_divergence') == 1 or signals.get('rsi_divergence') == 1:
                momentum_score += 15
                supporting_factors.append("底背离机会")
            
            # 3. 成交量指标评分
            if signals.get('volume_price_positive'):
                volume_score += 25
                supporting_factors.append("放量上涨")
            elif signals.get('volume_price_negative'):
                volume_score -= 25
                warning_factors.append("放量下跌")
            
            if signals.get('obv_bullish'):
                volume_score += 15
                supporting_factors.append("OBV多头")
            elif signals.get('obv_bearish'):
                volume_score -= 15
                warning_factors.append("OBV空头")
            
            # 4. 波动率指标评分
            if signals.get('bb_lower_breakout') and signals.get('bb_breakout_confirmed'):
                volatility_score += 20
                supporting_factors.append("布林带下轨有效突破")
            elif signals.get('bb_upper_breakout') and signals.get('bb_breakout_confirmed'):
                volatility_score -= 20
                warning_factors.append("布林带上轨突破")
            
            if signals.get('bb_squeeze'):
                volatility_score += 10
                supporting_factors.append("布林带收缩")
            
            if signals.get('atr_low_volatility'):
                volatility_score += 5
                supporting_factors.append("低波动率")
            
            # 计算加权综合评分
            composite_score = (
                trend_score * self.signal_weights['trend_indicators'] +
                momentum_score * self.signal_weights['momentum_indicators'] +
                volume_score * self.signal_weights['volume_indicators'] +
                volatility_score * self.signal_weights['volatility_indicators']
            )
            
            # 标准化到0-100范围
            composite_score = max(0, min(100, composite_score + 50))
            
            return composite_score, supporting_factors, warning_factors
            
        except Exception as e:
            logger.error(f"计算综合评分失败: {e}")
            return 50.0, [], []

    def _determine_signal_type_and_strength(self, composite_score: float) -> Tuple[SignalType, SignalStrength]:
        """根据综合评分确定信号类型和强度"""
        if composite_score >= self.signal_thresholds['strong_buy_threshold']:
            if composite_score >= 85:
                return SignalType.STRONG_BUY, SignalStrength.VERY_STRONG
            else:
                return SignalType.STRONG_BUY, SignalStrength.STRONG
        elif composite_score >= self.signal_thresholds['buy_threshold']:
            if composite_score >= 70:
                return SignalType.BUY, SignalStrength.STRONG
            else:
                return SignalType.BUY, SignalStrength.MODERATE
        elif composite_score <= self.signal_thresholds['strong_sell_threshold']:
            if composite_score <= 15:
                return SignalType.STRONG_SELL, SignalStrength.VERY_STRONG
            else:
                return SignalType.STRONG_SELL, SignalStrength.STRONG
        elif composite_score <= self.signal_thresholds['sell_threshold']:
            if composite_score <= 30:
                return SignalType.SELL, SignalStrength.STRONG
            else:
                return SignalType.SELL, SignalStrength.MODERATE
        else:
            return SignalType.HOLD, SignalStrength.WEAK

    def _calculate_confidence(self, signals: Dict[str, Any], composite_score: float) -> float:
        """计算信号置信度"""
        try:
            confidence_factors = []

            # 1. 基础置信度（基于综合评分）
            base_confidence = abs(composite_score - 50) / 50  # 0-1之间
            confidence_factors.append(base_confidence)

            # 2. 成交量确认加分
            if signals.get('volume_confirmed', False):
                confidence_factors.append(0.8)

            # 3. 多周期共振加分
            if signals.get('multi_timeframe_resonance', False):
                resonance_strength = signals.get('resonance_strength', 0.5)
                confidence_factors.append(0.6 + 0.4 * resonance_strength)

            # 4. 突破确认加分
            if signals.get('bb_breakout_confirmed', False) or signals.get('macd_zero_cross_confirmed', False):
                confidence_factors.append(0.7)

            # 5. 背离警告减分
            divergence_penalty = 0
            if signals.get('macd_divergence') in [-1, 1]:
                divergence_penalty += 0.2
            if signals.get('rsi_divergence') in [-1, 1]:
                divergence_penalty += 0.2

            # 6. 趋势一致性加分
            trend_consistency = self._calculate_trend_consistency(signals)
            confidence_factors.append(trend_consistency)

            # 计算最终置信度
            if confidence_factors:
                avg_confidence = np.mean(confidence_factors)
                final_confidence = max(0, min(1, avg_confidence - divergence_penalty))
            else:
                final_confidence = 0.5

            return final_confidence

        except Exception as e:
            logger.error(f"计算置信度失败: {e}")
            return 0.5

    def _calculate_trend_consistency(self, signals: Dict[str, Any]) -> float:
        """计算趋势一致性"""
        try:
            bullish_count = 0
            bearish_count = 0
            total_count = 0

            # 检查主要趋势指标
            trend_signals = [
                ('ma_bullish', True, False),
                ('macd_signal', 1, -1),
                ('rsi_signal', 1, -1),
                ('adx_direction', 1, -1),
                ('kdj_signal', 1, -1)
            ]

            for signal_name, bullish_value, bearish_value in trend_signals:
                if signal_name in signals:
                    value = signals[signal_name]
                    if value == bullish_value:
                        bullish_count += 1
                    elif value == bearish_value:
                        bearish_count += 1
                    total_count += 1

            if total_count == 0:
                return 0.5

            # 计算一致性
            max_direction = max(bullish_count, bearish_count)
            consistency = max_direction / total_count

            return consistency

        except Exception as e:
            logger.error(f"计算趋势一致性失败: {e}")
            return 0.5

    def _check_confirmations(self, signals: Dict[str, Any], df: pd.DataFrame) -> Dict[str, bool]:
        """检查各种确认条件"""
        try:
            confirmations = {
                'multi_timeframe': False,
                'volume': False,
                'breakout': False,
                'divergence': False
            }

            # 多周期确认
            confirmations['multi_timeframe'] = signals.get('multi_timeframe_resonance', False)

            # 成交量确认
            volume_ratio = signals.get('volume_ratio', 1.0)
            confirmations['volume'] = volume_ratio > self.signal_thresholds['volume_confirmation_ratio']

            # 突破确认
            bb_confirmed = signals.get('bb_breakout_confirmed', False)
            macd_confirmed = signals.get('macd_zero_cross_confirmed', False)
            confirmations['breakout'] = bb_confirmed or macd_confirmed

            # 背离警告
            macd_div = signals.get('macd_divergence', 0)
            rsi_div = signals.get('rsi_divergence', 0)
            confirmations['divergence'] = macd_div != 0 or rsi_div != 0

            return confirmations

        except Exception as e:
            logger.error(f"检查确认条件失败: {e}")
            return {
                'multi_timeframe': False,
                'volume': False,
                'breakout': False,
                'divergence': False
            }

    def get_signal_summary(self, signal: EnhancedTradingSignal) -> str:
        """获取信号摘要描述"""
        if signal is None:
            return "无交易信号"

        summary_parts = [
            f"信号类型: {signal.signal_type.value}",
            f"信号强度: {signal.strength.value}",
            f"置信度: {signal.confidence:.2%}",
            f"入场价: {signal.entry_price:.2f}",
            f"止损价: {signal.stop_loss:.2f}",
            f"止盈价: {signal.take_profit:.2f}",
            f"建议仓位: {signal.position_size}股",
            f"风险等级: {signal.risk_level.value}"
        ]

        if signal.supporting_factors:
            summary_parts.append(f"支持因子: {', '.join(signal.supporting_factors[:3])}")

        if signal.warning_factors:
            summary_parts.append(f"风险因子: {', '.join(signal.warning_factors[:3])}")

        return " | ".join(summary_parts)
