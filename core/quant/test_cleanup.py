#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
代码清理验证脚本
用于验证删除旧方法后的代码是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试增强版模块导入
        from tasks.tools.enhanced_signal_generator import EnhancedSignalGenerator, SignalType, SignalStrength
        from tasks.tools.enhanced_risk_control import EnhancedRiskControl, RiskLevel
        from tasks.tools.technical_indicators import TechnicalIndicators
        print("✅ 增强版模块导入成功")
        
        # 测试基本初始化
        generator = EnhancedSignalGenerator()
        risk_control = EnhancedRiskControl()
        indicators = TechnicalIndicators()
        print("✅ 增强版组件初始化成功")
        
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_signal_types():
    """测试信号类型枚举"""
    print("\n🔍 测试信号类型...")
    
    try:
        from tasks.tools.enhanced_signal_generator import SignalType, SignalStrength
        from tasks.tools.enhanced_risk_control import RiskLevel
        
        # 测试信号类型
        signal_types = [SignalType.STRONG_BUY, SignalType.BUY, SignalType.HOLD, SignalType.SELL, SignalType.STRONG_SELL]
        print(f"✅ 信号类型: {[st.value for st in signal_types]}")
        
        # 测试信号强度
        strengths = [SignalStrength.VERY_WEAK, SignalStrength.WEAK, SignalStrength.MODERATE, SignalStrength.STRONG, SignalStrength.VERY_STRONG]
        print(f"✅ 信号强度: {[s.value for s in strengths]}")
        
        # 测试风险等级
        risk_levels = [RiskLevel.VERY_LOW, RiskLevel.LOW, RiskLevel.MEDIUM, RiskLevel.HIGH, RiskLevel.VERY_HIGH]
        print(f"✅ 风险等级: {[r.value for r in risk_levels]}")
        
        return True
    except Exception as e:
        print(f"❌ 信号类型测试失败: {e}")
        return False

def test_enhanced_params():
    """测试增强算法参数"""
    print("\n🔍 测试增强算法参数...")
    
    try:
        from tasks.tools.technical_indicators import TechnicalIndicators
        from tasks.tools.enhanced_risk_control import EnhancedRiskControl
        from tasks.tools.enhanced_signal_generator import EnhancedSignalGenerator
        
        # 测试技术指标参数
        indicators = TechnicalIndicators()
        if hasattr(indicators, 'enhanced_params'):
            print(f"✅ 技术指标增强参数: {indicators.enhanced_params}")
        
        # 测试风险控制参数
        risk_control = EnhancedRiskControl()
        if hasattr(risk_control, 'risk_params'):
            print(f"✅ 风险控制参数: {risk_control.risk_params}")
        
        # 测试信号生成器参数
        generator = EnhancedSignalGenerator()
        if hasattr(generator, 'signal_weights'):
            print(f"✅ 信号权重配置: {generator.signal_weights}")
        if hasattr(generator, 'signal_thresholds'):
            print(f"✅ 信号阈值配置: {generator.signal_thresholds}")
        
        return True
    except Exception as e:
        print(f"❌ 参数测试失败: {e}")
        return False

def test_method_availability():
    """测试关键方法是否可用"""
    print("\n🔍 测试关键方法可用性...")
    
    try:
        from tasks.tools.technical_indicators import TechnicalIndicators
        from tasks.tools.enhanced_risk_control import EnhancedRiskControl
        from tasks.tools.enhanced_signal_generator import EnhancedSignalGenerator
        
        indicators = TechnicalIndicators()
        risk_control = EnhancedRiskControl()
        generator = EnhancedSignalGenerator()
        
        # 测试技术指标方法
        indicator_methods = [
            'calculate_ma_signals',
            'calculate_macd_signals', 
            'calculate_rsi_signals',
            'calculate_bollinger_bands',
            'calculate_adx_signals'
        ]
        
        for method in indicator_methods:
            if hasattr(indicators, method):
                print(f"✅ 技术指标方法: {method}")
            else:
                print(f"❌ 缺少方法: {method}")
        
        # 测试风险控制方法
        risk_methods = [
            'calculate_dynamic_stop_loss',
            'calculate_dynamic_take_profit',
            'calculate_position_size',
            'assess_risk_level'
        ]
        
        for method in risk_methods:
            if hasattr(risk_control, method):
                print(f"✅ 风险控制方法: {method}")
            else:
                print(f"❌ 缺少方法: {method}")
        
        # 测试信号生成方法
        generator_methods = [
            'generate_enhanced_signal',
            'get_signal_summary'
        ]
        
        for method in generator_methods:
            if hasattr(generator, method):
                print(f"✅ 信号生成方法: {method}")
            else:
                print(f"❌ 缺少方法: {method}")
        
        return True
    except Exception as e:
        print(f"❌ 方法测试失败: {e}")
        return False

def test_data_structures():
    """测试数据结构"""
    print("\n🔍 测试数据结构...")
    
    try:
        from tasks.tools.enhanced_signal_generator import EnhancedTradingSignal, SignalType, SignalStrength
        from tasks.tools.enhanced_risk_control import RiskLevel
        
        # 创建测试信号
        test_signal = EnhancedTradingSignal(
            signal_type=SignalType.BUY,
            strength=SignalStrength.STRONG,
            confidence=0.85,
            entry_price=10.50,
            stop_loss=9.50,
            take_profit=12.00,
            position_size=1000,
            risk_level=RiskLevel.MEDIUM,
            supporting_factors=["均线金叉", "成交量放大"],
            warning_factors=["接近阻力位"],
            multi_timeframe_confirmed=True,
            volume_confirmed=True,
            breakout_confirmed=False,
            divergence_warning=False
        )
        
        # 测试转换为字典
        signal_dict = test_signal.to_dict()
        print(f"✅ 信号数据结构转换成功: {len(signal_dict)} 个字段")
        
        # 验证关键字段
        required_fields = ['signal_type', 'strength', 'confidence', 'entry_price', 'stop_loss', 'take_profit']
        for field in required_fields:
            if field in signal_dict:
                print(f"✅ 包含字段: {field} = {signal_dict[field]}")
            else:
                print(f"❌ 缺少字段: {field}")
        
        return True
    except Exception as e:
        print(f"❌ 数据结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始代码清理验证测试")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_signal_types,
        test_enhanced_params,
        test_method_availability,
        test_data_structures
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！代码清理成功，增强版算法可正常使用")
        return True
    else:
        print("⚠️ 部分测试失败，请检查代码")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
