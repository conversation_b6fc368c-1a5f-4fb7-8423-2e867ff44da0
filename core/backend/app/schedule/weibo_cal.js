'use strict';

const BaseSchedule = require('../utils/base-schedule');

/**
 * 微博日历发布任务
 * 定期发布日历事件或随机语录到微博平台
 */
class WeiboCalSchedule extends BaseSchedule {
  constructor() {
    super({
      taskName: '微博日历发布任务',
      configKey: 'scheduleWB',
      skipLocal: true,
      notifyOnError: true,
      requiredServices: [ 'service.weibo', 'service.posts' ],
      maxRetries: 3,
      retryDelay: 5000,
    });
  }

  async run(ctx) {
    const stats = {
      contentType: null,
      contentLength: 0,
      published: false,
      calendarEvents: 0,
      errors: 0,
    };

    try {
      ctx.logger.info('开始执行微博日历发布任务');

      // 检查微博服务是否可用
      if (!ctx.service.weibo || !ctx.service.weibo.send_text) {
        throw new Error('微博服务不可用');
      }

      // 尝试获取日历内容
      const calendarContent = await this.getCalendarContent(ctx, stats);

      let contentToPublish = null;

      if (calendarContent) {
        // 优先发布日历内容
        contentToPublish = calendarContent;
        stats.contentType = 'calendar';
      } else {
        // 如果没有日历内容，发布随机语录
        contentToPublish = await this.getRandomSaying(ctx);
        stats.contentType = 'saying';
      }

      if (!contentToPublish) {
        throw new Error('无法获取发布内容');
      }

      // 处理内容长度和格式
      ctx.logger.info('原始内容:', contentToPublish);
      const processedContent = this.processContent(contentToPublish);
      ctx.logger.info('处理后内容:', processedContent);
      stats.contentLength = processedContent.length;

      // 发布到微博
      await this.publishToWeibo(ctx, processedContent);
      stats.published = true;

      ctx.logger.info(`微博发布成功: 类型 ${stats.contentType}, 长度 ${stats.contentLength}`);

      await this.recordStats(ctx, stats);
      return stats;

    } catch (error) {
      stats.errors++;
      ctx.logger.error('微博日历发布任务执行失败:', error);

      await this.sendNotification(
        ctx,
        '微博发布失败',
        `微博日历发布任务执行失败: ${error.message}`,
        'error'
      );

      throw error;
    }
  }

  /**
   * 获取日历内容
   * @param ctx
   * @param stats
   */
  async getCalendarContent(ctx, stats) {
    try {
      if (!ctx.service.calendars) {
        ctx.logger.info('日历服务不可用，跳过日历内容获取');
        return null;
      }

      const today = this.formatDate(new Date(), 'YYYYMMDD');
      const calendar = await ctx.service.calendars.find({
        type: 'currentday',
        date: today,
      });

      if (!calendar ||
          !calendar.calendarData ||
          !calendar.calendarData[0] ||
          !calendar.calendarData[0].calendarEvents ||
          calendar.calendarData[0].calendarEvents.length === 0) {
        ctx.logger.info('今日无日历事件');
        return null;
      }

      const events = calendar.calendarData[0].calendarEvents;
      stats.calendarEvents = events.length;

      // 构建日历内容
      let content = '今日财经日历：\n';

      events.forEach((element, index) => {
        if (element && element.event && element.event.content) {
          content += `${index + 1}. `;

          // 添加重要性标记
          const related = element.event.related || 0;
          if (related > 0) {
            content += `${'★'.repeat(Math.min(related, 3))} `;
          }

          // 清理事件内容中的HTML标签
          let eventContent = element.event.content;
          if (typeof eventContent === 'string') {
            // 移除HTML标签
            eventContent = eventContent.replace(/<[^>]*>/g, '');
            // 移除HTML实体编码
            eventContent = eventContent
              .replace(/&amp;/g, '&')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&quot;/g, '"')
              .replace(/&#39;/g, "'")
              // .replace(/&nbsp;/g, ' ');
          }

          content += `${eventContent}\n`;
        }
      });

      // 添加链接
      content += '\n详见 http://finevent.com.cn';

      return content;

    } catch (error) {
      ctx.logger.error('获取日历内容失败:', error);
      return null;
    }
  }

  /**
   * 获取随机语录
   * @param ctx
   */
  async getRandomSaying(ctx) {
    try {
      const post = await ctx.service.posts.getRandomSaying();

      if (!post || !post.content) {
        throw new Error('无法获取随机语录');
      }

      return post.content;

    } catch (error) {
      ctx.logger.error('获取随机语录失败:', error);
      throw error;
    }
  }

  /**
   * 处理发布内容
   * @param content
   */
  processContent(content) {
    if (!content || typeof content !== 'string') {
      return '';
    }

    // 清理内容
    let processedContent = content.trim();
    
    console.log('处理前内容:', processedContent);

    // 彻底移除所有HTML标签，包括自闭合标签
    processedContent = processedContent.replace(/<[^>]*>/g, '');
    console.log('移除HTML标签后:', processedContent);
    
    // 移除HTML实体编码
    processedContent = processedContent
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ');
    console.log('移除HTML实体后:', processedContent);

    // 清理多余的空格和换行
    processedContent = processedContent
      .replace(/\s+/g, ' ')  // 多个空格替换为单个空格
      .replace(/\n\s*\n/g, '\n')  // 空行清理
      .trim();
    console.log('清理空格换行后:', processedContent);

    // 微博字数限制处理（140字符）
    const maxLength = 130; // 留一些余量
    if (processedContent.length > maxLength) {
      processedContent = `${processedContent.substring(0, maxLength)}...`;
    }

    // 移除多余的换行符
    processedContent = processedContent.replace(/\n{3,}/g, '\n\n');
    
    console.log('最终处理结果:', processedContent);
    return processedContent;
  }

  /**
   * 发布到微博
   * @param ctx
   * @param content
   */
  async publishToWeibo(ctx, content) {
    try {
      await this.executeWithRetry(
        async () => {
          await ctx.service.weibo.send_text(content);
        },
        '微博发布',
        ctx
      );

      ctx.logger.info('微博发布成功');

    } catch (error) {
      ctx.logger.error('微博发布失败:', error);
      throw error;
    }
  }

  /**
   * 检查微博服务状态
   * @param ctx
   */
  async checkWeiboService(ctx) {
    try {
      // 这里可以添加微博服务的健康检查
      // 比如检查认证状态、API限制等

      if (!ctx.service.weibo) {
        throw new Error('微博服务未初始化');
      }

      // 可以扩展为检查微博API配置
      const config = await this.getConfig(ctx, 'weibo');
      if (!config) {
        ctx.logger.warn('微博配置不存在');
      }

      return true;

    } catch (error) {
      ctx.logger.error('微博服务检查失败:', error);
      return false;
    }
  }

  /**
   * 获取发布统计
   * @param ctx
   */
  async getPublishStats(ctx) {
    try {
      // 这里可以扩展为从数据库获取发布统计
      const today = this.formatDate(new Date(), 'YYYY-MM-DD');

      return {
        date: today,
        totalPublished: 0, // 可以从数据库查询
        lastPublishTime: null,
        publishType: 'unknown',
      };

    } catch (error) {
      ctx.logger.error('获取发布统计失败:', error);
      return null;
    }
  }
}

// 创建任务实例
const weiboCalTask = new WeiboCalSchedule();

module.exports = {
  schedule: {
    cron: '0 30 07 * * *', // 每天早上7:30执行
    type: 'worker',
    immediate: false,
  },
  async task(ctx) {
    return await weiboCalTask.execute(ctx);
  },
};
