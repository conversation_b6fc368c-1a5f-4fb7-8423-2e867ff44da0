#!/usr/bin/env python3
"""
检查HTML内容，寻找反爬验证的线索
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawler_engine import XueqiuCrawler

async def check_html_content():
    """检查HTML内容"""
    try:
        print("开始检查HTML内容...")
        
        # 创建爬虫实例
        crawler = XueqiuCrawler(enable_anti_detection=True)
        
        # 测试URL
        test_url = "https://xueqiu.com/today"
        
        print(f"测试URL: {test_url}")
        
        # 执行爬取
        result = await crawler.crawl_url(test_url)
        
        if result.success and result.raw_html:
            html_content = result.raw_html
            
            print(f"HTML内容长度: {len(html_content)}")
            
            # 检查是否包含反爬验证相关的内容
            anti_crawl_keywords = [
                "验证", "验证码", "滑块", "滑动", "geetest", "captcha",
                "Access Verification", "Please slide", "complete the verification",
                "unhashable type", "dict", "error"
            ]
            
            print("\n=== 反爬验证检查 ===")
            for keyword in anti_crawl_keywords:
                if keyword.lower() in html_content.lower():
                    print(f"✅ 发现关键词: {keyword}")
                else:
                    print(f"❌ 未发现关键词: {keyword}")
            
            # 检查页面标题和主要内容
            print("\n=== 页面内容分析 ===")
            
            # 查找页面标题
            if "<title>" in html_content:
                title_start = html_content.find("<title>") + 7
                title_end = html_content.find("</title>")
                if title_end > title_start:
                    title = html_content[title_start:title_end]
                    print(f"页面标题: {title}")
            
            # 查找页面主体内容
            if "<body" in html_content:
                body_start = html_content.find("<body")
                body_start = html_content.find(">", body_start) + 1
                body_end = html_content.find("</body>")
                if body_end > body_start:
                    body_content = html_content[body_start:body_end]
                    # 提取文本内容
                    import re
                    text_content = re.sub(r'<[^>]+>', '', body_content)
                    text_content = re.sub(r'\s+', ' ', text_content).strip()
                    print(f"页面文本内容预览: {text_content[:500]}...")
            
            # 检查JavaScript代码
            print("\n=== JavaScript检查 ===")
            if "<script" in html_content:
                script_count = html_content.count("<script")
                print(f"发现 {script_count} 个script标签")
                
                # 查找第一个script标签的内容
                script_start = html_content.find("<script")
                script_start = html_content.find(">", script_start) + 1
                script_end = html_content.find("</script>", script_start)
                if script_end > script_start:
                    script_content = html_content[script_start:script_end]
                    print(f"第一个script内容预览: {script_content[:300]}...")
            else:
                print("未发现script标签")
                
        else:
            print(f"爬取失败: {result.error}")
            
    except Exception as e:
        print(f"检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_html_content())
