#!/usr/bin/env python3
"""
测试JavaScript执行的不同配置和方法
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawler_engine import XueqiuCrawler

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_js_execution_methods():
    """测试不同的JavaScript执行方法"""
    try:
        print("=== 测试JavaScript执行的不同方法 ===")
        
        # 创建爬虫实例
        crawler = XueqiuCrawler(enable_anti_detection=True)
        
        # 测试URL
        test_url = "https://xueqiu.com/today"
        
        print(f"测试URL: {test_url}")
        
        # 方法1：最简单的JavaScript代码
        simple_js = "return 'Hello from JavaScript!';"
        
        print("\n--- 方法1：简单字符串 ---")
        result1 = await crawler._crawl_with_crawl4ai(
            test_url,
            js_code=simple_js,
            page_timeout=30000,
            delay_before_return_html=1
        )
        print(f"结果: {result1.js_result if hasattr(result1, 'js_result') else 'No js_result'}")
        
        # 方法2：函数形式的JavaScript
        function_js = """
        function test() {
            return 'Hello from function!';
        }
        test();
        """
        
        print("\n--- 方法2：函数形式 ---")
        result2 = await crawler._crawl_with_crawl4ai(
            test_url,
            js_code=function_js,
            page_timeout=30000,
            delay_before_return_html=1
        )
        print(f"结果: {result2.js_result if hasattr(result2, 'js_result') else 'No js_result'}")
        
        # 方法3：异步函数形式
        async_js = """
        (async function() {
            await new Promise(resolve => setTimeout(resolve, 1000));
            return 'Hello from async function!';
        })();
        """
        
        print("\n--- 方法3：异步函数形式 ---")
        result3 = await crawler._crawl_with_crawl4ai(
            test_url,
            js_code=async_js,
            page_timeout=30000,
            delay_before_return_html=1
        )
        print(f"结果: {result3.js_result if hasattr(result3, 'js_result') else 'No js_result'}")
        
        # 方法4：DOM操作
        dom_js = """
        const title = document.title;
        const links = document.querySelectorAll('a[href]').length;
        return {title: title, links: links};
        """
        
        print("\n--- 方法4：DOM操作 ---")
        result4 = await crawler._crawl_with_crawl4ai(
            test_url,
            js_code=dom_js,
            page_timeout=30000,
            delay_before_return_html=1
        )
        print(f"结果: {result4.js_result if hasattr(result4, 'js_result') else 'No js_result'}")
        
        # 方法5：使用c4a_script
        c4a_js = """
        return {
            message: 'Hello from c4a_script!',
            timestamp: Date.now(),
            userAgent: navigator.userAgent
        };
        """
        
        print("\n--- 方法5：c4a_script ---")
        result5 = await crawler._crawl_with_crawl4ai(
            test_url,
            js_code=c4a_js,
            page_timeout=30000,
            delay_before_return_html=1
        )
        print(f"结果: {result5.js_result if hasattr(result5, 'js_result') else 'No js_result'}")
        
        # 总结
        print("\n=== 测试总结 ===")
        results = [result1, result2, result3, result4, result5]
        for i, result in enumerate(results, 1):
            has_js = hasattr(result, 'js_result') and result.js_result is not None
            print(f"方法{i}: {'✅ 成功' if has_js else '❌ 失败'}")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_js_execution_methods())
