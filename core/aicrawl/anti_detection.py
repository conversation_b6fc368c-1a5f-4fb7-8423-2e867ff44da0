"""
反检测模块 - 核心反爬虫功能实现
"""
import asyncio
import random
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from urllib.parse import urlparse
import json

from config import AntiDetectionConfig
from slider_verification import SliderVerificationHandler
from shared_types import RequestResult

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

import os
import aiohttp
import asyncio
import random
from typing import List, Dict, Optional

class ProxyManager:
    """代理管理器"""
    
    def __init__(self, proxy_list: List[Dict[str, str]] = None):
        # 优先从环境变量读取代理配置
        self.proxy_pool_url = None
        self.proxy_list = self._get_proxies_from_env()
        # 如果环境变量中没有代理配置，使用传入的代理列表或默认配置
        if not self.proxy_list:
            self.proxy_list = proxy_list or AntiDetectionConfig.PROXY_POOLS
        self.failed_proxies = set()
        self.current_proxy_index = 0
        self.cached_proxies = []  # 缓存从代理池获取的代理
        
        # 启动代理池自动更新任务
        if self.proxy_pool_url:
            self._start_auto_refresh_task()
            # 初始填充一些代理到缓存
            asyncio.create_task(self._preload_proxies())
        
    def _get_proxies_from_env(self) -> List[Dict[str, str]]:
        """从环境变量读取代理配置"""
        proxy_url = os.environ.get('BB_PROXY_URL')
        if proxy_url:
            # 确保代理URL以斜杠结尾
            if not proxy_url.endswith('/'):
                proxy_url += '/'            
            logger.info(f"从环境变量加载代理配置: {proxy_url}")
            
            # 检查是否是代理池URL（包含'/get/'路径）
            if '/get/' in proxy_url:
                self.proxy_pool_url = proxy_url
                return []  # 对于代理池URL，不直接添加到proxy_list，而是在get_proxy时动态获取
            else:
                # 将静态代理添加到列表，同时记录为静态代理URL
                self.static_proxy_url = proxy_url
                return [{"http": proxy_url, "https": proxy_url}]
        return []
        
    async def get_proxy(self) -> Optional[Dict[str, str]]:
        """获取可用代理"""
        # 优先从代理池URL获取代理
        if self.proxy_pool_url:
            return await self._fetch_proxy_from_pool()
            
        # 如果没有设置代理池URL，从本地代理列表获取
        if not self.proxy_list:
            return None
            
        # 修复：将字典类型的代理转换为可哈希的字符串标识符
        available_proxies = []
        for p in self.proxy_list:
            try:
                if isinstance(p, dict):
                    # 对于字典类型的代理，创建字符串标识符
                    proxy_id = self._create_proxy_id(p)
                    if proxy_id not in self.failed_proxies:
                        available_proxies.append(p)
                else:
                    # 对于字符串类型的代理，直接检查
                    if p not in self.failed_proxies:
                        available_proxies.append(p)
            except Exception as e:
                logger.warning(f"处理代理配置时出错: {e}")
                continue
                
        if not available_proxies:
            # 重置失败代理列表
            self.failed_proxies.clear()
            available_proxies = self.proxy_list
            
        proxy = random.choice(available_proxies)
        
        # 确保代理配置格式正确
        if isinstance(proxy, dict):
            # 检查代理配置是否包含必要的键
            if "http" in proxy or "https" in proxy:
                return proxy
            else:
                logger.warning(f"代理配置格式不正确: {proxy}")
                return None
        else:
            # 如果不是字典，转换为标准格式
            proxy_str = str(proxy)
            if not proxy_str.startswith('http://') and not proxy_str.startswith('https://'):
                proxy_str = f'http://{proxy_str}'
            return {"http": proxy_str, "https": proxy_str}
    
    def _create_proxy_id(self, proxy: Dict[str, str]) -> str:
        """为字典类型的代理创建可哈希的标识符"""
        try:
            if "http" in proxy:
                return f"http_{proxy['http']}"
            elif "https" in proxy:
                return f"https_{proxy['https']}"
            else:
                return str(hash(str(proxy)))
        except Exception:
            return str(hash(str(proxy)))
    
    async def _fetch_proxy_from_pool(self) -> Optional[Dict[str, str]]:
        """从代理池获取代理"""
        try:
            # 如果有缓存的代理，优先使用
            if self.cached_proxies:
                return self.cached_proxies.pop()
                
            logger.info(f"从代理池获取新代理: {self.proxy_pool_url}")
            async with aiohttp.ClientSession() as session:
                async with session.get(self.proxy_pool_url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        # 从响应中提取proxy字段
                        if isinstance(data, dict) and 'proxy' in data:
                            proxy_str = data['proxy']
                            # 确保代理地址格式正确
                            if not proxy_str.startswith('http://') and not proxy_str.startswith('https://'):
                                proxy_str = f'http://{proxy_str}'
                            
                            proxy = {"http": proxy_str, "https": proxy_str}
                            logger.info(f"成功获取代理: {proxy}")
                            return proxy
                        else:
                            logger.error(f"代理池返回的数据格式不正确: {data}")
                    else:
                        logger.error(f"代理池请求失败，状态码: {response.status}")
        except Exception as e:
            logger.error(f"从代理池获取代理时发生错误: {e}")
            
        # 如果获取失败，尝试使用备用方案
        # 检查是否有静态代理配置
        if hasattr(self, 'static_proxy_url'):
            logger.info(f"使用静态代理: {self.static_proxy_url}")
            return {"http": self.static_proxy_url, "https": self.static_proxy_url}
        
        return None
        
    def _start_auto_refresh_task(self):
        """启动自动刷新代理池任务"""
        async def refresh_task():
            while True:
                try:
                    # 每10分钟刷新一次代理缓存
                    await asyncio.sleep(600)  # 10分钟 = 600秒
                    await self._refresh_proxy_cache()
                except Exception as e:
                    logger.error(f"自动刷新代理缓存时发生错误: {e}")
        
        # 创建并启动后台任务
        asyncio.create_task(refresh_task())
        logger.info("代理池自动更新任务已启动")
        
    async def _refresh_proxy_cache(self):
        """刷新代理缓存"""
        logger.info("开始刷新代理缓存")
        
        # 清空旧的缓存
        self.cached_proxies.clear()
        
        # 预加载更多的新代理，增加成功率
        for _ in range(5):  # 预加载5个代理
            proxy = await self._direct_fetch_proxy_from_pool()
            if proxy:
                self.cached_proxies.append(proxy)
                await asyncio.sleep(0.5)  # 减少延迟，加快刷新速度
        
        # 如果代理池获取失败，尝试使用备用代理
        if not self.cached_proxies and hasattr(self, 'static_proxy_url'):
            logger.info("代理池获取失败，使用备用静态代理")
            self.cached_proxies.append({"http": self.static_proxy_url, "https": self.static_proxy_url})
        
        logger.info(f"代理缓存刷新完成，当前缓存数量: {len(self.cached_proxies)}")
        
    async def _direct_fetch_proxy_from_pool(self) -> Optional[Dict[str, str]]:
        """直接从代理池获取代理（不使用缓存）"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.proxy_pool_url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if isinstance(data, dict) and 'proxy' in data:
                            proxy_str = data['proxy']
                            if not proxy_str.startswith('http://') and not proxy_str.startswith('https://'):
                                proxy_str = f'http://{proxy_str}'
                            return {"http": proxy_str, "https": proxy_str}
        except Exception as e:
            logger.error(f"直接从代理池获取代理时发生错误: {e}")
        return None
        
    async def _preload_proxies(self):
        """预加载代理到缓存"""
        logger.info("开始预加载代理")
        for _ in range(2):  # 预加载2个代理
            proxy = await self._direct_fetch_proxy_from_pool()
            if proxy:
                self.cached_proxies.append(proxy)
                await asyncio.sleep(1)
        logger.info(f"预加载代理完成，缓存数量: {len(self.cached_proxies)}")
    
    def mark_proxy_failed(self, proxy: Dict[str, str]):
        """标记代理失败"""
        try:
            if isinstance(proxy, dict):
                # 对于字典类型的代理，创建字符串标识符
                proxy_id = self._create_proxy_id(proxy)
                self.failed_proxies.add(proxy_id)
                logger.warning(f"代理 {proxy_id} 已被标记为失败")
            else:
                # 对于字符串类型的代理，直接添加
                proxy_str = str(proxy)
                self.failed_proxies.add(proxy_str)
                logger.warning(f"代理 {proxy_str} 已被标记为失败")
            
            # 如果失败代理数量过多，清空失败列表
            if len(self.failed_proxies) > len(self.proxy_list) * 0.8:
                logger.info("失败代理数量过多，清空失败列表")
                self.failed_proxies.clear()
            
            # 强制刷新代理缓存
            asyncio.create_task(self._refresh_proxy_cache())
                
        except Exception as e:
            logger.warning(f"标记代理失败时出错: {e}")
    
    def reset_failed_proxies(self):
        """重置失败代理列表"""
        self.failed_proxies.clear()

class HeaderManager:
    """请求头管理器"""
    
    def __init__(self):
        self.session_headers = {}
        
    def get_headers(self, url: str = None) -> Dict[str, str]:
        """获取请求头"""
        headers = AntiDetectionConfig.get_random_headers()
        
        # 为特定域名添加Referer
        if url:
            parsed_url = urlparse(url)
            if "xueqiu.com" in parsed_url.netloc:
                headers["Referer"] = "https://xueqiu.com/"
                headers["Origin"] = "https://xueqiu.com"
                
        return headers
    
    def update_session_headers(self, new_headers: Dict[str, str]):
        """更新会话头"""
        self.session_headers.update(new_headers)

class FrequencyController:
    """频率控制器"""
    
    def __init__(self):
        self.last_request_time = 0
        self.request_count = 0
        self.error_count = 0
        self.start_time = time.time()
        
    async def wait_if_needed(self):
        """根据频率控制等待"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        # 基础延迟
        min_delay = AntiDetectionConfig.REQUEST_DELAYS["min_delay"]
        
        # 如果请求过于频繁，增加延迟
        if time_since_last < min_delay:
            delay = min_delay - time_since_last
            
            # 如果有错误，增加额外延迟
            if self.error_count > 0:
                delay += AntiDetectionConfig.REQUEST_DELAYS["error_delay"]
                
            logger.info(f"频率控制: 等待 {delay:.2f} 秒")
            await asyncio.sleep(delay)
            
        # 随机延迟，模拟人类行为
        random_delay = AntiDetectionConfig.get_random_delay()
        await asyncio.sleep(random_delay)
        
        self.last_request_time = time.time()
        self.request_count += 1
        
    def record_error(self):
        """记录错误"""
        self.error_count += 1
        
    def record_success(self):
        """记录成功"""
        self.error_count = max(0, self.error_count - 1)

class BrowserConfigManager:
    """浏览器配置管理器"""
    
    @staticmethod
    def get_stealth_config() -> Dict[str, Any]:
        """获取隐身浏览器配置"""
        config = AntiDetectionConfig.BROWSER_CONFIG.copy()
        
        # 添加反检测参数
        config.update({
            "args": [
                "--no-sandbox",
                "--disable-setuid-sandbox", 
                "--disable-dev-shm-usage",
                "--disable-accelerated-2d-canvas",
                "--no-first-run",
                "--no-zygote",
                "--disable-gpu",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--user-agent=" + AntiDetectionConfig.get_random_user_agent()
            ],
            "ignore_default_args": ["--enable-automation"],
            "ignore_https_errors": True
        })
        
        return config
    
    @staticmethod
    def get_mobile_config() -> Dict[str, Any]:
        """获取移动端配置"""
        config = BrowserConfigManager.get_stealth_config()
        config["viewport"] = {"width": 375, "height": 667}
        
        # 移动端User-Agent
        mobile_ua = "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1"
        
        # 更新args中的user-agent
        for i, arg in enumerate(config["args"]):
            if arg.startswith("--user-agent="):
                config["args"][i] = f"--user-agent={mobile_ua}"
                break
                
        return config

class RetryManager:
    """重试管理器"""
    
    def __init__(self):
        self.retry_config = AntiDetectionConfig.RETRY_CONFIG
        
    async def execute_with_retry(self, func, *args, **kwargs) -> RequestResult:
        """带重试的执行函数"""
        last_error = None
        
        for attempt in range(self.retry_config["max_retries"] + 1):
            try:
                result = await func(*args, **kwargs)
                if result.success:
                    return result
                    
                last_error = result.error
                
            except Exception as e:
                last_error = str(e)
                logger.error(f"执行失败 (尝试 {attempt + 1}): {e}")
                
            if attempt < self.retry_config["max_retries"]:
                delay = self.retry_config["retry_delay"] * (
                    self.retry_config["backoff_factor"] ** attempt
                )
                logger.info(f"等待 {delay} 秒后重试...")
                await asyncio.sleep(delay)
                
        return RequestResult(success=False, error=last_error)

class AntiDetectionManager:
    """反检测管理器 - 统一管理所有反检测功能"""
    
    def __init__(self):
        self.proxy_manager = ProxyManager()
        self.header_manager = HeaderManager()
        self.frequency_controller = FrequencyController()
        self.retry_manager = RetryManager()
        self.slider_handler = SliderVerificationHandler()
        self.valid_cookies = {}  # 存储有效的cookies
        self.cookies_expiry = {}  # 存储cookies的过期时间
        
    def _get_domain(self, url: str) -> str:
        """从URL中提取域名"""
        parsed_url = urlparse(url)
        return parsed_url.netloc
    
    def _is_cookies_valid(self, domain: str) -> bool:
        """检查域名对应的cookies是否有效"""
        if domain not in self.valid_cookies:
            return False
        
        # 检查cookies是否过期
        expiry_time = self.cookies_expiry.get(domain, 0)
        return time.time() < expiry_time
    
    def _update_cookies(self, domain: str, cookies: Dict[str, str], expiry_seconds: int = 3600):
        """更新域名对应的cookies"""
        self.valid_cookies[domain] = cookies
        self.cookies_expiry[domain] = time.time() + expiry_seconds
        logger.info(f"已更新域名 {domain} 的cookies，有效期至 {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.cookies_expiry[domain]))}")
    
    def _get_cookies_for_request(self, url: str) -> Dict[str, str]:
        """获取请求需要的cookies"""
        domain = self._get_domain(url)
        if self._is_cookies_valid(domain):
            return self.valid_cookies[domain]
        return {}
    
    async def prepare_request(self, url: str) -> Dict[str, Any]:
        """准备请求参数"""
        # 频率控制
        await self.frequency_controller.wait_if_needed()
        
        # 获取代理和请求头
        proxy = await self.proxy_manager.get_proxy()
        headers = self.header_manager.get_headers(url)
        
        # 获取浏览器配置
        browser_config = BrowserConfigManager.get_stealth_config()
        
        # 添加cookies
        cookies = self._get_cookies_for_request(url)
        
        return {
            "proxy": proxy,
            "headers": headers,
            "browser_config": browser_config,
            "cookies": cookies
        }
        
    async def handle_slider_verification(self, url: str, html_content: str = None) -> Dict[str, str]:
        """
        处理滑块验证
        
        参数:
            url: 网页URL
            html_content: 网页HTML内容（可选）
        
        返回:
            Dict[str, str]: 验证后的cookies，如果验证失败则返回空字典
        """
        domain = self._get_domain(url)
        
        # 检查是否已经有有效的cookies
        if self._is_cookies_valid(domain):
            return self.valid_cookies[domain]
        
        # 检查是否需要滑块验证
        if html_content and not self.slider_handler.is_verification_required(html_content, url):
            logger.info(f"页面 {url} 不需要滑块验证")
            return {}
        
        # 尝试解决滑块验证并获取有效cookies
        logger.info(f"开始处理页面 {url} 的滑块验证")
        cookies = await self.slider_handler.solve_and_get_valid_cookies(url)
        
        if cookies:
            # 更新cookies并设置过期时间
            self._update_cookies(domain, cookies)
            logger.info(f"成功获取域名 {domain} 的有效cookies")
        else:
            logger.warning(f"未能获取域名 {domain} 的有效cookies")
            
        return cookies
    
    def handle_request_result(self, result: RequestResult, proxy: Dict[str, str] = None):
        """处理请求结果"""
        if result.success:
            self.frequency_controller.record_success()
        else:
            self.frequency_controller.record_error()
            if proxy:
                self.proxy_manager.mark_proxy_failed(proxy)
                
    async def execute_request(self, request_func, *args, **kwargs) -> RequestResult:
        """执行带反检测的请求，包含滑块验证处理和IP封禁处理"""
        max_ip_retries = 3  # 最大IP重试次数
        
        for ip_attempt in range(max_ip_retries):
            try:
                # 获取当前请求参数
                request_params = await self.prepare_request(kwargs.get('url', ''))
                kwargs.update(request_params)
                
                # 执行请求
                result = await self.retry_manager.execute_with_retry(request_func, *args, **kwargs)
                
                # 检查是否成功
                if result.success:
                    self.frequency_controller.record_success()
                    return result
                
                # 检查是否是因为IP被封禁
                is_blocked = self._is_ip_blocked(result, kwargs.get('url', ''))
                
                if is_blocked and ip_attempt < max_ip_retries - 1:
                    logger.warning(f"IP可能被封禁 (尝试 {ip_attempt + 1}/{max_ip_retries})，尝试切换代理和重新验证")
                    
                    # 标记当前代理为失败
                    if 'proxy' in kwargs and kwargs['proxy']:
                        self.proxy_manager.mark_proxy_failed(kwargs['proxy'])
                        logger.info(f"已将当前代理标记为失败")
                    
                    # 强制刷新代理缓存
                    await self.proxy_manager._refresh_proxy_cache()
                    
                    # 获取新的代理
                    new_proxy = await self.proxy_manager.get_proxy()
                    if new_proxy:
                        logger.info(f"获取到新代理: {new_proxy}")
                        kwargs['proxy'] = new_proxy
                    
                    # 尝试重新获取cookies
                    if kwargs.get('url'):
                        cookies = await self.handle_slider_verification(kwargs['url'])
                        if cookies:
                            kwargs['cookies'] = cookies
                            logger.info(f"重新获取到cookies")
                    
                    # 增加延迟，避免触发更严格的反爬
                    delay = 10 + (ip_attempt * 5)  # 递增延迟：10s, 15s, 20s
                    logger.info(f"等待 {delay} 秒后重试...")
                    await asyncio.sleep(delay)
                    
                    continue
                else:
                    # 不是IP封禁问题，或者已达到最大重试次数
                    break
                    
            except Exception as e:
                logger.error(f"执行请求时发生错误: {e}")
                if ip_attempt < max_ip_retries - 1:
                    await asyncio.sleep(5)
                    continue
                else:
                    return RequestResult(success=False, error=str(e))
        
        # 所有重试都失败了
        self.frequency_controller.record_error()
        return result if 'result' in locals() else RequestResult(success=False, error="所有IP重试都失败了")
    
    def _is_ip_blocked(self, result: RequestResult, url: str) -> bool:
        """检查是否IP被封禁"""
        # 检查状态码
        if hasattr(result, 'status_code'):
            blocked_status_codes = [403, 405, 429, 503, 502, 504]
            if result.status_code in blocked_status_codes:
                logger.warning(f"检测到IP封禁状态码: {result.status_code} 对于URL: {url}")
                return True
        
        # 检查响应内容
        if hasattr(result, 'data') and result.data:
            html_content = str(result.data).lower()
            blocked_indicators = [
                'access denied', 'forbidden', 'blocked', 'rate limited',
                'too many requests', 'ip blocked', 'access restricted',
                '405', '403', '429', '503'
            ]
            
            for indicator in blocked_indicators:
                if indicator in html_content:
                    logger.warning(f"检测到IP封禁指示符: {indicator} 对于URL: {url}")
                    return True
        
        # 检查错误信息
        if hasattr(result, 'error') and result.error:
            error_msg = str(result.error).lower()
            if any(word in error_msg for word in ['blocked', 'forbidden', 'denied', 'rate limit']):
                logger.warning(f"检测到IP封禁错误信息: {result.error} 对于URL: {url}")
                return True
        
        return False
