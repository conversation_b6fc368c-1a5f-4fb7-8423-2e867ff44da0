#!/usr/bin/env python3
"""
使用Playwright绕过反爬验证的爬虫
"""
import asyncio
import logging
import sys
import os
import time
import random

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PlaywrightCrawler:
    """使用Playwright的爬虫"""
    
    def __init__(self):
        self.browser = None
        self.page = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=False,  # 显示浏览器，便于调试
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        # 创建页面
        self.page = await self.browser.new_page()
        
        # 设置用户代理
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        return self
        
    async def __aexit__(self, self_type, value, traceback):
        """异步上下文管理器出口"""
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
    
    async def crawl_with_js(self, url: str, max_scroll: int = 50, max_load_more: int = 30):
        """使用JavaScript爬取页面"""
        try:
            print(f"开始访问: {url}")
            
            # 访问页面
            await self.page.goto(url, wait_until='networkidle')
            await self.page.wait_for_timeout(3000)
            
            # 检查是否被重定向到验证页面
            title = await self.page.evaluate("() => document.title")
            print(f"页面标题: {title}")
            
            if "Verification" in title:
                print("⚠️ 页面被重定向到验证页面，尝试绕过...")
                # 等待一段时间，然后刷新页面
                await self.page.wait_for_timeout(5000)
                await self.page.reload()
                await self.page.wait_for_timeout(3000)
                
                title = await self.page.evaluate("() => document.title")
                print(f"刷新后页面标题: {title}")
            
            # 执行JavaScript爬取逻辑
            result = await self.page.evaluate(f"""
                (async function() {{
                    console.log('🚀 开始Playwright爬取...');
                    
                    const links = new Set();
                    let totalClicks = 0;
                    const maxClicks = {max_load_more};
                    let scrollAttempts = 0;
                    const maxScrollAttempts = {max_scroll};
                    
                    // 提取链接的函数
                    function extractLinks() {{
                        const currentLinks = new Set();
                        document.querySelectorAll('a[href]').forEach(link => {{
                            const href = link.getAttribute('href');
                            if (href) {{
                                let fullUrl = href;
                                if (href.startsWith('/')) {{
                                    fullUrl = 'https://xueqiu.com' + href;
                                }} else if (!href.startsWith('http')) {{
                                    fullUrl = 'https://xueqiu.com/' + href;
                                }}
                                
                                if (fullUrl.match(/^https:\/\/xueqiu\.com\/\\d+\/\\d+$/)) {{
                                    currentLinks.add(fullUrl);
                                }}
                            }}
                        }});
                        return currentLinks;
                    }}
                    
                    // 智能滚动
                    async function smartScroll() {{
                        for (let i = 0; i < maxScrollAttempts; i++) {{
                            scrollAttempts++;
                            
                            const beforeHeight = document.body.scrollHeight;
                            const beforeLinks = links.size;
                            
                            // 分阶段滚动
                            window.scrollTo(0, document.body.scrollHeight * 0.3);
                            await new Promise(resolve => setTimeout(resolve, 800));
                            
                            window.scrollTo(0, document.body.scrollHeight * 0.6);
                            await new Promise(resolve => setTimeout(resolve, 800));
                            
                            window.scrollTo(0, document.body.scrollHeight * 0.9);
                            await new Promise(resolve => setTimeout(resolve, 800));
                            
                            window.scrollTo(0, document.body.scrollHeight);
                            await new Promise(resolve => setTimeout(resolve, 1500));
                            
                            // 提取新链接
                            const newLinks = extractLinks();
                            newLinks.forEach(link => links.add(link));
                            
                            const addedCount = links.size - beforeLinks;
                            console.log(`滚动 ${{i+1}}: 新增 ${{addedCount}} 个链接，总计 ${{links.size}}`);
                            
                            if (addedCount === 0) {{
                                console.log('连续无新链接，停止滚动');
                                break;
                            }}
                            
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }}
                    }}
                    
                    // 查找并点击加载更多按钮
                    async function clickLoadMore() {{
                        const loadMoreBtn = document.querySelector('a[href=""]');
                        if (loadMoreBtn && loadMoreBtn.textContent.includes('加载更多')) {{
                            console.log('找到加载更多按钮');
                            loadMoreBtn.click();
                            await new Promise(resolve => setTimeout(resolve, 3000));
                            return true;
                        }}
                        return false;
                    }}
                    
                    // 1. 初始提取
                    const initialLinks = extractLinks();
                    initialLinks.forEach(link => links.add(link));
                    console.log('初始链接数量:', links.size);
                    
                    // 2. 执行滚动
                    await smartScroll();
                    
                    // 3. 点击加载更多
                    while (totalClicks < maxClicks) {{
                        if (await clickLoadMore()) {{
                            totalClicks++;
                            console.log(`点击加载更多 ${{totalClicks}}/${{maxClicks}}`);
                            
                            // 再次滚动
                            await smartScroll();
                        }} else {{
                            console.log('未找到加载更多按钮');
                            break;
                        }}
                    }}
                    
                    const finalLinks = Array.from(links);
                    console.log('🎉 爬取完成！');
                    console.log(`最终链接数量: ${{finalLinks.length}}`);
                    
                    return finalLinks;
                }})();
            """)
            
            print(f"JavaScript执行完成，结果类型: {type(result)}")
            if result:
                print(f"提取到 {len(result)} 个链接")
                if len(result) > 0:
                    print(f"前5个链接: {result[:5]}")
            else:
                print("⚠️ JavaScript执行结果为空")
                
            return result
            
        except Exception as e:
            print(f"爬取过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return []

async def main():
    """主函数"""
    print("=== Playwright爬虫测试 ===")
    
    async with PlaywrightCrawler() as crawler:
        result = await crawler.crawl_with_js(
            "https://xueqiu.com/today",
            max_scroll=30,
            max_load_more=20
        )
        
        print(f"\n=== 最终结果 ===")
        print(f"成功提取 {len(result)} 个链接")

if __name__ == "__main__":
    asyncio.run(main())
