#!/usr/bin/env python3
"""
测试反检测功能
验证IP封禁检测和代理重试机制
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from anti_detection import AntiDetectionManager
from shared_types import RequestResult

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockRequestResult:
    """模拟请求结果"""
    def __init__(self, success: bool, data=None, error=None, status_code=None):
        self.success = success
        self.data = data
        self.error = error
        self.status_code = status_code

async def mock_request_func(url: str, **kwargs):
    """模拟请求函数"""
    logger.info(f"模拟请求: {url}")
    logger.info(f"请求参数: {kwargs}")
    
    # 模拟不同的响应情况
    if "blocked" in url:
        # 模拟IP被封禁
        return MockRequestResult(
            success=False,
            data="<html><title>405</title><body>Access Denied</body></html>",
            error="IP blocked",
            status_code=405
        )
    elif "success" in url:
        # 模拟成功响应
        return MockRequestResult(
            success=True,
            data="<html><title>Success</title><body>Content loaded</body></html>",
            status_code=200
        )
    else:
        # 模拟一般错误
        return MockRequestResult(
            success=False,
            data="<html><title>Error</title><body>Page not found</body></html>",
            error="Page not found",
            status_code=404
        )

async def test_anti_detection():
    """测试反检测功能"""
    logger.info("=== 开始测试反检测功能 ===")
    
    # 创建反检测管理器
    anti_detection = AntiDetectionManager()
    
    # 测试1: IP被封禁检测
    logger.info("\n--- 测试1: IP被封禁检测 ---")
    blocked_result = MockRequestResult(
        success=False,
        data="<html><title>405</title><body>Access Denied</body></html>",
        error="IP blocked",
        status_code=405
    )
    
    is_blocked = anti_detection._is_ip_blocked(blocked_result, "https://xueqiu.com/today")
    logger.info(f"IP封禁检测结果: {is_blocked}")
    
    # 测试2: 执行带反检测的请求
    logger.info("\n--- 测试2: 执行带反检测的请求 ---")
    
    # 测试被封禁的URL
    logger.info("测试被封禁的URL...")
    result1 = await anti_detection.execute_request(
        mock_request_func,
        url="https://xueqiu.com/today?blocked=true"
    )
    logger.info(f"被封禁URL的请求结果: {result1.success}, 错误: {getattr(result1, 'error', 'None')}")
    
    # 测试成功的URL
    logger.info("\n测试成功的URL...")
    result2 = await anti_detection.execute_request(
        mock_request_func,
        url="https://xueqiu.com/today?success=true"
    )
    logger.info(f"成功URL的请求结果: {result2.success}")
    
    # 测试3: 代理管理
    logger.info("\n--- 测试3: 代理管理 ---")
    
    # 获取代理
    proxy = await anti_detection.proxy_manager.get_proxy()
    logger.info(f"获取到的代理: {proxy}")
    
    # 标记代理失败
    if proxy:
        anti_detection.proxy_manager.mark_proxy_failed(proxy)
        logger.info("已标记代理为失败")
    
    # 刷新代理缓存
    await anti_detection.proxy_manager._refresh_proxy_cache()
    logger.info("代理缓存已刷新")
    
    logger.info("\n=== 反检测功能测试完成 ===")

if __name__ == "__main__":
    asyncio.run(test_anti_detection())
