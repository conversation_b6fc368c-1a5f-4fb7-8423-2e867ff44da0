#!/usr/bin/env python3
"""
简单的Playwright JavaScript测试
"""
import asyncio
from playwright.async_api import async_playwright

async def test_playwright():
    """测试Playwright"""
    try:
        print("=== 测试Playwright JavaScript执行 ===")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print("访问页面...")
            await page.goto("https://xueqiu.com/today")
            await page.wait_for_timeout(3000)
            
            print("执行JavaScript...")
            result = await page.evaluate("() => document.title")
            print(f"页面标题: {result}")
            
            result2 = await page.evaluate("() => document.querySelectorAll('a[href]').length")
            print(f"链接数量: {result2}")
            
            await browser.close()
            print("测试完成")
            
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_playwright())
