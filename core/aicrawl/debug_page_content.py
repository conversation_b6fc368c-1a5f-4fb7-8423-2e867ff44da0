#!/usr/bin/env python3
"""
调试页面内容脚本
查看雪球网站实际返回的内容，分析为什么无法提取链接
"""
import asyncio
import logging
import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from anti_detection import AntiDetectionManager
from shared_types import RequestResult

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_page_content():
    """调试页面内容"""
    logger.info("=== 开始调试页面内容 ===")
    
    # 创建反检测管理器
    anti_detection = AntiDetectionManager()
    
    # 目标URL
    url = "https://xueqiu.com/today"
    
    logger.info(f"目标URL: {url}")
    
    try:
        # 获取请求参数
        request_params = await anti_detection.prepare_request(url)
        logger.info(f"请求参数: {json.dumps(request_params, indent=2, default=str)}")
        
        # 尝试直接获取页面内容
        logger.info("尝试获取页面内容...")
        
        # 这里我们需要模拟一个简单的请求函数
        # 由于我们没有完整的爬虫引擎，我们先检查配置
        
        # 检查代理配置
        proxy = request_params.get('proxy')
        logger.info(f"代理配置: {proxy}")
        
        # 检查请求头
        headers = request_params.get('headers', {})
        logger.info(f"请求头: {json.dumps(headers, indent=2)}")
        
        # 检查cookies
        cookies = request_params.get('cookies', {})
        logger.info(f"Cookies: {json.dumps(cookies, indent=2)}")
        
        # 检查浏览器配置
        browser_config = request_params.get('browser_config', {})
        logger.info(f"浏览器配置: {json.dumps(browser_config, indent=2, default=str)}")
        
        logger.info("=== 页面内容调试完成 ===")
        
    except Exception as e:
        logger.error(f"调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_page_content())
