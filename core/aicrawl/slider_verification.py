"""
滑块验证处理器 - 专门处理网站的滑块验证码验证
"""
import asyncio
import logging
import time
import random
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from urllib.parse import urlparse

from playwright.async_api import async_playwright, Error as PlaywrightError

from config import AntiDetectionConfig
from shared_types import RequestResult

logger = logging.getLogger(__name__)

@dataclass
class SliderVerificationResult:
    """滑块验证结果"""
    success: bool
    cookies: Dict[str, str] = None
    error: str = None
    verification_time: float = None

class SliderVerificationHandler:
    """滑块验证处理器 - 专门处理网站的滑块验证码"""
    
    def __init__(self):
        # 初始化验证配置
        self.verification_config = {
            # 雪球网站特定滑块验证配置
            "xueqiu.com": {
                # 从配置文件读取雪球网站的特定配置
                "slider_container": AntiDetectionConfig.XUEQIU_CONFIG["slider_verification"]["selectors"]["slider_container"],
                "slider_track": AntiDetectionConfig.XUEQIU_CONFIG["slider_verification"]["selectors"]["slider_track"],
                "verification_box": AntiDetectionConfig.XUEQIU_CONFIG["slider_verification"]["selectors"]["verification_box"],
                "success_indicator": AntiDetectionConfig.XUEQIU_CONFIG["slider_verification"]["selectors"]["success_indicator"],
                # 验证尝试次数 - 从全局滑块验证配置获取
                "max_attempts": AntiDetectionConfig.SLIDER_VERIFICATION_CONFIG["max_attempts"],
                # 滑动轨迹参数 - 从雪球特定配置获取
                "slide_params": {
                    "min_delay": AntiDetectionConfig.XUEQIU_CONFIG["slider_verification"]["params"]["min_delay"],
                    "max_delay": AntiDetectionConfig.XUEQIU_CONFIG["slider_verification"]["params"]["max_delay"],
                    "min_steps": AntiDetectionConfig.XUEQIU_CONFIG["slider_verification"]["params"]["min_steps"],
                    "max_steps": AntiDetectionConfig.XUEQIU_CONFIG["slider_verification"]["params"]["max_steps"],
                    "human_factor": AntiDetectionConfig.XUEQIU_CONFIG["slider_verification"]["params"]["human_factor"],
                    "enable_random_offsets": AntiDetectionConfig.XUEQIU_CONFIG["slider_verification"]["params"]["enable_random_offsets"],
                    "enable_back_steps": AntiDetectionConfig.XUEQIU_CONFIG["slider_verification"]["params"]["enable_back_steps"]
                }
            },
            # 可以根据其他网站添加不同的配置
        }
    
    async def detect_and_handle_slider_verification(self, url: str, html_content: str = None) -> SliderVerificationResult:
        """
        检测并处理滑块验证
        
        参数:
            url: 网页URL
            html_content: 网页HTML内容（可选）
        
        返回:
            SliderVerificationResult: 滑块验证结果
        """
        start_time = time.time()
        domain = self._get_domain(url)
        
        if domain not in self.verification_config:
            # 当前域名没有特定的滑块验证配置
            return SliderVerificationResult(
                success=False,
                error=f"未配置域名 {domain} 的滑块验证处理方案"
            )
            
        config = self.verification_config[domain]
        result = await self._handle_slider_verification_with_playwright(url, config)
        
        result.verification_time = time.time() - start_time
        
        if result.success:
            logger.info(f"滑块验证成功，用时: {result.verification_time:.2f}秒")
        else:
            logger.warning(f"滑块验证失败: {result.error}")
            
        return result
    
    def _get_domain(self, url: str) -> str:
        """从URL中提取域名"""
        parsed_url = urlparse(url)
        return parsed_url.netloc
    
    async def _handle_slider_verification_with_playwright(self, url: str, config: Dict[str, Any]) -> SliderVerificationResult:
        """
        使用Playwright处理滑块验证
        
        参数:
            url: 需要验证的网页URL
            config: 针对该域名的验证配置
        
        返回:
            SliderVerificationResult: 滑块验证结果
        """
        browser = None
        page = None
        
        try:
            # 启动Playwright浏览器
            playwright = await async_playwright().start()
            
            # 获取隐身浏览器配置
            browser_config = AntiDetectionConfig.BROWSER_CONFIG.copy()
            # 优先使用滑块验证特定的headless设置，其次使用全局浏览器配置
            headless = AntiDetectionConfig.SLIDER_VERIFICATION_CONFIG.get("headless_mode", browser_config.get("headless", True))
            
            # 创建浏览器实例
            browser = await playwright.chromium.launch(
                headless=headless,
                args=[
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--user-agent=" + AntiDetectionConfig.get_random_user_agent()
                ]
            )
            
            # 创建新页面
            context = await browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent=AntiDetectionConfig.get_random_user_agent()
            )
            page = await context.new_page()
            
            # 导航到目标URL
            await page.goto(url, timeout=60000)
            
            # 等待验证框出现
            try:
                await page.wait_for_selector(config["verification_box"], timeout=10000)
                logger.info("检测到滑块验证框")
            except PlaywrightError:
                logger.info("未检测到滑块验证框，可能不需要验证")
                # 获取当前Cookies
                cookies = await context.cookies()
                cookie_dict = {cookie["name"]: cookie["value"] for cookie in cookies}
                return SliderVerificationResult(
                    success=True,
                    cookies=cookie_dict
                )
            
            # 尝试滑块验证
            for attempt in range(config["max_attempts"]):
                logger.info(f"滑块验证尝试 #{attempt + 1}")
                
                if await self._perform_slider_action(page, config["slide_params"]):
                    # 等待验证成功
                    try:
                        await page.wait_for_selector(config["success_indicator"], timeout=5000)
                        logger.info("滑块验证成功")
                        
                        # 获取验证后的Cookies
                        cookies = await context.cookies()
                        cookie_dict = {cookie["name"]: cookie["value"] for cookie in cookies}
                        
                        return SliderVerificationResult(
                            success=True,
                            cookies=cookie_dict
                        )
                    except PlaywrightError:
                        logger.warning("验证成功指示器未出现，可能验证失败")
                        continue
            
            return SliderVerificationResult(
                success=False,
                error=f"滑块验证失败，已尝试 {config['max_attempts']} 次"
            )
            
        except Exception as e:
            logger.error(f"滑块验证过程中发生错误: {str(e)}")
            return SliderVerificationResult(
                success=False,
                error=str(e)
            )
        finally:
            # 关闭浏览器
            if page:
                await page.close()
            if browser:
                await browser.close()
            await playwright.stop()
    
    async def _perform_slider_action(self, page, slide_params: Dict[str, Any]) -> bool:
        """
        执行滑块操作，模拟人类滑动行为
        
        参数:
            page: Playwright页面实例
            slide_params: 滑动参数配置
        
        返回:
            bool: 滑动操作是否成功
        """
        try:
            # 获取滑块元素和轨迹元素
            slider = await page.wait_for_selector("//div[contains(@class, 'geetest_slider_button')]", timeout=3000)
            slider_track = await page.wait_for_selector("//div[contains(@class, 'geetest_slider_track')]", timeout=3000)
            
            # 尝试动态计算滑动距离
            try:
                # 获取滑块和轨迹的尺寸信息
                slider_box = await slider.bounding_box()
                track_box = await slider_track.bounding_box()
                
                if slider_box and track_box:
                    # 计算需要滑动的距离（轨迹总宽度减去滑块宽度）
                    slide_distance = track_box['width'] - slider_box['width']
                    logger.info(f"动态计算滑动距离: {slide_distance}px")
                else:
                    # 如果无法获取尺寸信息，使用默认值
                    slide_distance = 260
                    logger.warning("无法获取滑块和轨迹尺寸，使用默认滑动距离")
            except Exception as e:
                # 如果计算失败，使用默认值并记录错误
                slide_distance = 260
                logger.warning(f"计算滑动距离失败: {str(e)}，使用默认值")
            
            # 生成人类滑动轨迹
            track = self._generate_human_slide_track(slide_distance, slide_params)
            
            # 执行滑动操作
            await slider.hover()
            await asyncio.sleep(random.uniform(0.3, 0.8))  # 模拟犹豫时间
            await slider.mouse_down()
            await asyncio.sleep(random.uniform(0.1, 0.2))  # 按下后短暂停顿
            
            current_position = 0
            slider_box = await slider.bounding_box()
            if slider_box:
                start_x = slider_box['x'] + slider_box['width'] / 2
                start_y = slider_box['y'] + slider_box['height'] / 2
                
                for step in track:
                    current_position += step
                    # 添加一些随机的垂直偏移，模拟人类操作
                    y_offset = random.uniform(-2, 2)
                    await page.mouse.move(start_x + current_position, start_y + y_offset)
                    await asyncio.sleep(random.uniform(slide_params["min_delay"], slide_params["max_delay"]))
            
            # 松开鼠标前短暂停顿
            await asyncio.sleep(random.uniform(0.05, 0.15))
            await slider.mouse_up()
            
            # 等待验证结果
            await asyncio.sleep(2)
            
            # 检查是否验证成功
            try:
                # 尝试找到成功指示器
                success_indicator = await page.wait_for_selector(
                    "//div[contains(@class, 'geetest_success_radar_tip_content')]", 
                    timeout=2000
                )
                if success_indicator:
                    logger.info("滑块验证成功")
                    return True
            except:
                # 没有找到成功指示器，可能验证失败
                logger.warning("未找到验证成功指示器")
            
            # 检查滑块是否回到原位（验证失败的表现）
            try:
                await page.wait_for_selector(
                    "//div[contains(@class, 'geetest_slider_button') and contains(@style, 'left: 0')]",
                    timeout=1000
                )
                logger.warning("滑块回到原位，验证失败")
                return False
            except:
                # 滑块没有回到原位，可能验证成功
                return True
        except Exception as e:
            logger.error(f"滑块操作失败: {str(e)}")
            return False
    
    def _generate_human_slide_track(self, target_distance: int, params: Dict[str, Any]) -> List[int]:
        """
        生成模拟人类滑动的轨迹
        
        参数:
            target_distance: 目标滑动距离
            params: 滑动参数
        
        返回:
            List[int]: 滑动轨迹列表，包含每一步的滑动距离
        """
        track = []
        current = 0
        
        # 使用物理模型模拟人类滑动行为
        # 分为三个阶段：起始加速、中间匀速、最后减速
        
        # 随机确定三个阶段的比例
        accel_ratio = random.uniform(0.3, 0.5)  # 加速阶段占比
        uniform_ratio = random.uniform(0.1, 0.3)  # 匀速阶段占比
        decel_ratio = 1 - accel_ratio - uniform_ratio  # 减速阶段占比
        
        # 计算各阶段的目标距离
        accel_distance = target_distance * accel_ratio
        uniform_distance = target_distance * uniform_ratio
        
        # 计算加速度和减速度
        max_accel = random.uniform(5, 10)  # 最大加速度
        max_decel = random.uniform(3, 7)   # 最大减速度
        
        # 当前速度
        velocity = 0
        
        # 生成加速阶段轨迹
        while current < accel_distance:
            # 加速度逐渐增加
            acceleration = min(max_accel, (current / accel_distance) * max_accel)
            velocity += acceleration
            # 限制最大速度
            velocity = min(velocity, random.uniform(15, 25))
            
            # 计算当前步长
            step = int(velocity) + 1
            # 确保不会超过目标距离
            if current + step > accel_distance:
                step = int(accel_distance - current)
            
            track.append(step)
            current += step
        
        # 生成匀速阶段轨迹
        uniform_target = current + uniform_distance
        while current < uniform_target:
            # 保持大致相同的速度，但有微小波动
            step = int(random.uniform(velocity * 0.8, velocity * 1.2)) + 1
            # 确保不会超过目标距离
            if current + step > uniform_target:
                step = int(uniform_target - current)
            
            track.append(step)
            current += step
        
        # 生成减速阶段轨迹
        while current < target_distance:
            # 减速度逐渐增加
            remaining_distance = target_distance - current
            deceleration = min(max_decel, (1 - remaining_distance / (target_distance - uniform_target)) * max_decel)
            velocity -= deceleration
            # 确保速度不会变为负数
            velocity = max(velocity, 0.1)
            
            # 计算当前步长
            step = int(velocity) + 1
            # 确保不会超过目标距离
            if current + step > target_distance:
                step = int(target_distance - current)
            
            # 添加微小的随机波动，模拟人类操作的不稳定性
            if random.random() > 0.7:
                # 5%的概率回退一点，但不会导致整体后退
                back_step = random.randint(1, 2)
                if current + step - back_step < target_distance:
                    track.append(back_step)
                    current -= back_step
                else:
                    track.append(step)
                    current += step
            else:
                track.append(step)
                current += step
            
        # 调整最后一步，确保到达目标位置
        if current > target_distance:
            track[-1] -= (current - target_distance)
        
        # 添加微小的垂直轨迹变化，增强人类操作特征
        # 注意：这里只返回水平轨迹，垂直偏移在_perform_slider_action中处理
        
        return track
    
    def is_verification_required(self, html_content: str, url: str = None) -> bool:
        """
        检查页面是否需要滑块验证
        
        参数:
            html_content: 网页HTML内容
            url: 网页URL（可选）
        
        返回:
            bool: 是否需要滑块验证
        """
        # 检查常见的滑块验证关键词
        verification_keywords = [
            "geetest", "gt_captcha", "slider_verification",
            "滑动验证", "验证码", "滑块", "验证",
            "请按住滑块", "向右滑动", "完成验证"
        ]
        
        if not html_content:
            return False
            
        # 检查是否包含滑块验证相关关键词
        for keyword in verification_keywords:
            if keyword.lower() in html_content.lower():
                return True
                
        return False
    
    async def solve_and_get_valid_cookies(self, url: str) -> Dict[str, str]:
        """
        解决滑块验证并获取有效的cookies
        
        参数:
            url: 需要验证的网页URL
        
        返回:
            Dict[str, str]: 验证后的有效cookies，如果验证失败则返回空字典
        """
        result = await self.detect_and_handle_slider_verification(url)
        
        if result.success and result.cookies:
            return result.cookies
        else:
            return {}