#!/usr/bin/env python3
"""
检查crawl4ai结果对象的结构
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawl4ai import AsyncWebCrawler

# 创建一个简单的配置类来替代 CrawlerRunConfig
class SimpleCrawlerConfig:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def inspect_result_structure():
    """检查结果对象的结构"""
    try:
        print("=== 检查crawl4ai结果对象结构 ===")
        
        # 测试URL
        test_url = "https://xueqiu.com/today"
        
        print(f"测试URL: {test_url}")
        
        # 创建配置
        config = SimpleCrawlerConfig(
            cache_mode="bypass",
            js_code="return 'Hello from crawl4ai!';",
            js_only=True,
            capture_console_messages=True,
            verbose=True,
            page_timeout=30000,
            delay_before_return_html=1
        )
        
        print("配置创建完成，开始测试...")
        
        # 直接使用AsyncWebCrawler
        async with AsyncWebCrawler() as crawler:
            result = await crawler.arun(
                url=test_url,
                crawler_config=config
            )
            
            print(f"\n=== 结果对象详细信息 ===")
            print(f"类型: {type(result)}")
            print(f"模块: {result.__class__.__module__}")
            print(f"类名: {result.__class__.__name__}")
            
            # 检查所有属性
            print(f"\n所有属性:")
            for attr in dir(result):
                if not attr.startswith('_'):
                    try:
                        value = getattr(result, attr)
                        print(f"  {attr}: {type(value)} = {value}")
                    except Exception as e:
                        print(f"  {attr}: 无法访问 - {e}")
            
            # 检查字典表示
            if hasattr(result, '__dict__'):
                print(f"\n__dict__内容:")
                for key, value in result.__dict__.items():
                    print(f"  {key}: {type(value)} = {value}")
            
            # 检查特殊方法
            print(f"\n特殊方法:")
            for method in ['__getitem__', '__iter__', '__len__']:
                if hasattr(result, method):
                    try:
                        if method == '__len__':
                            length = len(result)
                            print(f"  __len__: {length}")
                        elif method == '__iter__':
                            items = list(result)
                            print(f"  __iter__: {len(items)} 项")
                        elif method == '__getitem__':
                            if hasattr(result, '__len__') and len(result) > 0:
                                first_item = result[0]
                                print(f"  __getitem__[0]: {type(first_item)} = {first_item}")
                    except Exception as e:
                        print(f"  {method}: 执行失败 - {e}")
            
            # 尝试访问结果
            if hasattr(result, '_results'):
                print(f"\n_results属性:")
                _results = result._results
                print(f"  类型: {type(_results)}")
                print(f"  长度: {len(_results) if hasattr(_results, '__len__') else 'N/A'}")
                
                if hasattr(_results, '__iter__'):
                    for i, item in enumerate(_results):
                        print(f"  项目{i}: {type(item)} = {item}")
                        if hasattr(item, '__dict__'):
                            for key, value in item.__dict__.items():
                                print(f"    {key}: {type(value)} = {value}")
                        break  # 只显示第一个项目
                        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(inspect_result_structure())
