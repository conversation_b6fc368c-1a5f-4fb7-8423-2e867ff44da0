"""
配置文件 - 反爬虫系统配置
"""
import json
import random
from typing import List, Dict, Any

class ScrollConfig:
    """页面滚动配置类"""

    # 滚动策略配置 - 优化后更稳定
    SCROLL_STRATEGIES = {
        "conservative": {
            "max_scrolls": 3,
            "scroll_delay": 3000,  # 增加延迟确保加载
            "step_delay": 1500,
            "positions": [0.5, 0.8, 1.0]
        },
        "moderate": {
            "max_scrolls": 4,
            "scroll_delay": 2500,
            "step_delay": 1200,
            "positions": [0.3, 0.6, 0.8, 1.0]
        },
        "aggressive": {
            "max_scrolls": 6,
            "scroll_delay": 2000,
            "step_delay": 1000,
            "positions": [0.2, 0.4, 0.6, 0.8, 0.9, 1.0]
        }
    }

    # 加载更多按钮的选择器
    LOAD_MORE_SELECTORS = [
        '[class*="load"]',
        '[class*="more"]',
        'button',
        '.load-more',
        '.show-more',
        '[class*="展开"]',
        '[class*="加载"]',
        '[class*="更多"]',
        '.timeline-load-more',
        '.comment-load-more'
    ]

    # 加载更多按钮的文本关键词
    LOAD_MORE_KEYWORDS = [
        '更多', '加载', 'load', 'more', '展开', 'show', 'expand',
        '查看更多', '加载更多', 'load more', 'show more'
    ]

    # 评论展开按钮的选择器
    EXPAND_COMMENT_SELECTORS = [
        '.expand',
        '.show-more',
        '.unfold',
        '[class*="expand"]',
        '[class*="unfold"]',
        '[class*="show-more"]',
        '[class*="展开"]',
        '[class*="更多"]',
        'a[class*="more"]',
        'span[class*="more"]',
        'button[class*="expand"]',
        '.text-expand',
        '.content-expand'
    ]

    # 评论展开按钮的文本关键词
    EXPAND_COMMENT_KEYWORDS = [
        '展开', '更多', '全文', '查看全文', '显示更多',
        'expand', 'show more', 'read more', 'unfold',
        '展开全文', '查看更多', '显示全部', '...更多',
        '点击展开', '展开内容', '查看完整内容'
    ]

    @classmethod
    def get_scroll_strategy(cls, strategy_name: str = "moderate") -> dict:
        """获取滚动策略配置"""
        return cls.SCROLL_STRATEGIES.get(strategy_name, cls.SCROLL_STRATEGIES["moderate"])

    @classmethod
    def generate_scroll_js(cls, target_count: int = 10, strategy: str = "moderate") -> str:
        """生成智能滚动的JavaScript代码 - 针对雪球页面优化"""
        config = cls.get_scroll_strategy(strategy)

        return f"""
        async function xueqiuSimpleScroll() {{
            console.log('🚀 开始雪球简化滚动策略: {strategy}');
            console.log('🎯 目标评论数:', {target_count});

            const config = {json.dumps(config)};
            const targetCount = {target_count};
            let scrollCount = 0;
            let lastCommentCount = 0;

            // 简化的评论选择器 - 专注于最有效的
            const commentSelectors = [
                '.timeline__item__comment',
                '[class*="timeline"]',
                '.status-item',
                '[data-id]'
            ];

            // 获取评论数量
            function getCommentCount() {{
                let maxCount = 0;
                for (let selector of commentSelectors) {{
                    try {{
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > maxCount) {{
                            maxCount = elements.length;
                            console.log(`📊 ${{selector}}: ${{elements.length}} 个元素`);
                        }}
                    }} catch (e) {{
                        // 忽略错误
                    }}
                }}
                return maxCount;
            }}

            // 展开折叠的评论内容
            async function expandCommentContent() {{
                const expandSelectors = {json.dumps(cls.EXPAND_COMMENT_SELECTORS)};
                const expandKeywords = {json.dumps(cls.EXPAND_COMMENT_KEYWORDS)};
                let expandedCount = 0;

                // 查找所有可能的展开按钮
                for (let selector of expandSelectors) {{
                    try {{
                        const buttons = document.querySelectorAll(selector);
                        for (let btn of buttons) {{
                            if (btn.offsetParent && !btn.disabled && btn.style.display !== 'none') {{
                                const btnText = btn.textContent.toLowerCase().trim();
                                const hasExpandKeyword = expandKeywords.some(keyword =>
                                    btnText.includes(keyword.toLowerCase())
                                );

                                if (hasExpandKeyword) {{
                                    try {{
                                        btn.click();
                                        console.log(`📖 展开评论: ${{btn.textContent.trim()}}`);
                                        expandedCount++;
                                        // 展开后等待内容加载
                                        await new Promise(resolve => setTimeout(resolve, 500));
                                    }} catch (e) {{
                                        console.log('展开失败:', e.message);
                                    }}
                                }}
                            }}
                        }}
                    }} catch (e) {{
                        // 忽略选择器错误
                    }}
                }}

                // 特殊处理：查找省略号后的展开链接
                const ellipsisElements = document.querySelectorAll('*');
                for (let elem of ellipsisElements) {{
                    const text = elem.textContent;
                    if (text && (text.includes('...') || text.includes('…'))) {{
                        // 查找附近的展开按钮
                        const parent = elem.parentElement;
                        if (parent) {{
                            const expandBtn = parent.querySelector('a, span, button');
                            if (expandBtn && expandBtn.textContent.includes('更多')) {{
                                try {{
                                    expandBtn.click();
                                    console.log(`📖 展开省略内容: ${{expandBtn.textContent.trim()}}`);
                                    expandedCount++;
                                    await new Promise(resolve => setTimeout(resolve, 500));
                                }} catch (e) {{
                                    // 忽略错误
                                }}
                            }}
                        }}
                    }}
                }}

                return expandedCount;
            }}

            // 点击加载更多按钮
            function clickLoadMore() {{
                const buttons = document.querySelectorAll('button, a, [class*="load"], [class*="more"]');
                let clicked = 0;

                for (let btn of buttons) {{
                    if (btn.offsetParent && !btn.disabled) {{
                        const text = btn.textContent.toLowerCase();
                        if (text.includes('更多') || text.includes('加载') ||
                            text.includes('load') || text.includes('more')) {{
                            try {{
                                btn.click();
                                console.log(`🔘 点击: ${{btn.textContent.trim()}}`);
                                clicked++;
                            }} catch (e) {{
                                // 忽略点击错误
                            }}
                        }}
                    }}
                }}
                return clicked;
            }}

            // 主滚动循环
            console.log(`🔄 开始滚动，最大 ${{config.max_scrolls}} 次`);

            for (let i = 0; i < config.max_scrolls; i++) {{
                const beforeCount = getCommentCount();
                console.log(`📈 滚动 ${{i + 1}}: 当前 ${{beforeCount}} 条评论`);

                // 1. 首先展开所有折叠的评论内容
                const expandedCount = await expandCommentContent();
                if (expandedCount > 0) {{
                    console.log(`📖 展开了 ${{expandedCount}} 个折叠内容`);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }}

                // 2. 渐进式滚动
                for (let position of config.positions) {{
                    const targetY = document.body.scrollHeight * position;
                    window.scrollTo(0, targetY);
                    await new Promise(resolve => setTimeout(resolve, config.step_delay));

                    // 在每个位置都尝试展开内容
                    await expandCommentContent();

                    // 点击加载更多
                    clickLoadMore();
                }}

                // 3. 最终展开检查
                const finalExpandedCount = await expandCommentContent();
                if (finalExpandedCount > 0) {{
                    console.log(`📖 最终展开了 ${{finalExpandedCount}} 个内容`);
                }}

                // 等待内容加载
                await new Promise(resolve => setTimeout(resolve, config.scroll_delay));

                const afterCount = getCommentCount();
                console.log(`📊 滚动后: ${{afterCount}} 条评论 (新增: ${{afterCount - beforeCount}})`);

                // 如果达到目标或没有新增，可以提前结束
                if (afterCount >= targetCount || afterCount === beforeCount) {{
                    console.log(`🎯 提前结束: 达到目标或无新增`);
                    break;
                }}

                lastCommentCount = afterCount;
            }}

            // 最终滚动到底部
            window.scrollTo(0, document.body.scrollHeight);
            await new Promise(resolve => setTimeout(resolve, 2000));

            const finalCount = getCommentCount();
            console.log(`🏁 滚动完成: ${{finalCount}} 条评论`);

            return {{
                scrollCount: scrollCount,
                finalCommentCount: finalCount,
                strategy: '{strategy}',
                success: finalCount > 0
            }};
        }}

        // 执行简化滚动
        const scrollResult = await xueqiuSimpleScroll();
        console.log('📋 滚动结果:', scrollResult);
        """

class AntiDetectionConfig:
    """反检测配置类"""
    
    # 用户代理池 - 模拟真实浏览器
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]
    
    # 请求头模板
    HEADERS_TEMPLATES = [
        {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1"
        },
        {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
    ]
    
    # 代理配置
    PROXY_POOLS = [
        # 主要代理 - 用于爬虫失败时使用
        {"http": "http://***********:5010", "https": "http://***********:5010"},
    ]
    
    # 请求频率控制
    REQUEST_DELAYS = {
        "min_delay": 2,      # 最小延迟(秒)
        "max_delay": 8,      # 最大延迟(秒)
        "burst_delay": 15,   # 突发请求后的延迟(秒)
        "error_delay": 30    # 错误后的延迟(秒)
    }
    
    # 重试配置
    RETRY_CONFIG = {
        "max_retries": 3,
        "retry_delay": 5,
        "backoff_factor": 2
    }
    
    # 浏览器配置
    BROWSER_CONFIG = {
        "headless": True,  # 在Docker容器中运行必须使用无头模式
        "disable_images": False,  # 滑块验证可能需要加载图片
        "disable_css": False,
        "disable_javascript": False,
        "viewport": {"width": 1920, "height": 1080},
        "timeout": 60000,
        "args": [
            "--disable-blink-features=AutomationControlled",
            "--disable-infobars",
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-dev-shm-usage",
            "--disable-gpu",
            "--window-size=1920,1080"
        ],
        "ignore_https_errors": True,
        "user_data_dir": "/tmp/playwright_user_data",  # 持久化浏览器数据
        "accept_downloads": False
    }
    
    # 滑块验证特定配置
    SLIDER_VERIFICATION_CONFIG = {
        "max_attempts": 5,  # 最大验证尝试次数
        "retry_delay": 3,   # 验证失败后的重试延迟(秒)
        "success_threshold": 0.8,  # 成功阈值
        "headless_mode": True,     # 在Docker容器中运行必须使用无头模式
        "human_emulation": {
            "enable": True,
            "mouse_movement": "bezier",  # 使用贝塞尔曲线模拟鼠标移动
            "typing_speed": "human"
        }
    }
    
    # 雪球网站特定配置
    XUEQIU_CONFIG = {
        "base_url": "https://xueqiu.com",
        "homepage_url": "https://xueqiu.com",
        "today_url": "https://xueqiu.com/today",  # 今日页面URL
        "stock_url_pattern": "https://xueqiu.com/S/{symbol}",
        "required_cookies": [
            # 雪球可能需要的cookie，通过浏览器访问获取
            "xq_a_token",
            "xqat",
            "xq_r_token",
            "xq_id_token",
            "u",
            "device_id"
        ],
        # 专注于HTML页面爬取，避免API反爬
        "crawl_settings": {
            "wait_for_load": True,
            "enable_javascript": True,
            "page_load_timeout": 60,
            "scroll_to_load_comments": True,
            "delay_between_requests": 5,  # 增加请求间隔，减少被识别的风险
            "anti_detection": True,
            "use_stealth_mode": True,
            "rotate_user_agent": True
        },
        "url_patterns": {
            "homepage": "https://xueqiu.com",
            "stock_detail": "https://xueqiu.com/S/{symbol}",
            "stock_comments": "https://xueqiu.com/S/{symbol}#comments",
            "user_profile": "https://xueqiu.com/u/{user_id}",
            "topic": "https://xueqiu.com/topic/{topic_id}"
        },
        # 雪球滑块验证特定配置
        "slider_verification": {
            "enabled": True,
            "selectors": {
                "slider_container": "//div[contains(@class, 'geetest_slider_button')]",
                "slider_track": "//div[contains(@class, 'geetest_slider_track')]",
                "verification_box": "//div[contains(@class, 'geetest_panel')]",
                "success_indicator": "//div[contains(@class, 'geetest_success_radar_tip_content')]"
            },
            "params": {
                "min_delay": 0.08,  # 步骤之间的最小延迟
                "max_delay": 0.2,   # 步骤之间的最大延迟
                "min_steps": 40,    # 最小步数
                "max_steps": 60,    # 最大步数
                "human_factor": 0.2, # 人类因素系数，控制随机性
                "enable_random_offsets": True,  # 启用随机偏移
                "enable_back_steps": True       # 启用回退步骤
            }
        }
    }
    
    @classmethod
    def get_random_user_agent(cls) -> str:
        """获取随机User-Agent"""
        return random.choice(cls.USER_AGENTS)
    
    @classmethod
    def get_random_headers(cls) -> Dict[str, str]:
        """获取随机请求头"""
        headers = random.choice(cls.HEADERS_TEMPLATES).copy()
        headers["User-Agent"] = cls.get_random_user_agent()
        return headers
    
    @classmethod
    def get_random_delay(cls) -> float:
        """获取随机延迟时间"""
        return random.uniform(
            cls.REQUEST_DELAYS["min_delay"],
            cls.REQUEST_DELAYS["max_delay"]
        )
    
    @classmethod
    def get_random_proxy(cls) -> Dict[str, str]:
        """获取随机代理"""
        if not cls.PROXY_POOLS:
            return None
        return random.choice(cls.PROXY_POOLS)

# 数据提取配置
class ExtractionConfig:
    """数据提取配置"""
    
    # 雪球网站HTML选择器配置 - 基于实际页面结构
    XUEQIU_SELECTORS = {
        # 个股页面基本信息选择器
        "stock_info": {
            "name": [
                "h1.stock-name",
                ".stock-info h1",
                ".quote-info .name",
                "[data-reactid*='name']",
                "h1"
            ],
            "code": [
                ".stock-symbol",
                ".quote-info .symbol",
                "[data-reactid*='symbol']",
                ".stock-code"
            ],
            "price": [
                ".stock-current",
                ".current-price",
                ".quote-price",
                "[data-reactid*='current']",
                ".price-current",
                ".stock-price .current"
            ],
            "change": [
                ".stock-change",
                ".change-amount",
                "[data-reactid*='change']",
                ".price-change .amount"
            ],
            "change_percent": [
                ".stock-percent",
                ".change-percent",
                "[data-reactid*='percent']",
                ".price-change .percent"
            ],
            "volume": [
                ".stock-volume",
                ".volume",
                "[data-reactid*='volume']",
                ".trade-volume"
            ],
            "market_cap": [
                ".stock-market-cap",
                ".market-cap",
                "[data-reactid*='market_capital']",
                ".market-value"
            ]
        },

        # 首页热门股票选择器 - 基于雪球实际页面结构
        "homepage_hot_stocks": {
            "container": [
                ".StockHotList_stock-hot__container_3fO",
                ".StockHotList_board_Yio",
                ".StockSlider_home__stock_container_2cd",
                ".StockSlider_home__stock-index__main_1oA",
                "[class*='StockHotList']",
                "[class*='StockSlider']"
            ],
            "item": [
                ".StockHotList_stock-hot__list_Do4 tr",
                ".StockHotList_board__list_2rp tr",
                "table[class*='stock-hot__list'] tr",
                "table[class*='board__list'] tr",
                ".StockSlider_home__stock-index__item_1V7",
                "tr"
            ],
            "symbol": [
                "a[href*='/S/']",
                "[class*='symbol']",
                "[class*='code']",
                "[data-symbol]",
                ".stock-code"
            ],
            "name": [
                "a[href*='/S/']",
                "[class*='name']",
                "[class*='stock-name']",
                "[class*='title']"
            ],
            "price": [
                "[class*='price']",
                "[class*='current']",
                "[class*='StockSlider_current']",
                ".StockSlider_current_2gu"
            ],
            "change_percent": [
                "td:last-child",
                "td:nth-child(4)",
                ".StockHotList_gain_2E8:last-child",
                "[class*='gain']:last-child",
                "[class*='percent']",
                "[class*='change']",
                ".StockSlider_gain_1xU"
            ]
        },

        # 首页热门话题选择器 - 基于雪球实际页面结构
        "homepage_hot_topics": {
            "container": [
                ".HashtagHotList_topic-hot__list_2l1",
                ".HashtagHotList_board__list_2X_",
                ".HashtagHotList_board_3Oq",
                "[class*='HashtagHotList']",
                "[class*='topic-hot']"
            ],
            "item": [
                ".HashtagHotList_topic-hot__list_2l1 tr",
                ".HashtagHotList_board__list_2X_ tr",
                "table[class*='topic-hot__list'] tr",
                "table[class*='HashtagHotList'] tr",
                "tr"
            ],
            "title": [
                "a",
                "[href*='/hashtag/']",
                "[class*='title']",
                "[class*='topic-title']",
                "span"
            ],
            "url": [
                "a",
                "[href*='/hashtag/']"
            ],
            "author": [
                "[class*='author']",
                "[class*='user-name']",
                "[class*='username']",
                "[class*='user']"
            ],
            "view_count": [
                "[class*='view-count']",
                "[class*='views']",
                "[class*='read-count']",
                "[class*='count']"
            ],
            "comment_count": [
                "[class*='comment-count']",
                "[class*='comments']",
                "[class*='reply-count']",
                "[class*='discuss']"
            ]
        },

        # 首页用户文章/动态选择器 - 基于雪球实际页面结构
        "homepage_user_posts": {
            "container": [
                ".style_home__timeline_1Tz",
                "[class*='timeline']",
                "[class*='feed']"
            ],
            "item": [
                ".style_timeline__item_3WW",
                "[class*='timeline__item']",
                "[class*='feed__item']"
            ],
            "content": [
                ".style_timeline__item__content_38K",
                ".style_content_1G-",
                "[class*='content']"
            ],
            "author": [
                ".style_timeline__item__info_38G a",
                "[class*='author']",
                "[class*='user']"
            ],
            "author_avatar": [
                ".avatar_icon_23v img",
                "[class*='avatar'] img",
                "img[class*='avatar']"
            ],
            "publish_time": [
                ".style_timeline__item__info_38G time",
                "[class*='time']",
                "[class*='date']"
            ],
            "like_count": [
                "[class*='like']",
                "[class*='fav']",
                "[class*='praise']"
            ],
            "comment_count": [
                "[class*='comment']",
                "[class*='reply']"
            ],
            "repost_count": [
                "[class*='repost']",
                "[class*='forward']",
                "[class*='share']"
            ],
            "post_url": [
                "a[href*='/status/']",
                "a[href*='/post/']"
            ],
            "mentioned_stocks": [
                "a[href*='/S/']",
                "[class*='stock']"
            ],
            "images": [
                ".style_status__images_3Y2 img",
                "[class*='image'] img",
                "img[src*='image']"
            ]
        },

        # 评论区选择器 - 针对雪球页面优化
        "comments": {
            "container": [
                # 雪球页面特有的容器
                ".stock-timeline",
                ".timeline",
                ".status-list",
                ".feed-list",
                ".discussion-list",
                "[class*='stock-timeline']",
                "[class*='timeline']",
                "[class*='status-list']",
                "[class*='feed']",
                "[class*='discuss']",
                # 通用容器
                ".comments-section",
                ".comment-list",
                ".comments",
                "#comments"
            ],
            "item": [
                # 雪球页面特有的项目
                ".timeline__item__comment",
                ".status-item",
                ".timeline-item",
                ".feed-item",
                "[class*='timeline__item']",
                "[class*='status-item']",
                "[class*='timeline-item']",
                "[class*='feed-item']",
                "[data-id]",
                # 通用项目
                ".comment-item",
                ".comment",
                "li[class*='item']",
                "div[class*='item']",
                "article"
            ],
            "content": [
                # 雪球页面特有的内容
                ".status-content",
                ".feed-content",
                "[class*='status-content']",
                "[class*='feed-content']",
                "[class*='text-content']",
                # 通用内容
                ".comment-content",
                ".text",
                ".content",
                ".desc",
                ".description",
                "p:not([class*='meta'])",
                ".message"
            ],
            "author": [
                # 雪球页面特有的作者
                ".user-name",
                ".username",
                ".nickname",
                "[class*='user-name']",
                "[class*='username']",
                "[class*='nickname']",
                "a[href*='/u/']",
                # 通用作者
                ".comment-author",
                ".author",
                ".by-author",
                ".poster"
            ],
            "time": [
                # 雪球页面特有的时间
                ".publish-time",
                ".created-at",
                ".timestamp",
                "[class*='time']",
                "[class*='date']",
                "[title*='时间']",
                "[title*='发布']",
                # 通用时间
                ".comment-time",
                ".time",
                "time",
                ".date",
                "[datetime]"
            ],
            "like_count": [
                # 雪球页面特有的点赞
                ".fav-count",
                ".like-count",
                "[class*='fav-count']",
                "[class*='like-count']",
                "[class*='praise']",
                "[class*='thumb']",
                # 通用点赞
                ".likes",
                ".favorites",
                ".upvotes"
            ],
            "reply_count": [
                # 回复数选择器
                ".reply-count",
                ".comment-count",
                "[class*='reply-count']",
                "[class*='comment-count']",
                "[class*='replies']"
            ],
            "author_id": [
                # 作者ID提取
                "a[href*='/u/']",
                "[data-user-id]",
                "[data-uid]"
            ]
        }
    }
    
    # JSON数据提取规则
    JSON_EXTRACTION_RULES = {
        "quote_data": {
            "path": "window.SNB.data.quote",
            "fields": {
                "symbol": "symbol",
                "name": "name", 
                "current": "current",
                "percent": "percent",
                "chg": "chg",
                "volume": "volume",
                "amount": "amount",
                "market_capital": "market_capital",
                "pe_ttm": "pe_ttm",
                "pb": "pb"
            }
        }
    }

    # HTML内容提取配置
    HTML_EXTRACTION_CONFIG = {
        "enable_html_content": True,  # 是否启用HTML内容提取
        "preserve_formatting": True,  # 是否保留基本格式
        "allowed_tags": [  # 允许保留的HTML标签
            "p", "br", "div", "span", "strong", "b", "em", "i", "u", "a", "img",
            "h1", "h2", "h3", "h4", "h5", "h6", "ul", "ol", "li", "blockquote"
        ],
        "allowed_attributes": [  # 允许保留的属性
            "href", "src", "alt", "title", "class"
        ],
        "remove_tags": [  # 需要移除的标签
            "script", "style", "noscript", "iframe", "object", "embed"
        ],
        "min_content_length": {  # 最小内容长度要求
            "text": 20,      # 纯文本最小长度
            "html": 50       # HTML内容最小长度
        }
    }
