#!/usr/bin/env python3
"""
快速测试Playwright爬虫
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main_advanced import PopularArticlesCrawler

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_quick():
    """快速测试"""
    try:
        print("=== 快速测试Playwright爬虫 ===")
        
        # 创建爬虫实例
        crawler = PopularArticlesCrawler()
        
        # 测试URL
        test_url = "https://xueqiu.com/today"
        
        print(f"测试URL: {test_url}")
        print("开始测试...")
        
        # 测试链接提取（使用较小的参数）
        links = await crawler._extract_article_links_with_load_more(
            test_url,
            max_load_more=5  # 只测试5次加载更多
        )
        
        print(f"\n=== 测试结果 ===")
        print(f"提取到 {len(links)} 个链接")
        
        if links:
            print("前5个链接:")
            for i, link in enumerate(links[:5], 1):
                print(f"  {i}. {link}")
        
        return links
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    asyncio.run(test_quick())
