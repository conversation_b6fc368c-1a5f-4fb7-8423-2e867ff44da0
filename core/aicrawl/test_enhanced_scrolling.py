#!/usr/bin/env python3
"""
测试优化后的滚动和加载更多功能
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main_advanced import PopularArticlesCrawler

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_enhanced_scrolling():
    """测试增强的滚动和加载更多功能"""
    try:
        print("=== 测试增强的滚动和加载更多功能 ===")
        
        # 创建爬虫实例
        crawler = PopularArticlesCrawler()
        
        # 测试URL
        test_url = "https://xueqiu.com/today"
        
        print(f"测试URL: {test_url}")
        print("开始测试增强型链接提取（支持加载更多）...")
        
        # 使用更大的加载更多次数
        max_load_more = 30
        
        # 执行增强型提取
        start_time = asyncio.get_event_loop().time()
        links = await crawler._extract_article_links_with_load_more(test_url, max_load_more)
        end_time = asyncio.get_event_loop().time()
        
        print(f"\n=== 测试结果 ===")
        print(f"总耗时: {end_time - start_time:.2f} 秒")
        print(f"提取到的链接数量: {len(links)}")
        
        if links:
            print(f"前5个链接示例:")
            for i, link in enumerate(links[:5]):
                print(f"  {i+1}. {link}")
                
            # 统计链接格式
            valid_links = [link for link in links if link.startswith('https://xueqiu.com/')]
            print(f"\n有效链接数量: {len(valid_links)}")
            
            # 检查是否有重复链接
            unique_links = set(links)
            print(f"去重后链接数量: {len(unique_links)}")
            if len(links) != len(unique_links):
                print(f"⚠️ 发现重复链接: {len(links) - len(unique_links)} 个")
        else:
            print("❌ 没有提取到任何链接")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_enhanced_scrolling())
