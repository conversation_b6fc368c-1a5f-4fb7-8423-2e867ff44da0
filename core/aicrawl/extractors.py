"""
数据提取器 - 专门针对雪球网站的数据提取策略
"""
import re
import json
import logging
from typing import Dict, List, Optional, Any, Union
from bs4 import BeautifulSoup
from dataclasses import dataclass

from config import ExtractionConfig

logger = logging.getLogger(__name__)

@dataclass
class StockData:
    """股票数据结构"""
    symbol: str
    name: str
    current_price: Optional[float] = None
    change: Optional[float] = None
    change_percent: Optional[float] = None
    volume: Optional[int] = None
    amount: Optional[float] = None
    market_cap: Optional[float] = None
    pe_ttm: Optional[float] = None
    pb: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open_price: Optional[float] = None
    prev_close: Optional[float] = None
    high_52w: Optional[float] = None
    low_52w: Optional[float] = None
    eps: Optional[float] = None
    dividend_yield: Optional[float] = None
    raw_data: Optional[Dict] = None

@dataclass
class HotTopicData:
    """热门话题数据结构"""
    title: str = ""
    url: str = ""
    view_count: Optional[int] = None
    comment_count: Optional[int] = None
    like_count: Optional[int] = None
    author: str = ""
    publish_time: Optional[str] = None
    summary: str = ""
    tags: List[str] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []

@dataclass
class HotStockData:
    """热门股票数据结构"""
    symbol: str = ""
    name: str = ""
    current_price: Optional[float] = None
    change_percent: Optional[float] = None
    volume: Optional[int] = None
    reason: str = ""  # 上榜原因
    rank: Optional[int] = None
    heat_score: Optional[float] = None

@dataclass
class CommentData:
    """评论数据结构"""
    comment_id: str = ""
    content: str = ""
    author: str = ""
    author_id: str = ""
    publish_time: Optional[str] = None
    like_count: Optional[int] = None
    reply_count: Optional[int] = None
    parent_id: Optional[str] = None  # 父评论ID，用于回复
    sentiment: Optional[str] = None  # 情感分析结果：positive/negative/neutral

@dataclass
class UserPostData:
    """用户文章/动态数据结构"""
    post_id: str = ""
    content: str = ""
    author: str = ""
    author_id: str = ""
    author_avatar: str = ""
    publish_time: Optional[str] = None
    like_count: Optional[int] = None
    comment_count: Optional[int] = None
    repost_count: Optional[int] = None
    post_url: str = ""
    is_repost: bool = False
    original_post: Optional['UserPostData'] = None
    mentioned_stocks: List[str] = None
    images: List[str] = None

    def __post_init__(self):
        if self.mentioned_stocks is None:
            self.mentioned_stocks = []
        if self.images is None:
            self.images = []

@dataclass
class XueqiuHomePageData:
    """雪球首页数据结构"""
    hot_stocks: List[HotStockData] = None
    hot_topics: List[HotTopicData] = None
    user_posts: List[UserPostData] = None
    market_summary: Dict[str, Any] = None
    crawl_time: Optional[str] = None

    def __post_init__(self):
        if self.hot_stocks is None:
            self.hot_stocks = []
        if self.hot_topics is None:
            self.hot_topics = []
        if self.user_posts is None:
            self.user_posts = []
        if self.market_summary is None:
            self.market_summary = {}

@dataclass
class StockDetailData:
    """个股详细数据结构"""
    basic_info: StockData = None
    comments: List[CommentData] = None
    news: List[HotTopicData] = None
    financial_data: Dict[str, Any] = None
    technical_indicators: Dict[str, Any] = None

    def __post_init__(self):
        if self.comments is None:
            self.comments = []
        if self.news is None:
            self.news = []
        if self.financial_data is None:
            self.financial_data = {}
        if self.technical_indicators is None:
            self.technical_indicators = {}

class XueqiuExtractor:
    """雪球网站数据提取器"""
    
    def __init__(self):
        self.selectors = ExtractionConfig.XUEQIU_SELECTORS
        self.json_rules = ExtractionConfig.JSON_EXTRACTION_RULES
        
    def extract_from_html(self, html_content: str, url: str = None) -> Optional[StockData]:
        """从HTML内容提取股票数据"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 首先尝试从JavaScript变量中提取数据
            js_data = self._extract_from_javascript(html_content)
            if js_data:
                return js_data
                
            # 如果JS提取失败，尝试从HTML元素提取
            return self._extract_from_dom(soup, url)
            
        except Exception as e:
            logger.error(f"HTML提取失败: {e}")
            return None
    
    def _extract_from_javascript(self, html_content: str) -> Optional[StockData]:
        """从JavaScript变量中提取数据"""
        try:
            # 查找SNB数据的多种模式
            patterns = [
                r'SNB\s*=\s*\{[^{]*data:\s*\{[^{]*quote:\s*(\{[^}]+\})',
                r'window\.SNB\s*=\s*\{.*?data:\s*\{.*?quote:\s*(\{.*?\})',
                r'SNB\s*=\s*\{.*?quote:\s*(\{.*?\})',
                r'data:\s*\{.*?quote:\s*(\{[^}]*\})'
            ]

            for pattern in patterns:
                match = re.search(pattern, html_content, re.DOTALL)
                if match:
                    try:
                        quote_str = match.group(1)
                        # 清理和修复JSON字符串
                        quote_str = self._clean_json_string(quote_str)
                        quote_data = json.loads(quote_str)
                        result = self._parse_quote_data(quote_data)
                        if result:
                            return result
                    except Exception as e:
                        logger.debug(f"解析模式失败: {pattern[:50]}... - {e}")
                        continue

            # 尝试其他可能的数据源
            return self._extract_from_other_js_sources(html_content)

        except Exception as e:
            logger.error(f"JavaScript数据提取失败: {e}")
            return None
    
    def _clean_json_string(self, json_str: str) -> str:
        """清理JSON字符串"""
        # 移除注释
        json_str = re.sub(r'//.*?\n', '', json_str)
        # 移除多余的逗号
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)
        return json_str
    
    def _extract_from_other_js_sources(self, html_content: str) -> Optional[StockData]:
        """从其他JavaScript数据源提取"""
        patterns = [
            r'window\.STOCK_PAGE\s*=\s*true;.*?SNB\s*=\s*\{.*?data:\s*\{.*?quote:\s*(\{.*?\})',
            r'var\s+stockData\s*=\s*(\{.*?\});',
            r'window\.stockInfo\s*=\s*(\{.*?\});'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, html_content, re.DOTALL)
            if match:
                try:
                    data_str = match.group(1)
                    data_str = self._clean_json_string(data_str)
                    data = json.loads(data_str)
                    return self._parse_quote_data(data)
                except:
                    continue
                    
        return None
    
    def _parse_quote_data(self, quote_data: Dict) -> StockData:
        """解析quote数据为StockData对象"""
        try:
            return StockData(
                symbol=quote_data.get('symbol', ''),
                name=quote_data.get('name', ''),
                current_price=self._safe_float(quote_data.get('current')),
                change=self._safe_float(quote_data.get('chg')),
                change_percent=self._safe_float(quote_data.get('percent')),
                volume=self._safe_int(quote_data.get('volume')),
                amount=self._safe_float(quote_data.get('amount')),
                market_cap=self._safe_float(quote_data.get('market_capital')),
                pe_ttm=self._safe_float(quote_data.get('pe_ttm')),
                pb=self._safe_float(quote_data.get('pb')),
                high=self._safe_float(quote_data.get('high')),
                low=self._safe_float(quote_data.get('low')),
                open_price=self._safe_float(quote_data.get('open')),
                prev_close=self._safe_float(quote_data.get('last_close')),
                high_52w=self._safe_float(quote_data.get('high52w')),
                low_52w=self._safe_float(quote_data.get('low52w')),
                eps=self._safe_float(quote_data.get('eps')),
                dividend_yield=self._safe_float(quote_data.get('dividend_yield')),
                raw_data=quote_data
            )
        except Exception as e:
            logger.error(f"解析quote数据失败: {e}")
            return None
    
    def _extract_from_dom(self, soup: BeautifulSoup, url: str = None) -> Optional[StockData]:
        """从DOM元素提取数据"""
        try:
            # 从URL提取股票代码
            symbol = self._extract_symbol_from_url(url) if url else ''

            # 尝试多种选择器提取基本信息
            name_selectors = ['h1', '.stock-name', 'title', '[data-symbol]']
            price_selectors = ['.stock-current', '.current-price', '.price', '[data-current]']

            name = None
            for selector in name_selectors:
                name = self._extract_text(soup, selector)
                if name and symbol in name:
                    break

            current_price = None
            for selector in price_selectors:
                current_price = self._extract_number(soup, selector)
                if current_price:
                    break

            # 如果没有找到价格，尝试从文本中提取数字
            if not current_price and name:
                price_match = re.search(r'[\d,]+\.?\d*', name)
                if price_match:
                    try:
                        current_price = float(price_match.group().replace(',', ''))
                    except:
                        pass

            change = self._extract_number(soup, '.stock-change', '.change')
            change_percent = self._extract_number(soup, '.stock-percent', '.percent')

            # 如果有基本信息就返回
            if symbol or name or current_price:
                return StockData(
                    symbol=symbol,
                    name=name or '',
                    current_price=current_price,
                    change=change,
                    change_percent=change_percent
                )

            return None

        except Exception as e:
            logger.error(f"DOM提取失败: {e}")
            return None
    
    def _extract_symbol_from_url(self, url: str) -> str:
        """从URL提取股票代码"""
        match = re.search(r'/S/([A-Z0-9]+)', url)
        return match.group(1) if match else ''
    
    def _extract_text(self, soup: BeautifulSoup, *selectors) -> Optional[str]:
        """提取文本内容"""
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        return None
    
    def _extract_number(self, soup: BeautifulSoup, *selectors) -> Optional[float]:
        """提取数字"""
        text = self._extract_text(soup, *selectors)
        if text:
            # 清理数字字符串
            number_str = re.sub(r'[^\d.-]', '', text)
            try:
                return float(number_str)
            except:
                pass
        return None
    
    def _safe_float(self, value: Any) -> Optional[float]:
        """安全转换为float"""
        if value is None:
            return None
        try:
            return float(value)
        except:
            return None
    
    def _safe_int(self, value: Any) -> Optional[int]:
        """安全转换为int"""
        if value is None:
            return None
        try:
            return int(float(value))
        except:
            return None

    def _extract_text_by_selectors(self, element, selectors: List[str]) -> str:
        """使用多个选择器尝试提取文本"""
        for selector in selectors:
            try:
                text = self._extract_text(element, selector)
                if text and text.strip():
                    return text.strip()
            except:
                continue
        return ""

    def _extract_number_by_selectors(self, element, selectors: List[str]) -> Optional[float]:
        """使用多个选择器尝试提取数字"""
        for selector in selectors:
            try:
                number = self._extract_number(element, selector)
                if number is not None:
                    return number
            except:
                continue
        return None

    def _extract_attribute_by_selectors(self, element, selectors: List[str], attr: str) -> str:
        """使用多个选择器尝试提取属性"""
        for selector in selectors:
            try:
                elem = element.select_one(selector)
                if elem and elem.get(attr):
                    return elem.get(attr).strip()
            except:
                continue
        return ""

    def extract_homepage_data(self, html_content: str) -> Optional[XueqiuHomePageData]:
        """提取雪球首页数据"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 提取热门股票
            hot_stocks = self._extract_hot_stocks(soup, html_content)

            # 提取热门话题
            hot_topics = self._extract_hot_topics(soup, html_content)

            # 提取用户文章/动态
            user_posts = self._extract_user_posts(soup, html_content)

            # 提取市场概况
            market_summary = self._extract_market_summary(soup, html_content)

            return XueqiuHomePageData(
                hot_stocks=hot_stocks,
                hot_topics=hot_topics,
                user_posts=user_posts,
                market_summary=market_summary,
                crawl_time=self._get_current_time()
            )

        except Exception as e:
            logger.error(f"首页数据提取失败: {e}")
            return None

    def extract_stock_comments(self, html_content: str, symbol: str = None) -> List[CommentData]:
        """提取个股评论数据"""
        try:
            # 尝试从JSON API响应中提取
            if self._is_json_response(html_content):
                return self._extract_comments_from_json(html_content)

            # 从HTML页面提取
            soup = BeautifulSoup(html_content, 'html.parser')
            return self._extract_comments_from_html(soup)

        except Exception as e:
            logger.error(f"评论数据提取失败: {e}")
            return []

    def extract_stock_detail(self, html_content: str, url: str = None) -> Optional[StockDetailData]:
        """提取个股详细数据"""
        try:
            # 提取基本股票信息
            basic_info = self.extract_from_html(html_content, url)

            # 提取评论（如果页面包含）
            comments = self.extract_stock_comments(html_content)

            # 提取相关新闻
            news = self._extract_stock_news(html_content)

            # 提取财务数据
            financial_data = self._extract_financial_data(html_content)

            # 提取技术指标
            technical_indicators = self._extract_technical_indicators(html_content)

            return StockDetailData(
                basic_info=basic_info,
                comments=comments,
                news=news,
                financial_data=financial_data,
                technical_indicators=technical_indicators
            )

        except Exception as e:
            logger.error(f"个股详细数据提取失败: {e}")
            return None

    def _extract_hot_stocks(self, soup: BeautifulSoup, html_content: str) -> List[HotStockData]:
        """提取热门股票 - 专注HTML解析"""
        hot_stocks = []
        try:
            selectors = self.selectors.get("homepage_hot_stocks", {})

            # 尝试多种容器选择器
            container = None
            for container_selector in selectors.get("container", []):
                container = soup.select_one(container_selector)
                if container:
                    logger.debug(f"找到热门股票容器: {container_selector}")
                    break

            if not container:
                # 如果没有找到容器，直接在整个页面中查找
                container = soup
                logger.debug("未找到热门股票容器，在整个页面中查找")

            # 查找股票项目
            stock_elements = []
            for item_selector in selectors.get("item", []):
                elements = container.select(item_selector)
                if elements:
                    stock_elements = elements
                    logger.debug(f"找到 {len(elements)} 个股票项目: {item_selector}")
                    break

            # 提取每个股票的信息
            for i, element in enumerate(stock_elements[:10]):  # 限制前10个
                try:
                    # 提取股票代码 - 优先从href中提取
                    symbol = ""
                    for selector in selectors.get("symbol", []):
                        elem = element.select_one(selector)
                        if elem:
                            if selector == "a[href*='/S/']" and elem.get('href'):
                                href = elem.get('href', '')
                                if '/S/' in href:
                                    symbol = href.split('/S/')[-1]
                                    break
                            else:
                                symbol = elem.get_text(strip=True)
                                if symbol:
                                    break

                    # 提取股票名称
                    name = self._extract_text_by_selectors(element, selectors.get("name", []))

                    # 提取价格
                    price = self._extract_number_by_selectors(element, selectors.get("price", []))

                    # 提取涨跌幅
                    change_percent = self._extract_number_by_selectors(element, selectors.get("change_percent", []))

                    # 如果至少有股票代码或名称，就创建记录
                    if symbol or name:
                        hot_stock = HotStockData(
                            symbol=symbol or '',
                            name=name or '',
                            current_price=price,
                            change_percent=change_percent,
                            rank=i + 1
                        )
                        hot_stocks.append(hot_stock)
                        logger.debug(f"提取热门股票: {symbol} - {name}")

                except Exception as e:
                    logger.debug(f"提取单个热门股票失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"热门股票提取失败: {e}")

        logger.info(f"成功提取 {len(hot_stocks)} 个热门股票")
        return hot_stocks

    def _extract_hot_topics(self, soup: BeautifulSoup, html_content: str) -> List[HotTopicData]:
        """提取热门话题 - 专注HTML解析"""
        hot_topics = []
        try:
            selectors = self.selectors.get("homepage_hot_topics", {})

            # 尝试多种容器选择器
            container = None
            for container_selector in selectors.get("container", []):
                container = soup.select_one(container_selector)
                if container:
                    logger.debug(f"找到热门话题容器: {container_selector}")
                    break

            if not container:
                # 如果没有找到容器，直接在整个页面中查找
                container = soup
                logger.debug("未找到热门话题容器，在整个页面中查找")

            # 查找话题项目
            topic_elements = []
            for item_selector in selectors.get("item", []):
                elements = container.select(item_selector)
                if elements:
                    topic_elements = elements
                    logger.debug(f"找到 {len(elements)} 个话题项目: {item_selector}")
                    break

            # 提取每个话题的信息
            for element in topic_elements[:10]:  # 限制前10个
                try:
                    # 提取标题和链接 - 优先从链接元素提取
                    title = ""
                    url = ""

                    # 查找链接元素
                    for selector in selectors.get("title", []):
                        elem = element.select_one(selector)
                        if elem:
                            if elem.name == 'a' and elem.get('href'):
                                title = elem.get_text(strip=True)
                                url = elem.get('href', '')
                                break
                            elif not title:
                                title = elem.get_text(strip=True)

                    # 如果没有从title选择器找到URL，尝试url选择器
                    if not url:
                        url = self._extract_attribute_by_selectors(element, selectors.get("url", []), "href")

                    # 提取作者
                    author = self._extract_text_by_selectors(element, selectors.get("author", []))

                    # 提取浏览量
                    view_count = self._extract_number_by_selectors(element, selectors.get("view_count", []))

                    # 提取评论数
                    comment_count = self._extract_number_by_selectors(element, selectors.get("comment_count", []))

                    # 如果至少有标题，就创建记录
                    if title:
                        # 处理相对URL
                        if url and url.startswith('/'):
                            url = 'https://xueqiu.com' + url

                        hot_topic = HotTopicData(
                            title=title,
                            url=url or '',
                            view_count=self._safe_int(view_count),
                            comment_count=self._safe_int(comment_count),
                            author=author or ''
                        )
                        hot_topics.append(hot_topic)
                        logger.debug(f"提取热门话题: {title}")

                except Exception as e:
                    logger.debug(f"提取单个热门话题失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"热门话题提取失败: {e}")

        logger.info(f"成功提取 {len(hot_topics)} 个热门话题")
        return hot_topics

    def _extract_user_posts(self, soup: BeautifulSoup, html_content: str) -> List[UserPostData]:
        """提取用户文章/动态 - 专注HTML解析"""
        user_posts = []
        print(html_content)
        try:
            selectors = self.selectors.get("homepage_user_posts", {})

            # 尝试多种容器选择器
            container = None
            for container_selector in selectors.get("container", []):
                container = soup.select_one(container_selector)
                if container:
                    logger.debug(f"找到用户文章容器: {container_selector}")
                    break

            if not container:
                # 如果没有找到容器，直接在整个页面中查找
                container = soup
                logger.debug("未找到用户文章容器，在整个页面中查找")

            # 查找文章项目
            post_elements = []
            for item_selector in selectors.get("item", []):
                elements = container.select(item_selector)
                if elements:
                    post_elements = elements
                    logger.debug(f"找到 {len(elements)} 个文章项目: {item_selector}")
                    break

            # 提取每个文章的信息
            for i, element in enumerate(post_elements[:20]):  # 限制前20个
                try:
                    # 提取文章内容 - 支持HTML格式
                    content = self._extract_text_by_selectors(element, selectors.get("content", []))
                    
                    # 如果纯文本内容太短，尝试提取HTML内容
                    if not content or len(content.strip()) < 20:
                        html_content = self._extract_html_content_by_selectors(element, selectors.get("content", []))
                        if html_content and len(html_content.strip()) >= 50:  # HTML内容长度要求稍高
                            content = html_content
                    
                    # 如果仍然没有内容，跳过
                    if not content or len(content.strip()) < 20:
                        continue

                    # 提取作者信息
                    author = ""
                    author_id = ""
                    author_url = ""

                    for selector in selectors.get("author", []):
                        author_elem = element.select_one(selector)
                        if author_elem:
                            author = author_elem.get_text(strip=True)
                            author_url = author_elem.get('href', '')
                            if author_url and '/' in author_url:
                                # 从URL中提取用户ID
                                parts = author_url.strip('/').split('/')
                                if parts and parts[-1].isdigit():
                                    author_id = parts[-1]
                            break

                    # 提取头像
                    author_avatar = self._extract_attribute_by_selectors(
                        element, selectors.get("author_avatar", []), "src"
                    )

                    # 提取发布时间
                    publish_time = self._extract_text_by_selectors(element, selectors.get("publish_time", []))

                    # 提取互动数据
                    like_count = self._extract_number_by_selectors(element, selectors.get("like_count", []))
                    comment_count = self._extract_number_by_selectors(element, selectors.get("comment_count", []))
                    repost_count = self._extract_number_by_selectors(element, selectors.get("repost_count", []))

                    # 提取文章URL
                    post_url = self._extract_attribute_by_selectors(
                        element, selectors.get("post_url", []), "href"
                    )

                    # 提取提到的股票
                    mentioned_stocks = []
                    stock_links = element.select("a[href*='/S/']")
                    for link in stock_links:
                        href = link.get('href', '')
                        if '/S/' in href:
                            symbol = href.split('/S/')[-1]
                            if symbol not in mentioned_stocks:
                                mentioned_stocks.append(symbol)

                    # 提取图片
                    images = []
                    for selector in selectors.get("images", []):
                        img_elements = element.select(selector)
                        for img in img_elements:
                            src = img.get('src', '')
                            if src and src not in images:
                                images.append(src)

                    # 检查是否为转发
                    is_repost = bool(element.select_one('.retweeted_content_27I'))

                    # 创建文章数据
                    if content and author:
                        post = UserPostData(
                            post_id=str(i + 1),  # 简单的ID生成
                            content=content.strip(),
                            author=author,
                            author_id=author_id,
                            author_avatar=author_avatar or '',
                            publish_time=publish_time or '',
                            like_count=self._safe_int(like_count),
                            comment_count=self._safe_int(comment_count),
                            repost_count=self._safe_int(repost_count),
                            post_url=post_url or '',
                            is_repost=is_repost,
                            mentioned_stocks=mentioned_stocks,
                            images=images
                        )
                        user_posts.append(post)
                        logger.debug(f"提取用户文章: {author} - {content[:50]}...")

                except Exception as e:
                    logger.debug(f"提取单个用户文章失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"用户文章提取失败: {e}")

        logger.info(f"成功提取 {len(user_posts)} 篇用户文章")
        return user_posts

    def _extract_market_summary(self, soup: BeautifulSoup, html_content: str) -> Dict[str, Any]:
        """提取市场概况"""
        market_summary = {}
        try:
            # 尝试从JavaScript数据中提取市场指数
            js_pattern = r'marketData["\']?\s*:\s*(\{.*?\})'
            match = re.search(js_pattern, html_content, re.DOTALL)
            if match:
                try:
                    market_data = json.loads(match.group(1))
                    market_summary.update(market_data)
                except:
                    pass

            # 从HTML元素提取市场指数
            index_elements = soup.select('.market-index, .index-item')
            for element in index_elements:
                try:
                    name = self._extract_text(element, '.index-name, .name')
                    value = self._extract_number(element, '.index-value, .value')
                    change = self._extract_number(element, '.index-change, .change')
                    if name and value:
                        market_summary[name] = {
                            'value': value,
                            'change': change
                        }
                except:
                    continue

        except Exception as e:
            logger.error(f"市场概况提取失败: {e}")

        return market_summary

    def _extract_comments_from_json(self, json_content: str) -> List[CommentData]:
        """从JSON响应中提取评论"""
        comments = []
        try:
            data = json.loads(json_content)

            # 雪球API的评论数据结构
            if 'list' in data:
                comment_list = data['list']
            elif 'data' in data and isinstance(data['data'], list):
                comment_list = data['data']
            else:
                comment_list = []

            for comment_data in comment_list:
                try:
                    comment = CommentData(
                        comment_id=str(comment_data.get('id', '')),
                        content=comment_data.get('text', ''),
                        author=comment_data.get('user', {}).get('screen_name', ''),
                        author_id=str(comment_data.get('user', {}).get('id', '')),
                        publish_time=comment_data.get('created_at', ''),
                        like_count=self._safe_int(comment_data.get('fav_count')),
                        reply_count=self._safe_int(comment_data.get('reply_count'))
                    )
                    comments.append(comment)
                except:
                    continue

        except Exception as e:
            logger.error(f"JSON评论提取失败: {e}")

        return comments

    def _extract_comments_from_html(self, soup: BeautifulSoup) -> List[CommentData]:
        """从HTML页面中提取评论 - 确保数据结构与API一致"""
        comments = []
        try:
            # 首先尝试从页面中的JavaScript变量提取评论数据
            js_comments = self._extract_comments_from_js_variables(soup)
            if js_comments:
                logger.info(f"从JavaScript变量中提取到 {len(js_comments)} 条评论")
                return js_comments

            # 如果JS提取失败，尝试从HTML DOM结构提取
            dom_comments = self._extract_comments_from_dom(soup)
            if dom_comments:
                logger.info(f"从DOM结构中提取到 {len(dom_comments)} 条评论")
                return dom_comments

            logger.warning("未能从HTML页面中提取到评论数据")
            return []

        except Exception as e:
            logger.error(f"HTML评论提取失败: {e}")
            return []

    def _extract_comments_from_js_variables(self, soup: BeautifulSoup) -> List[CommentData]:
        """从页面JavaScript变量中提取评论数据"""
        comments = []
        try:
            # 查找包含评论数据的script标签
            script_tags = soup.find_all('script', string=True)

            for script in script_tags:
                script_content = script.string
                if not script_content:
                    continue

                # 查找可能包含评论数据的JavaScript变量
                # 雪球页面通常在window.SNB或类似变量中存储数据
                patterns = [
                    r'window\.SNB\s*=\s*({.*?});',
                    r'window\.INITIAL_STATE\s*=\s*({.*?});',
                    r'__INITIAL_STATE__\s*=\s*({.*?});',
                    r'window\.__NUXT__\s*=\s*({.*?});',
                    r'window\.pageData\s*=\s*({.*?});'
                ]

                for pattern in patterns:
                    import re
                    matches = re.search(pattern, script_content, re.DOTALL)
                    if matches:
                        try:
                            data_str = matches.group(1)
                            # 尝试解析JSON数据
                            data = json.loads(data_str)

                            # 在数据中查找评论
                            found_comments = self._find_comments_in_data(data)
                            if found_comments:
                                comments.extend(found_comments)
                                logger.debug(f"从JS变量中找到 {len(found_comments)} 条评论")

                        except (json.JSONDecodeError, Exception) as e:
                            logger.debug(f"解析JS数据失败: {e}")
                            continue

        except Exception as e:
            logger.debug(f"JS变量评论提取失败: {e}")

        return comments

    def _find_comments_in_data(self, data: dict, path: str = "") -> List[CommentData]:
        """在嵌套数据结构中递归查找评论"""
        comments = []

        if isinstance(data, dict):
            # 检查是否是评论列表
            if self._is_comment_list(data):
                comments.extend(self._parse_comment_list(data))

            # 递归搜索所有字段
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    sub_comments = self._find_comments_in_data(value, f"{path}.{key}")
                    comments.extend(sub_comments)

        elif isinstance(data, list):
            for i, item in enumerate(data):
                if isinstance(item, (dict, list)):
                    sub_comments = self._find_comments_in_data(item, f"{path}[{i}]")
                    comments.extend(sub_comments)

        return comments

    def _is_comment_list(self, data: dict) -> bool:
        """判断数据是否为评论列表结构"""
        # 检查是否包含评论列表的特征字段
        if 'list' in data and isinstance(data['list'], list):
            # 检查列表中的项目是否像评论
            if data['list'] and isinstance(data['list'][0], dict):
                first_item = data['list'][0]
                # 检查是否包含评论的关键字段
                comment_fields = ['text', 'content', 'user', 'created_at', 'id']
                return any(field in first_item for field in comment_fields)
        return False

    def _parse_comment_list(self, data: dict) -> List[CommentData]:
        """解析评论列表数据，确保与API结构一致"""
        comments = []
        comment_list = data.get('list', [])

        for comment_data in comment_list:
            try:
                # 按照API数据结构解析
                comment = CommentData(
                    comment_id=str(comment_data.get('id', '')),
                    content=comment_data.get('text', comment_data.get('content', '')),
                    author=comment_data.get('user', {}).get('screen_name', '') if isinstance(comment_data.get('user'), dict) else str(comment_data.get('user', '')),
                    author_id=str(comment_data.get('user', {}).get('id', '')) if isinstance(comment_data.get('user'), dict) else '',
                    publish_time=comment_data.get('created_at', ''),
                    like_count=self._safe_int(comment_data.get('fav_count')),
                    reply_count=self._safe_int(comment_data.get('reply_count'))
                )

                # 只添加有内容的评论
                if comment.content and comment.content.strip():
                    comments.append(comment)

            except Exception as e:
                logger.debug(f"解析单条评论失败: {e}")
                continue

        return comments

    def _extract_comments_from_dom(self, soup: BeautifulSoup) -> List[CommentData]:
        """从DOM结构中提取评论数据"""
        comments = []
        try:
            selectors = self.selectors.get("comments", {})

            # 扩展的评论容器选择器，针对雪球页面结构
            extended_container_selectors = [
                ".timeline",
                ".status-list",
                ".comment-list",
                ".comments-section",
                "[class*='timeline']",
                "[class*='status']",
                "[class*='comment']",
                ".feed-list",
                ".discussion-list"
            ]

            # 查找评论容器
            container = None
            all_selectors = selectors.get("container", []) + extended_container_selectors

            for container_selector in all_selectors:
                container = soup.select_one(container_selector)
                if container:
                    logger.debug(f"找到评论容器: {container_selector}")
                    break

            if not container:
                container = soup
                logger.debug("未找到评论容器，在整个页面中查找")

            # 扩展的评论项目选择器
            extended_item_selectors = [
                ".status-item",
                ".timeline-item",
                ".comment-item",
                "[class*='status-item']",
                "[class*='timeline-item']",
                "[class*='comment-item']",
                ".feed-item",
                "li[class*='item']",
                "div[class*='item']"
            ]

            # 查找评论项目
            comment_elements = []
            all_item_selectors = selectors.get("item", []) + extended_item_selectors

            for item_selector in all_item_selectors:
                elements = container.select(item_selector)
                if elements:
                    comment_elements = elements
                    logger.debug(f"找到 {len(elements)} 个评论项目: {item_selector}")
                    break

            # 提取每个评论的信息
            for i, element in enumerate(comment_elements):
                try:
                    comment = self._extract_single_comment_from_element(element, i, selectors)
                    if comment and comment.content.strip():
                        comments.append(comment)
                        logger.debug(f"提取评论: {comment.author} - {comment.content[:50]}...")

                except Exception as e:
                    logger.debug(f"提取单个评论失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"DOM评论提取失败: {e}")

        return comments

    def _extract_single_comment_from_element(self, element, index: int, selectors: dict) -> CommentData:
        """从单个DOM元素中提取评论数据"""
        # 扩展的内容选择器
        extended_content_selectors = [
            ".status-content",
            ".comment-content",
            ".text",
            ".content",
            "[class*='content']",
            "[class*='text']",
            "p",
            ".desc"
        ]

        # 扩展的作者选择器
        extended_author_selectors = [
            ".user-name",
            ".author",
            ".username",
            "[class*='user-name']",
            "[class*='author']",
            "[class*='username']",
            "a[href*='/u/']",
            ".nickname"
        ]

        # 扩展的时间选择器
        extended_time_selectors = [
            ".publish-time",
            ".time",
            ".created-at",
            "[class*='time']",
            "[class*='date']",
            "time",
            ".timestamp"
        ]

        # 扩展的点赞数选择器
        extended_like_selectors = [
            ".like-count",
            ".fav-count",
            ".likes",
            "[class*='like']",
            "[class*='fav']",
            "[class*='praise']"
        ]

        # 提取数据
        all_content_selectors = selectors.get("content", []) + extended_content_selectors
        all_author_selectors = selectors.get("author", []) + extended_author_selectors
        all_time_selectors = selectors.get("time", []) + extended_time_selectors
        all_like_selectors = selectors.get("like_count", []) + extended_like_selectors

        content = self._extract_text_by_selectors(element, all_content_selectors)
        author = self._extract_text_by_selectors(element, all_author_selectors)
        publish_time = self._extract_text_by_selectors(element, all_time_selectors)
        like_count = self._extract_number_by_selectors(element, all_like_selectors)

        # 如果没有找到纯文本内容，尝试提取HTML内容
        if not content:
            html_content = self._extract_html_content_by_selectors(element, all_content_selectors)
            if html_content and len(html_content.strip()) >= 20:
                content = html_content
        
        # 如果仍然没有内容，尝试从整个元素提取
        if not content:
            content = element.get_text(strip=True)

        # 过滤无效评论
        if self._is_invalid_comment_content(content, author):
            return None

        # 尝试提取作者ID（从链接href或data属性）
        author_id = ""
        try:
            author_link = element.select_one("a[href*='/u/']")
            if author_link:
                href = author_link.get('href', '')
                import re
                match = re.search(r'/u/(\d+)', href)
                if match:
                    author_id = match.group(1)
        except:
            pass

        # 生成评论ID（尝试从data属性或使用索引）
        comment_id = str(index + 1000)
        try:
            data_id = element.get('data-id') or element.get('id')
            if data_id:
                comment_id = str(data_id)
        except:
            pass

        return CommentData(
            comment_id=comment_id,
            content=content.strip() if content else '',
            author=author.strip() if author else f'用户{index + 1}',
            author_id=author_id or str(index + 2000),
            publish_time=publish_time.strip() if publish_time else '2024-01-15T10:00:00',
            like_count=self._safe_int(like_count),
            reply_count=0  # DOM中通常难以提取回复数
        )

    def _is_invalid_comment_content(self, content: str, author: str) -> bool:
        """判断是否为无效评论内容"""
        if not content or len(content) < 10:
            return True

        # 过滤导航和标签文本
        invalid_patterns = [
            '全部讨论交易资讯公告',
            '新帖热帖',
            '全部',
            '讨论',
            '交易',
            '资讯',
            '公告',
            '新帖',
            '热帖',
            '加载更多',
            '查看更多',
            '展开',
            '收起'
        ]

        for pattern in invalid_patterns:
            if pattern in content:
                return True

        # 过滤无效作者
        if author in ['全部', '新帖', '热帖', '讨论', '交易', '资讯', '公告']:
            return True

        # 如果文本主要是标点符号或数字
        import re
        if re.match(r'^[\s\d\.\-\+%]+$', content):
            return True

        return False

    def _extract_stock_news(self, html_content: str) -> List[HotTopicData]:
        """提取个股相关新闻"""
        news = []
        try:
            # 尝试从JavaScript数据中提取
            js_pattern = r'newsData["\']?\s*:\s*(\[.*?\])'
            match = re.search(js_pattern, html_content, re.DOTALL)
            if match:
                try:
                    news_data = json.loads(match.group(1))
                    for item in news_data:
                        news_item = HotTopicData(
                            title=item.get('title', ''),
                            url=item.get('url', ''),
                            publish_time=item.get('created_at', ''),
                            summary=item.get('summary', '')
                        )
                        news.append(news_item)
                except:
                    pass

        except Exception as e:
            logger.error(f"个股新闻提取失败: {e}")

        return news

    def _extract_financial_data(self, html_content: str) -> Dict[str, Any]:
        """提取财务数据"""
        financial_data = {}
        try:
            # 尝试从JavaScript数据中提取财务指标
            js_pattern = r'financialData["\']?\s*:\s*(\{.*?\})'
            match = re.search(js_pattern, html_content, re.DOTALL)
            if match:
                try:
                    data = json.loads(match.group(1))
                    financial_data.update(data)
                except:
                    pass

        except Exception as e:
            logger.error(f"财务数据提取失败: {e}")

        return financial_data

    def _extract_technical_indicators(self, html_content: str) -> Dict[str, Any]:
        """提取技术指标"""
        technical_indicators = {}
        try:
            # 尝试从JavaScript数据中提取技术指标
            js_pattern = r'technicalData["\']?\s*:\s*(\{.*?\})'
            match = re.search(js_pattern, html_content, re.DOTALL)
            if match:
                try:
                    data = json.loads(match.group(1))
                    technical_indicators.update(data)
                except:
                    pass

        except Exception as e:
            logger.error(f"技术指标提取失败: {e}")

        return technical_indicators

    def _is_json_response(self, content: str) -> bool:
        """判断是否为JSON响应"""
        try:
            json.loads(content)
            return True
        except:
            return False

    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().isoformat()

    def _extract_html_content(self, soup: BeautifulSoup, *selectors) -> Optional[str]:
        """提取HTML内容，保留基本格式"""
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return self._clean_html_content(element)
        return None
    
    def _extract_html_content_by_selectors(self, element, selectors: List[str]) -> str:
        """使用多个选择器尝试提取HTML内容"""
        for selector in selectors:
            try:
                html_content = self._extract_html_content(element, selector)
                if html_content and html_content.strip():
                    return html_content.strip()
            except:
                continue
        return ""
    
    def _clean_html_content(self, element) -> str:
        """清理HTML内容，保留基本格式但移除不必要的标签"""
        if not element:
            return ""
        
        # 导入配置
        from config import ExtractionConfig
        html_config = ExtractionConfig.HTML_EXTRACTION_CONFIG
        
        # 检查是否启用HTML内容提取
        if not html_config.get("enable_html_content", True):
            return element.get_text(strip=True)
        
        # 检查爬虫应用是否启用HTML内容提取
        # 这里需要通过某种方式获取爬虫应用的配置
        # 暂时使用配置文件中的设置
        
        # 创建副本避免修改原始元素
        try:
            # 使用BeautifulSoup的copy方法
            if hasattr(element, 'copy'):
                element_copy = element.copy()
            else:
                # 如果没有copy方法，创建新的BeautifulSoup对象
                element_copy = BeautifulSoup(str(element), 'html.parser')
        except Exception as e:
            logger.warning(f"无法创建元素副本: {e}")
            # 如果都失败了，直接使用原始元素
            element_copy = element
        
        # 移除不需要的标签
        remove_tags = html_config.get("remove_tags", ["script", "style", "noscript"])
        for tag_name in remove_tags:
            for tag in element_copy.find_all(tag_name):
                tag.decompose()
        
        # 处理标签和属性
        allowed_tags = html_config.get("allowed_tags", [])
        allowed_attrs = html_config.get("allowed_attributes", [])
        
        for tag in element_copy.find_all(True):
            # 如果标签不在允许列表中，转换为纯文本
            if allowed_tags and tag.name not in allowed_tags:
                tag.unwrap()
                continue
            
            # 移除不必要的属性
            attrs = dict(tag.attrs)
            for attr in list(attrs.keys()):
                if attr not in allowed_attrs:
                    del tag[attr]
            
            # 移除事件处理器
            for attr in list(attrs.keys()):
                if attr.startswith('on'):
                    del tag[attr]
        
        # 转换为字符串
        html_str = str(element_copy)
        
        # 清理多余的空白字符
        import re
        html_str = re.sub(r'\s+', ' ', html_str)
        html_str = re.sub(r'>\s+<', '><', html_str)
        
        return html_str.strip()
    
    def _extract_text(self, soup: BeautifulSoup, *selectors) -> Optional[str]:
        """提取文本内容"""
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        return None

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_stock_data(data: StockData) -> bool:
        """验证股票数据的完整性"""
        if not data:
            return False
            
        # 必须有股票代码和名称
        if not data.symbol or not data.name:
            return False
            
        # 价格数据应该合理
        if data.current_price is not None and data.current_price <= 0:
            return False
            
        return True
    
    @staticmethod
    def clean_stock_data(data: StockData) -> StockData:
        """清理股票数据"""
        if not data:
            return data
            
        # 清理股票代码
        if data.symbol:
            data.symbol = data.symbol.strip().upper()
            
        # 清理股票名称
        if data.name:
            data.name = data.name.strip()
            
        return data

class MultiSourceExtractor:
    """多数据源提取器"""
    
    def __init__(self):
        self.xueqiu_extractor = XueqiuExtractor()
        self.validator = DataValidator()
        
    def extract(self, html_content: str, url: str = None, source: str = 'xueqiu') -> Optional[StockData]:
        """从多个数据源提取数据"""
        try:
            if source == 'xueqiu':
                data = self.xueqiu_extractor.extract_from_html(html_content, url)
            else:
                logger.warning(f"不支持的数据源: {source}")
                return None
                
            if data and self.validator.validate_stock_data(data):
                return self.validator.clean_stock_data(data)
                
            return None
            
        except Exception as e:
            logger.error(f"数据提取失败: {e}")
            return None

    def extract_homepage(self, html_content: str, source: str = 'xueqiu') -> Optional[XueqiuHomePageData]:
        """提取首页数据"""
        try:
            if source == 'xueqiu':
                return self.xueqiu_extractor.extract_homepage_data(html_content)
            else:
                logger.warning(f"不支持的数据源: {source}")
                return None
        except Exception as e:
            logger.error(f"首页数据提取失败: {e}")
            return None

    def extract_stock_detail(self, html_content: str, url: str = None, source: str = 'xueqiu') -> Optional[StockDetailData]:
        """提取个股详细数据"""
        try:
            if source == 'xueqiu':
                return self.xueqiu_extractor.extract_stock_detail(html_content, url)
            else:
                logger.warning(f"不支持的数据源: {source}")
                return None
        except Exception as e:
            logger.error(f"个股详细数据提取失败: {e}")
            return None

    def extract_comments(self, html_content: str, source: str = 'xueqiu') -> List[CommentData]:
        """提取评论数据"""
        try:
            if source == 'xueqiu':
                return self.xueqiu_extractor.extract_stock_comments(html_content)
            else:
                logger.warning(f"不支持的数据源: {source}")
                return []
        except Exception as e:
            logger.error(f"评论数据提取失败: {e}")
            return []
