#!/usr/bin/env python3
"""
简单的守护进程测试脚本
用于诊断容器启动问题
"""
import os
import sys
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_daemon.log', mode='w'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_daemon')

def main():
    """主函数"""
    logger.info("=== 简单守护进程测试开始 ===")
    
    # 检查环境
    logger.info(f"当前工作目录: {os.getcwd()}")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"Python路径: {sys.path}")
    
    # 检查文件
    files_to_check = [
        'daemon_config.json',
        'start_daemon.sh',
        'crawler_daemon.py',
        'requirements.txt'
    ]
    
    for file_name in files_to_check:
        if os.path.exists(file_name):
            logger.info(f"✓ 文件存在: {file_name}")
        else:
            logger.error(f"✗ 文件不存在: {file_name}")
    
    # 检查环境变量
    env_vars = ['PATH', 'PYTHONPATH', 'HOME']
    for var in env_vars:
        value = os.environ.get(var, '未设置')
        logger.info(f"环境变量 {var}: {value}")
    
    # 模拟守护进程运行
    logger.info("开始模拟守护进程运行...")
    try:
        for i in range(10):
            logger.info(f"守护进程运行中... ({i+1}/10)")
            time.sleep(5)
        logger.info("测试完成，守护进程正常运行")
    except KeyboardInterrupt:
        logger.info("收到中断信号，退出测试")
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
