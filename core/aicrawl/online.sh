#!/bin/bash

# ！！！！！！！！这个作为线上服务的本地补充服务，由于爬虫会被封ip等特殊性，通过本地服务运行补充数据
# deploy-cron使用本地数据库,其它服务都使用线上数据库,

# global
export TZ=Asia/Shanghai
export BB_VERSION=V6.0.0
export DOCKER_DEFAULT_PLATFORM=linux/amd64

# mysql
# export BB_MYSQL_HOST=**************
# 本地mysql
# export BB_MYSQL_HOST=127.0.0.1
# export BB_MYSQL_DBNAME=blackbear
# export BB_MYSQL_USER=mizhdi
# export BB_MYSQL_PASSWD=mzd12345
# export BB_MYSQL_PORT=3308

# mysql 线上
export BB_MYSQL_HOST=rm-8vbi3iof9aug3vo89go.mysql.zhangbei.rds.aliyuncs.com
export BB_MYSQL_DBNAME=blackbear
export BB_MYSQL_USER=mizhdi
export BB_MYSQL_PASSWD=mizdiI38193857OI
export BB_MYSQL_PORT=3306

# redis
export BB_REDIS_HOST=r-8vbgj5rh2xed7oropqpd.redis.zhangbei.rds.aliyuncs.com
export BB_REDIS_PORT=6379
export BB_REDIS_PASSWORD=mizdiI38193857OI
export BB_REDIS_DB0=0
export BB_REDIS_CRAWL_DB=8
export BB_REDIS_WS_DB=3
export BB_REDIS_BACKEND_DB=0
export BB_REDIS_QUANT_DB=5
export BB_REDIS_CONN_DB=2

# redis 本地
# export BB_REDIS_HOST=**************
# export BB_REDIS_PORT=6379
# export BB_REDIS_PASSWORD=12345
# export BB_REDIS_DB0=0
# export BB_REDIS_CRAWL_DB=8
# export BB_REDIS_WS_DB=3
# export BB_REDIS_BACKEND_DB=0
# export BB_REDIS_QUANT_DB=5
# export BB_REDIS_CONN_DB=2

# 各个项目地址
export BB_PROXY_URL=http://***********:5010
# export BB_PROXY_URL=http://**************:5010
export BB_SCRAPY_URL=http://**************:6800
export BB_TASK_URL=http://**************:5920
export BB_FRONTEND_URL=http://**************:80
# 用ip防止跨域
export BB_API_URL=http://127.0.0.1:7001
export BB_WEB_URL=http://**************:3000
export BB_ASSETS_URL=https://assets.finevent.top

# 根据oss切换同 ossHost
export BB_IMAGE_URL=http://**************:9000

# crawl
export BB_SCRAPY_PROXY=True
export BB_SCRAPY_DEVELOPMENT=True


#  --------- cron -------------------
export BB_CRON_MYSQL_HOST=**************
export BB_CRON_MYSQL_DBNAME=blackbear-deploy
export BB_CRON_MYSQL_USER=mizhdi
export BB_CRON_MYSQL_PASSWD=mzd12345
export BB_CRON_MYSQL_PORT=3306

#  --------- backend -------------------
# aliyun oss (0)和minio(1)切换
export BB_UseOSS=1
export BB_BB_OssBucketName=blackbear
# 统一oss地址
export BB_OssHost=http://**************:9000
# minio
export BB_MinioEndPoint=**************
export BB_MinioPort=9000
export BB_MinioAccessKey=xabALv8xA6q9HBqB6NbT
export BB_MinioSecretKey=bCSy5VQSrluC0KAkKbLyzNHHldFgU7j9dgBFEiT8

# aliyun oss
export BB_OssAccessKeyId=LTAI5t8GvEDoDmDMWEcks6Yo
export BB_OssAccessKeySecret=******************************
export BB_OssBucket=finevent-images
export BB_OssEndpoint=oss-cn-zhangjiakou.aliyuncs.com

# mail
export BB_MailerHost=smtp.qq.com
export BB_MailerPort=465
export BB_MailerAuthUser=<EMAIL>
export BB_MailerAuthPass=xasyimamhyowecgf

# alinode
export BB_AlinodeAppid=82535
export BB_AlinodeSecret=fe8c9ef71d388154332a14700b347d51b4d74534

# wechat
export BB_WechatApiAppId=wx053f8547616de1ed
export BB_WechatApiAppSecret=72b907c5a7e15f2c5c21e9df5760f5dc

# weibo
export BB_PassportWeiboKey=*********
export BB_PassportWeiboSecret=e0febb7ae543ddae0483299759da5515
export BB_WeiboPassportRedirect=http://**************:80/#/loginsuccess

export BB_BingIndexNowKey=********************************
export BB_SystemUserId=fetadmin-xxxx-oooo
export BB_AdminUsername=admin.top

# alisms
export BB_ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5t8GvEDoDmDMWEcks6Yo
export BB_ALIBABA_CLOUD_ACCESS_KEY_SECRET=******************************

export BB_ENABLE_NODE_LOG=YES
export BB_AKToolsHost=http://***********:8888
export BB_CACHE_ENABLED=true

# ==================== 新增优化配置 ====================

# JWT配置
export BB_JWT_SECRET=blackbear-production-jwt-secret-key-CHANGE-IN-PRODUCTION-2025
export BB_JWT_EXPIRES_IN=30d

# 安全配置（实际使用的）
export BB_ENHANCED_SECURITY_ENABLED=true
export BB_XSS_FILTER_ENABLED=true
export BB_SQL_INJECTION_FILTER_ENABLED=true

# 监控配置（实际使用的）
export BB_APM_ENABLED=true

# 性能配置（实际使用的）
export BB_RESPONSE_COMPRESSION_ENABLED=true
export BB_CACHE_OPTIMIZATION_ENABLED=true

# 日志配置
export BB_LOG_LEVEL=INFO
export BB_CONSOLE_LOG_LEVEL=INFO

# 限流配置（实际使用的）
export BB_RATE_LIMIT_ENABLED=true
export BB_RATE_LIMIT_MAX=1000
export BB_RATE_LIMIT_DURATION=900000

# 开发环境特殊配置
export BB_EGG_KEYS=blackbear-production-egg-keys-CHANGE-IN-PRODUCTION-2025
export NODE_ENV=development
export EGG_SERVER_ENV=local

# 端口配置
export BB_PORT=7001
export BB_HOSTNAME=0.0.0.0

# Redis连接优化（实际使用的）
export BB_REDIS_CONNECT_TIMEOUT=10000
export BB_REDIS_COMMAND_TIMEOUT=5000
export BB_REDIS_RETRY_DELAY=100
export BB_REDIS_MAX_RETRIES=3

# 文件上传配置
export BB_UPLOAD_FILE_SIZE=10mb

# CORS配置（实际使用的）
export BB_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://**************:80

# 会话配置
export BB_SESSION_MAX_AGE=86400000

# 缓存配置（实际使用的）
export BB_CACHE_DEFAULT_TTL=300
export BB_CACHE_L1_TTL=60
export BB_CACHE_L2_TTL=300
export BB_CACHE_L3_TTL=3600

# 性能监控配置
export BB_PERFORMANCE_MONITORING=true
export BB_SLOW_QUERY_THRESHOLD=1000
export BB_MEMORY_USAGE_WARNING=0.8

# 安全响应头配置
export BB_SECURITY_HEADERS_ENABLED=true
export BB_CONTENT_SECURITY_POLICY_ENABLED=true

# IP白名单（开发环境）
export BB_IP_WHITELIST=127.0.0.1,::1,localhost

# IP黑名单（开发环境）
export BB_IP_BLACKLIST=

export BB_REDIS_CACHE_DB4=4

# 调试配置
export BB_DEBUG_ENABLED=true
export BB_TRACE_ENABLED=false

# 环境信息打印函数
print_env_info() {
    echo "✅ Blackbear优化配置已加载"
    echo "🔧 环境: development"
    echo "🗄️  数据库: rm-8vbi3iof9aug3vo89go.mysql.zhangbei.rds.aliyuncs.com:3306/blackbear"
    echo "📦 Redis: r-8vbgj5rh2xed7oropqpd.redis.zhangbei.rds.aliyuncs.com:6379"
    echo "🌐 API地址: http://127.0.0.1:7001"
    echo "🛡️  安全增强: 已启用"
    echo "📊 监控系统: 已启用"

    # 详细环境信息
    echo ""
    echo "🐍 Python: $(python3 --version 2>/dev/null || echo '未安装')"
    echo "📦 Node.js: $(node --version 2>/dev/null || echo '未安装')"
    echo "🔧 NPM: $(npm --version 2>/dev/null || echo '未安装')"

    # 服务状态检查
    echo ""
    echo "🔍 服务状态检查:"

    # MySQL连接测试
    if command -v mysql &> /dev/null; then
        if timeout 3 mysql -h "rm-8vbi3iof9aug3vo89go.mysql.zhangbei.rds.aliyuncs.com" -P "3306" -u "mizhdi" -p"mizdiI38193857OI" -e "SELECT 1" 2>/dev/null >/dev/null; then
            echo "  ✅ MySQL: 连接正常"
        else
            echo "  ❌ MySQL: 连接失败"
        fi
    else
        echo "  ⚠️  MySQL: 客户端未安装"
    fi

    # Redis连接测试
    if command -v redis-cli &> /dev/null; then
        if timeout 3 redis-cli -h "r-8vbgj5rh2xed7oropqpd.redis.zhangbei.rds.aliyuncs.com" -p "6379" -a "mizdiI38193857OI" ping 2>/dev/null | grep -q "PONG"; then
            echo "  ✅ Redis: 连接正常"
        else
            echo "  ❌ Redis: 连接失败"
        fi
    else
        echo "  ⚠️  Redis: 客户端未安装"
    fi

    # API服务测试
    if timeout 3 curl -s "http://127.0.0.1:7001/api/web/sidebar" >/dev/null 2>&1; then
        echo "  ✅ API服务: 运行正常"
    else
        echo "  ❌ API服务: 无响应"
    fi

    # 进程检查
    if pgrep -f "node.*egg-scripts" >/dev/null 2>&1; then
        echo "  ✅ 后端进程: 运行中"
    else
        echo "  ❌ 后端进程: 未运行"
    fi

    if pgrep -f "scrapy" >/dev/null 2>&1; then
        echo "  ✅ 爬虫进程: 运行中"
    else
        echo "  ❌ 爬虫进程: 未运行"
    fi

    echo ""
    echo "📋 主要服务地址:"
    echo "  🌐 API: http://127.0.0.1:7001"
    echo "  🖥️  前端: http://**************:80"
    echo "  🕸️  Web: http://**************:3000"
    echo "  🔄 代理: http://***********:5010"
    echo "  🕷️  Scrapy: http://**************:6800"
    echo "  📸 图片: http://**************:9000"
}

# 调用函数显示环境信息
print_env_info