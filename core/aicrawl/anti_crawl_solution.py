#!/usr/bin/env python3
"""
反爬验证解决方案
通过模拟真实浏览器行为来绕过反爬验证
"""
import asyncio
import logging
import sys
import os
import time
import random

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawler_engine import XueqiuCrawler

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AntiCrawlSolution:
    """反爬验证解决方案"""
    
    def __init__(self):
        self.crawler = XueqiuCrawler(enable_anti_detection=True)
        
    async def solve_with_real_browser(self, url: str):
        """使用真实浏览器行为解决反爬验证"""
        try:
            print(f"开始使用真实浏览器行为访问: {url}")
            
            # 模拟真实用户行为
            await self._simulate_human_behavior()
            
            # 使用增强的爬取方法
            result = await self._enhanced_crawl(url)
            
            return result
            
        except Exception as e:
            print(f"反爬解决方案执行失败: {e}")
            return None
    
    async def _simulate_human_behavior(self):
        """模拟人类行为"""
        print("模拟人类行为...")
        
        # 随机延迟
        delay = random.uniform(2, 5)
        print(f"等待 {delay:.1f} 秒...")
        await asyncio.sleep(delay)
        
        # 模拟鼠标移动和滚动
        print("模拟鼠标移动和滚动...")
        await asyncio.sleep(1)
        
    async def _enhanced_crawl(self, url: str):
        """增强的爬取方法"""
        try:
            print("执行增强爬取...")
            
            # 使用JavaScript执行来绕过反爬
            js_code = """
            (async function() {
                console.log('开始执行反爬绕过脚本...');
                
                // 等待页面完全加载
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // 模拟真实用户滚动
                for (let i = 0; i < 5; i++) {
                    window.scrollTo(0, document.body.scrollHeight * (i + 1) / 5);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
                // 等待动态内容加载
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 提取文章链接
                const links = [];
                document.querySelectorAll('a[href]').forEach(link => {
                    const href = link.getAttribute('href');
                    if (href && href.match(/^https:\/\/xueqiu\.com\/\d+\/\d+$/)) {
                        links.push(href);
                    }
                });
                
                console.log(`找到 ${links.length} 个文章链接`);
                return links;
            })();
            """
            
            # 执行JavaScript
            result = await self.crawler._crawl_with_crawl4ai(
                url,
                js_code=js_code,
                page_timeout=60000,  # 1分钟超时
                delay_before_return_html=5
            )
            
            return result
            
        except Exception as e:
            print(f"增强爬取失败: {e}")
            return None

async def main():
    """主函数"""
    print("=== 反爬验证解决方案测试 ===")
    
    solution = AntiCrawlSolution()
    
    # 测试URL
    test_url = "https://xueqiu.com/today"
    
    # 执行反爬解决方案
    result = await solution.solve_with_real_browser(test_url)
    
    if result and result.success:
        print(f"✅ 反爬解决方案成功！")
        print(f"数据长度: {len(result.data) if result.data else 0}")
        
        if hasattr(result, 'js_result') and result.js_result:
            print(f"JavaScript执行结果: {result.js_result}")
        else:
            print("⚠️ 没有JavaScript执行结果")
    else:
        print(f"❌ 反爬解决方案失败")
        if result:
            print(f"错误信息: {result.error}")

if __name__ == "__main__":
    asyncio.run(main())
