FROM python:3.10.9

# 设置容器内的工作目录
WORKDIR /app

# 将当前目录下的所有文件复制到容器内工作目录
COPY . /app

# 安装系统依赖，包括Playwright所需的浏览器运行库
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    bc \
    libnspr4 \
    libnss3 \
    libdbus-1-3 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libxkbcommon0 \
    libatspi2.0-0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libasound2 \
    && rm -rf /var/lib/apt/lists/*

# 升级pip
RUN python -m pip install --upgrade pip

# 安装Python依赖
RUN pip3 install -r requirements.txt

# 安装Playwright浏览器
RUN playwright install

# 设置执行权限
RUN chmod +x /app/start_daemon.sh

# 创建生产环境的启动脚本
RUN echo '#!/bin/bash\n\nset -e\n\n# 设置日志文件\nLOG_FILE="/app/container.log"\necho "$(date): 容器启动开始" | tee -a "$LOG_FILE"\n\n# 加载配置文件\nif [ -f /app/online.sh ]; then\n    echo "$(date): 加载配置文件 online.sh" | tee -a "$LOG_FILE"\n    source /app/online.sh\n    echo "$(date): 配置文件加载完成" | tee -a "$LOG_FILE"\nelse\n    echo "$(date): 警告: online.sh 未找到，使用默认配置" | tee -a "$LOG_FILE"\nfi\n\n# 检查必要的文件\nif [ ! -f /app/start_daemon.sh ]; then\n    echo "$(date): 错误: start_daemon.sh 未找到" | tee -a "$LOG_FILE"\n    exit 1\nfi\n\nif [ ! -f /app/daemon_config.json ]; then\n    echo "$(date): 错误: daemon_config.json 未找到" | tee -a "$LOG_FILE"\n    exit 1\nfi\n\n# 设置执行权限\nchmod +x /app/start_daemon.sh\n\n# 启动爬虫守护进程\necho "$(date): 启动爬虫守护进程" | tee -a "$LOG_FILE"\n/app/start_daemon.sh start --config-file=daemon_config.json 2>&1 | tee -a "$LOG_FILE"\n\n# 检查守护进程状态\nsleep 5\necho "$(date): 检查守护进程状态" | tee -a "$LOG_FILE"\nif [ -f /app/crawler_daemon.pid ]; then\n    PID=$(cat /app/crawler_daemon.pid)\n    echo "$(date): 找到PID文件，PID: $PID" | tee -a "$LOG_FILE"\n    if ps -p "$PID" > /dev/null 2>&1; then\n        echo "$(date): 守护进程启动成功，PID: $PID" | tee -a "$LOG_FILE"\n        echo "$(date): 容器将保持运行状态，监控守护进程" | tee -a "$LOG_FILE"\n        # 保持容器运行，监控守护进程\n        while ps -p "$PID" > /dev/null 2>&1; do\n            sleep 30\n            echo "$(date): 守护进程运行中，PID: $PID" | tee -a "$LOG_FILE"\n        done\n        echo "$(date): 守护进程已退出" | tee -a "$LOG_FILE"\n    else\n        echo "$(date): 错误: 守护进程启动失败" | tee -a "$LOG_FILE"\n        exit 1\n    fi\nelse\n    echo "$(date): 错误: 未找到PID文件" | tee -a "$LOG_FILE"\n    echo "$(date): 显示日志文件内容:" | tee -a "$LOG_FILE"\n    if [ -f /app/crawler_daemon.log ]; then\n        tail -50 /app/crawler_daemon.log | tee -a "$LOG_FILE"\n    else\n        echo "$(date): 日志文件不存在" | tee -a "$LOG_FILE"\n    fi\n    exit 1\nfi\n' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# 设置入口点脚本
ENTRYPOINT ["/app/entrypoint.sh"]
