#!/usr/bin/env python3
"""
直接使用crawl4ai测试JavaScript执行
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawl4ai import AsyncWebCrawler

# 创建一个简单的配置类来替代 CrawlerRunConfig
class SimpleCrawlerConfig:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_direct_crawl4ai():
    """直接使用crawl4ai测试"""
    try:
        print("=== 直接使用crawl4ai测试JavaScript执行 ===")
        
        # 测试URL
        test_url = "https://xueqiu.com/today"
        
        print(f"测试URL: {test_url}")
        
        # 创建配置
        config = SimpleCrawlerConfig(
            cache_mode="bypass",
            js_code="return 'Hello from direct crawl4ai!';",
            js_only=True,
            capture_console_messages=True,
            verbose=True,
            page_timeout=30000,
            delay_before_return_html=1
        )
        
        print("配置创建完成，开始测试...")
        
        # 直接使用AsyncWebCrawler
        async with AsyncWebCrawler() as crawler:
            result = await crawler.arun(
                url=test_url,
                crawler_config=config
            )
            
            print(f"\n=== 测试结果 ===")
            print(f"爬取成功: {result.success}")
            print(f"结果类型: {type(result)}")
            print(f"结果属性: {dir(result)}")
            
            # 检查JavaScript结果
            if hasattr(result, 'js_result'):
                print(f"JavaScript结果: {result.js_result}")
            else:
                print("❌ 没有js_result属性")
                
            # 检查其他可能的结果属性
            for attr in ['result', 'data', 'content', 'html']:
                if hasattr(result, attr):
                    value = getattr(result, attr)
                    print(f"{attr}: {type(value)} - {str(value)[:100] if value else 'None'}")
                    
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_direct_crawl4ai())
