"""
性能配置模块
用于管理aicrawl的性能参数和优化设置
"""

import logging
from dataclasses import dataclass
from typing import Optional

logger = logging.getLogger(__name__)

@dataclass
class PerformanceConfig:
    """性能配置类"""
    
    # 并发控制
    max_concurrent_requests: int = 3   # 最大并发请求数
    fast_mode_concurrent: int = 3      # 快速模式并发数 - 当fast_mode=true时的主要并发控制参数
    normal_mode_concurrent: int = 2    # 普通模式并发数
    max_aggressive_concurrent: int = 5  # 激进模式最大并发数限制
    
    # 批次处理
    batch_size: int = 50              # 批次大小
    batch_delay: float = 2.0          # 批次间延迟（秒）
    
    # 超时设置
    page_timeout_fast: int = 10000    # 快速模式页面超时（毫秒）
    page_timeout_normal: int = 15000  # 普通模式页面超时（毫秒）
    page_timeout_related: int = 8000  # 相关链接提取超时（毫秒）
    
    # 延迟设置
    request_delay: float = 1.0        # 请求间延迟（秒）
    fast_delay: float = 0.5           # 快速模式延迟（秒）
    
    # 内存管理
    gc_frequency: int = 2             # 垃圾回收频率（每N层执行一次）
    max_articles_per_batch: int = 50  # 每批次最大文章数
    
    # 重试设置
    max_retries: int = 3              # 最大重试次数
    retry_delay: float = 1.0          # 重试延迟（秒）
    
    # 日志级别
    enable_performance_logging: bool = True  # 启用性能日志
    enable_debug_logging: bool = False       # 启用调试日志

# 预定义的性能配置模式
class PerformanceModes:
    """预定义的性能模式"""
    
    @staticmethod
    def conservative() -> PerformanceConfig:
        """保守模式 - 最低资源消耗"""
        return PerformanceConfig(
            max_concurrent_requests=2,
            fast_mode_concurrent=3,
            normal_mode_concurrent=2,
            batch_size=10,
            batch_delay=4.0,
            page_timeout_fast=8000,
            page_timeout_normal=12000,
            request_delay=2.5,
            fast_delay=1.5
        )
    
    @staticmethod
    def balanced() -> PerformanceConfig:
        """平衡模式 - 默认配置"""
        # 使用类的默认值，确保与修改后的默认参数一致
        return PerformanceConfig()
    
    @staticmethod
    def aggressive() -> PerformanceConfig:
        """激进模式 - 更高性能但消耗更多资源"""
        return PerformanceConfig(
            max_concurrent_requests=8,
            fast_mode_concurrent=8,
            normal_mode_concurrent=5,
            batch_size=40,
            batch_delay=2.0,
            page_timeout_fast=12000,
            page_timeout_normal=18000,
            request_delay=1.0,
            fast_delay=0.5
        )

# 全局配置实例
_performance_config: Optional[PerformanceConfig] = None

def get_performance_config() -> PerformanceConfig:
    """获取当前性能配置"""
    global _performance_config
    if _performance_config is None:
        _performance_config = PerformanceModes.balanced()
    return _performance_config

def set_performance_config(config: PerformanceConfig):
    """设置性能配置"""
    global _performance_config
    _performance_config = config
    logger.info(f"性能配置已更新: 并发={config.max_concurrent_requests}, 批次={config.batch_size}")

def set_performance_mode(mode: str):
    """设置性能模式"""
    mode_map = {
        'conservative': PerformanceModes.conservative(),
        'balanced': PerformanceModes.balanced(),
        'aggressive': PerformanceModes.aggressive()
    }
    
    if mode not in mode_map:
        raise ValueError(f"未知的性能模式: {mode}. 可用模式: {list(mode_map.keys())}")
    
    set_performance_config(mode_map[mode])
    logger.info(f"性能模式已设置为: {mode}")

def optimize_for_system_resources(cpu_cores: int = None, memory_gb: int = None):
    """根据系统资源自动优化配置"""
    import psutil
    
    if cpu_cores is None:
        cpu_cores = psutil.cpu_count()
    
    if memory_gb is None:
        memory_gb = psutil.virtual_memory().total / (1024**3)
    
    # 根据CPU核心数调整并发
    if cpu_cores <= 2:
        mode = 'conservative'
    elif cpu_cores <= 4:
        mode = 'balanced'
    else:
        mode = 'aggressive'
    
    # 根据内存调整批次大小
    config = PerformanceModes.__dict__[mode]()
    
    if memory_gb < 4:
        config.batch_size = min(config.batch_size, 10)
        config.max_concurrent_requests = min(config.max_concurrent_requests, 3)
    elif memory_gb < 8:
        config.batch_size = min(config.batch_size, 20)
        config.max_concurrent_requests = min(config.max_concurrent_requests, 5)
    
    set_performance_config(config)
    logger.info(f"根据系统资源自动优化: CPU={cpu_cores}核, 内存={memory_gb:.1f}GB, 模式={mode}")

# 性能监控装饰器
def monitor_performance(func):
    """性能监控装饰器"""
    import functools
    import time
    
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        config = get_performance_config()
        if not config.enable_performance_logging:
            return await func(*args, **kwargs)
        
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            end_time = time.time()
            logger.info(f"[性能] {func.__name__} 执行时间: {end_time - start_time:.2f}秒")
            return result
        except Exception as e:
            end_time = time.time()
            logger.error(f"[性能] {func.__name__} 执行失败，耗时: {end_time - start_time:.2f}秒, 错误: {e}")
            raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        config = get_performance_config()
        if not config.enable_performance_logging:
            return func(*args, **kwargs)
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            logger.info(f"[性能] {func.__name__} 执行时间: {end_time - start_time:.2f}秒")
            return result
        except Exception as e:
            end_time = time.time()
            logger.error(f"[性能] {func.__name__} 执行失败，耗时: {end_time - start_time:.2f}秒, 错误: {e}")
            raise
    
    # 检查是否是异步函数
    import asyncio
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper
