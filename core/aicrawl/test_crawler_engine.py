#!/usr/bin/env python3
"""
测试爬虫引擎的简单脚本
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)

from crawler_engine import XueqiuCrawler

async def test_crawler_engine():
    """测试爬虫引擎"""
    try:
        print("开始测试爬虫引擎...")
        
        # 创建爬虫实例
        crawler = XueqiuCrawler(enable_anti_detection=True)
        
        # 测试URL
        test_url = "https://xueqiu.com/today"
        
        print(f"测试URL: {test_url}")
        
        # 执行爬取
        result = await crawler.crawl_url(test_url)
        
        print(f"爬取结果: success={result.success}")
        if result.success:
            print(f"数据长度: {len(result.data) if result.data else 0}")
            if result.data:
                print(f"数据预览: {result.data[:500]}...")
            else:
                print("⚠️ 爬取成功但没有数据内容")
                
            # 检查原始HTML
            if hasattr(result, 'raw_html') and result.raw_html:
                print(f"原始HTML长度: {len(result.raw_html)}")
                print(f"HTML预览: {result.raw_html[:500]}...")
            else:
                print("⚠️ 没有原始HTML数据")
                
            # 检查JavaScript结果
            if hasattr(result, 'js_result') and result.js_result:
                print(f"JavaScript结果: {result.js_result}")
            else:
                print("⚠️ 没有JavaScript执行结果")
        else:
            print(f"错误信息: {result.error}")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_crawler_engine())
