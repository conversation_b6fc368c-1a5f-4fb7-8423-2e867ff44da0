#!/usr/bin/env python3
"""
测试禁用JavaScript的静态内容获取
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawler_engine import XueqiuCrawler

async def test_static_content():
    """测试静态内容获取"""
    try:
        print("开始测试静态内容获取...")
        
        # 创建爬虫实例，禁用JavaScript
        crawler = XueqiuCrawler(enable_anti_detection=False)  # 禁用反检测
        
        # 测试URL
        test_url = "https://xueqiu.com/today"
        
        print(f"测试URL: {test_url}")
        
        # 执行爬取
        result = await crawler.crawl_url(test_url)
        
        if result.success and result.raw_html:
            html_content = result.raw_html
            
            print(f"静态HTML内容长度: {len(html_content)}")
            
            # 检查页面标题
            if "<title>" in html_content:
                title_start = html_content.find("<title>") + 7
                title_end = html_content.find("</title>")
                if title_end > title_start:
                    title = html_content[title_start:title_end]
                    print(f"页面标题: {title}")
            
            # 检查页面主体内容
            if "<body" in html_content:
                body_start = html_content.find("<body")
                body_start = html_content.find(">", body_start) + 1
                body_end = html_content.find("</body>")
                if body_end > body_start:
                    body_content = html_content[body_start:body_end]
                    # 提取文本内容
                    import re
                    text_content = re.sub(r'<[^>]+>', '', body_content)
                    text_content = re.sub(r'\s+', ' ', text_content).strip()
                    print(f"静态页面文本内容预览: {text_content[:500]}...")
                    
                    # 检查是否包含文章链接
                    article_links = re.findall(r'https://xueqiu\.com/\d+/\d+', body_content)
                    print(f"发现 {len(article_links)} 个文章链接")
                    if article_links:
                        print(f"前3个链接: {article_links[:3]}")
            else:
                print("未找到body标签")
                
        else:
            print(f"爬取失败: {result.error}")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_static_content())
