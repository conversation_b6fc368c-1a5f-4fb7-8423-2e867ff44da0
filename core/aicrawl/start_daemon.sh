#!/bin/bash
# AI爬虫守护进程管理脚本
# 提供启动、停止、重启等功能

# 脚本配置
SCRIPT_NAME="$(basename "$0")"
# 获取脚本所在的绝对路径
script_dir="$(cd "$(dirname "$0")" && pwd)"
# 使用绝对路径设置文件位置
PYTHON_SCRIPT="$script_dir/crawler_daemon.py"
PID_FILE="$script_dir/crawler_daemon.pid"
LOG_FILE="$script_dir/crawler_daemon.log"

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo "错误: 未找到python3，请先安装Python 3"
        exit 1
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$(python3 -c "import sys; print('.'.join(map(str, sys.version_info[:2])))")
    # 正确比较语义化版本号
    IFS='.' read -r major minor <<< "$PYTHON_VERSION"
    if (( major < 3 || (major == 3 && minor < 8) )); then
        echo "警告: Python版本 $PYTHON_VERSION 低于推荐的3.8版本，可能会有兼容性问题"
    fi
}

# 检查运行环境
check_env() {
    if [ ! -f "$PYTHON_SCRIPT" ]; then
        echo "错误: 找不到 $PYTHON_SCRIPT 文件，请在正确的目录下运行此脚本"
        exit 1
    fi
    
    # 检查是否有运行权限
    if [ ! -x "$PYTHON_SCRIPT" ]; then
        chmod +x "$PYTHON_SCRIPT" || {
            echo "错误: 无法设置 $PYTHON_SCRIPT 为可执行文件"
            exit 1
        }
    fi
}

# 启动守护进程
start_daemon() {
    # 检查是否已经在运行
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" &> /dev/null; then
            echo "守护进程已经在运行中 (PID: $PID)"
            return 1
        else
            echo "发现残留的PID文件，删除它..."
            rm -f "$PID_FILE"
        fi
    fi
    
    # 检查是否有日志目录
    if [ ! -f "$LOG_FILE" ]; then
        touch "$LOG_FILE" || {
            echo "警告: 无法创建日志文件 $LOG_FILE"
        }
    fi
    
    echo "正在启动AI爬虫守护进程..."
    
    # 处理配置文件参数
    local config_arg=""
    for arg in "$@"; do
        if [[ "$arg" == --config-file=* ]]; then
            config_file="${arg#*=}"
            # 如果是相对路径，相对于script_dir（aicrawl目录）解析
            if [[ "$config_file" != /* ]]; then
                config_file="$script_dir/$config_file"
            fi
            if [ -f "$config_file" ]; then
                echo "使用配置文件: $config_file"
            else
                echo "警告: 配置文件不存在: $config_file"
            fi
        fi
    done
    
    # 启动守护进程，将输出重定向到日志文件
    python3 "$PYTHON_SCRIPT" "$@" >> "$LOG_FILE" 2>&1 &
    
    # 等待PID文件生成
    for i in {1..5}; do
        if [ -f "$PID_FILE" ]; then
            PID=$(cat "$PID_FILE")
            echo "守护进程启动成功 (PID: $PID)"
            return 0
        fi
        sleep 1
    done
    
    echo "错误: 守护进程启动失败，无法找到PID文件"
    return 1
}

# 停止守护进程
stop_daemon() {
    if [ ! -f "$PID_FILE" ]; then
        echo "守护进程未运行（未找到PID文件）"
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    
    if ps -p "$PID" &> /dev/null; then
        echo "正在停止守护进程 (PID: $PID)..."
        
        # 尝试优雅停止
        kill -TERM "$PID"
        
        # 等待进程终止
        for i in {1..10}; do
            if ! ps -p "$PID" &> /dev/null; then
                echo "守护进程已成功停止"
                rm -f "$PID_FILE"
                return 0
            fi
            sleep 1
        done
        
        # 如果优雅停止失败，强制终止
        echo "警告: 守护进程未响应优雅停止信号，尝试强制终止..."
        kill -KILL "$PID"
        
        # 再次等待
        sleep 2
        
        if ! ps -p "$PID" &> /dev/null; then
            echo "守护进程已强制停止"
            rm -f "$PID_FILE"
            return 0
        else
            echo "错误: 无法停止守护进程 (PID: $PID)"
            return 1
        fi
    else
        echo "警告: PID文件存在，但进程未运行，删除PID文件"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 查看守护进程状态
status_daemon() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" &> /dev/null; then
            echo "守护进程正在运行 (PID: $PID)"
            return 0
        else
            echo "守护进程未运行，但PID文件存在"
            return 1
        fi
    else
        echo "守护进程未运行"
        return 1
    fi
}

# 查看守护进程日志
tail_log() {
    if [ ! -f "$LOG_FILE" ]; then
        echo "日志文件不存在: $LOG_FILE"
        return 1
    fi
    
    if [ "$1" = "-f" ]; then
        echo "正在跟踪日志文件（按Ctrl+C退出）..."
        tail -f "$LOG_FILE"
    else
        echo "最近的日志内容："
        tail -n 50 "$LOG_FILE"
    fi
}

# 显示帮助信息
show_help() {
    echo "使用方法: $SCRIPT_NAME [命令] [参数]"
    echo "命令:"
    echo "  start       启动守护进程"
    echo "  stop        停止守护进程"
    echo "  restart     重启守护进程"
    echo "  status      查看守护进程状态"
    echo "  log         查看守护进程日志"
    echo "  log -f      跟踪守护进程日志"
    echo "  help        显示帮助信息"
    echo ""
    echo "启动参数示例:"
    echo "  $SCRIPT_NAME start --schedule-type=interval --interval-minutes=60"
    echo "  $SCRIPT_NAME start --deep-crawl --max-articles=2000"
    echo "  $SCRIPT_NAME start --no-database --no-full-content"
}

# 主函数
main() {
    # 检查Python环境和脚本环境
    check_python
    check_env
    
    # 处理命令行参数
    case "$1" in
        start)
            shift
            start_daemon "$@"
            ;;
        stop)
            stop_daemon
            ;;
        restart)
            shift
            stop_daemon
            start_daemon "$@"
            ;;
        status)
            status_daemon
            ;;
        log)
            shift
            tail_log "$@"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"