#!/usr/bin/env python3
"""
测试增强后的爬虫功能
验证反检测和链接提取的改进
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main_advanced import PopularArticlesCrawler

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_enhanced_crawler():
    """测试增强后的爬虫功能"""
    logger.info("=== 开始测试增强后的爬虫功能 ===")
    
    try:
        # 创建爬虫实例
        crawler = PopularArticlesCrawler(
            enable_anti_detection=True,
            save_to_database=False,  # 测试时不保存到数据库
            enable_html_content=True
        )
        
        logger.info("爬虫实例创建成功")
        
        # 测试单个页面爬取
        test_url = "https://xueqiu.com/today"
        logger.info(f"测试页面: {test_url}")
        
        # 使用增强模式爬取
        articles = await crawler.crawl_popular_articles(
            pages=[test_url],
            enhanced_mode=True,
            fetch_full_content=False  # 测试时只获取链接，不获取完整内容
        )
        
        logger.info(f"爬取结果: 获取到 {len(articles)} 篇文章")
        
        if articles:
            logger.info("前3篇文章信息:")
            for i, article in enumerate(articles[:3]):
                logger.info(f"  {i+1}. {article.get('title', '无标题')} - {article.get('url', '无URL')}")
        else:
            logger.warning("未获取到任何文章，可能仍然存在反爬问题")
        
        logger.info("=== 爬虫功能测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_enhanced_crawler())
