#!/usr/bin/env python3
"""
AI爬虫守护进程
负责定时启动爬虫任务，监控执行状态，并提供进程管理功能
"""
import os
import sys
import time
import json
import logging
import argparse
import asyncio
import signal
from datetime import datetime
from typing import Dict, List, Any, Optional

# 获取当前脚本所在的目录（aicrawl目录）
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(SCRIPT_DIR, 'crawler_daemon.log'), mode='w'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('crawler_daemon')

# 导入项目模块
sys.path.append(SCRIPT_DIR)

# 导入已有的爬虫和监控模块
try:
    from main_advanced import PopularArticlesCrawler
    from slider_verification import SliderVerificationHandler
    # 尝试从monitoring模块导入CrawlerMonitor
    try:
        from monitoring import CrawlerMonitor
    except ImportError:
        # 如果monitoring模块不可用，创建一个简单的监控类作为替代品
        class SimpleCrawlerMonitor:
            def __init__(self):
                self.metrics = {'initialized': True}
            def record_task_start(self, task_id, task_type):
                return {'task_id': task_id, 'task_type': task_type, 'start_time': time.time()}
            def record_task_completion(self, task, result):
                pass
            def get_metrics(self):
                return self.metrics
        # 将简单监控类设为CrawlerMonitor
        CrawlerMonitor = SimpleCrawlerMonitor
    import schedule
    MODULES_LOADED = True
except ImportError as e:
    logger.error(f"导入项目模块失败: {e}")
    MODULES_LOADED = False

class DaemonManager:
    """爬虫守护进程管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.running = False
        self.crawler = None
        self.monitor = None
        self.last_run = None
        # 使用绝对路径设置PID文件
        pid_file = config.get('pid_file', 'crawler_daemon.pid')
        self.pid_file = os.path.join(SCRIPT_DIR, pid_file)
        
    def initialize(self):
        """初始化守护进程"""
        if not MODULES_LOADED:
            logger.error("项目模块导入失败，无法初始化守护进程")
            return False
        
        try:
            # 创建爬虫实例
            self.crawler = PopularArticlesCrawler(
                enable_anti_detection=self.config.get('enable_anti_detection', True),
                save_to_database=self.config.get('save_to_database', True),
                database_url=self.config.get('database_url')
            )
            
            # 创建滑块验证处理器
            self.slider_handler = SliderVerificationHandler()
            
            # 创建监控实例
            self.monitor = CrawlerMonitor()
            
            # 记录PID到文件
            self._write_pid_file()
            
            # 设置信号处理
            self._setup_signals()
            
            logger.info("守护进程初始化成功")
            return True
        except Exception as e:
            logger.error(f"守护进程初始化失败: {e}")
            return False
    
    def start(self):
        """启动守护进程"""
        logger.info("=== AI爬虫守护进程启动 ===" if not self.running else "=== AI爬虫守护进程已在运行中 ===")
        
        if not self.initialize():
            return False
        
        self.running = True
        
        # 设置定时任务
        self._schedule_tasks()
        
        try:
            # 立即执行一次爬取任务
            if self.config.get('run_immediately', True):
                asyncio.run(self._execute_crawl_task())
            
            # 主循环
            logger.info(f"守护进程开始运行，定时任务已设置")
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次任务
                
        except KeyboardInterrupt:
            logger.info("守护进程接收到中断信号，准备退出")
        finally:
            self.stop()
            
        return True
    
    def stop(self):
        """停止守护进程"""
        if not self.running:
            return
        
        logger.info("=== AI爬虫守护进程停止 ===")
        self.running = False
        
        # 清除所有定时任务
        schedule.clear()
        
        # 删除PID文件
        self._remove_pid_file()
        
        logger.info("守护进程已成功停止")
    
    def _schedule_tasks(self):
        """设置定时任务"""
        # 根据配置设置定时任务
        schedule_type = self.config.get('schedule_type', 'daily')
        schedule_time = self.config.get('schedule_time', '08:00')
        
        if schedule_type == 'hourly':
            # 每小时执行一次
            logger.info(f"设置每小时定时任务")
            schedule.every().hour.at(schedule_time.split(':')[1]).do(
                self._run_async_task, self._execute_crawl_task
            )
        elif schedule_type == 'daily':
            # 每天执行一次
            logger.info(f"设置每天定时任务，时间: {schedule_time}")
            schedule.every().day.at(schedule_time).do(
                self._run_async_task, self._execute_crawl_task
            )
        elif schedule_type == 'interval':
            # 每隔指定分钟执行一次
            interval = self.config.get('interval_minutes', 120)
            logger.info(f"设置间隔定时任务，每{interval}分钟执行一次")
            schedule.every(interval).minutes.do(
                self._run_async_task, self._execute_crawl_task
            )
        
        # 添加健康检查任务
        schedule.every().hour.do(self._run_async_task, self._health_check)
    
    async def _execute_crawl_task(self):
        """执行爬取任务"""
        if not self.crawler or not self.running:
            return
        
        logger.info(f"=== 开始执行爬取任务: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")
        
        task_config = self.config.get('task_config', {})
        logger.info(f"执行爬取任务时的task_config: {task_config}")
        
        try:
            # 记录任务开始
            if self.monitor:
                monitor_task = self.monitor.record_task_start(
                    task_id=f"crawl_{int(time.time())}",
                    task_type="popular_articles"
                )
            
            # 处理滑块验证
            valid_cookies = {}
            if self.config.get('enable_slider_verification', True):
                logger.info("开始处理滑块验证...")
                base_url = task_config.get('pages', ['https://xueqiu.com/today'])[0]
                valid_cookies = await self.slider_handler.solve_and_get_valid_cookies(base_url)
                if valid_cookies:
                    logger.info(f"滑块验证成功，获取到有效cookies: {len(valid_cookies)}个")
                else:
                    logger.warning("滑块验证失败，使用默认配置继续爬取")
            
            # 将验证后的cookies传递给爬虫
            if valid_cookies:
                try:
                    # 尝试设置爬虫的cookies属性
                    if hasattr(self.crawler, 'set_cookies'):
                        # 如果有set_cookies方法，调用它
                        self.crawler.set_cookies(valid_cookies)
                    else:
                        # 否则直接设置cookies属性
                        self.crawler.cookies = valid_cookies
                    logger.info("成功将验证后的cookies应用到爬虫")
                except Exception as e:
                    logger.error(f"设置cookies时出错: {e}")
            
            # 执行爬取任务
            if task_config.get('deep_crawl', False):
                # 深度爬取模式
                articles = await self.crawler.deep_crawl_from_today(
                    max_depth=task_config.get('max_depth', 5),
                    max_articles=task_config.get('max_articles', 1000),
                    fetch_full_content=task_config.get('fetch_full_content', True),
                    fast_mode=task_config.get('fast_mode', False),
                    load_more_clicks=task_config.get('load_more_clicks', 10)
                )
            else:
                # 普通爬取模式
                pages = task_config.get('pages', ["https://xueqiu.com/today"])
                articles = await self.crawler.crawl_popular_articles(
                    pages=pages,
                    enhanced_mode=task_config.get('enhanced_mode', True),
                    fetch_full_content=task_config.get('fetch_full_content', True)
                )
            
            # 更新任务状态
            self.last_run = datetime.now()
            
            # 记录任务完成
            if self.monitor:
                result = {
                    'success': True,
                    'total_articles': len(articles),
                    'data_type': 'popular_articles'
                }
                # 这里需要模拟一个任务对象用于监控
                class DummyTask:
                    def __init__(self, monitor=None, monitor_task=None):
                        self.task_id = f"crawl_{int(time.time())}"
                        self.task_type = "popular_articles"
                        self.scheduled_time = monitor_task.start_time if monitor_task else datetime.now()
                        self.last_error = None
                
                dummy_task = DummyTask(monitor=self.monitor, monitor_task=monitor_task)
                self.monitor.record_task_completion(dummy_task, result)
            
            logger.info(f"=== 爬取任务执行完成: {len(articles)} 篇文章, 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")
            
        except Exception as e:
            logger.error(f"爬取任务执行失败: {e}")
            
            # 记录错误
            if self.monitor:
                result = {
                    'success': False,
                    'error': str(e)
                }
                
                class DummyTask:
                    def __init__(self, monitor=None, monitor_task=None):
                        self.task_id = f"crawl_{int(time.time())}"
                        self.task_type = "popular_articles"
                        self.scheduled_time = monitor_task.start_time if monitor_task else datetime.now()
                        self.last_error = str(e)
                
                dummy_task = DummyTask(monitor=self.monitor, monitor_task=monitor_task)
                self.monitor.record_task_completion(dummy_task, result)
    
    async def _health_check(self):
        """执行健康检查"""
        if not self.running:
            return
        
        logger.info(f"执行系统健康检查: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 检查爬虫状态
        if not self.crawler:
            logger.warning("爬虫实例未初始化")
            return
        
        # 输出监控指标
        if self.monitor:
            metrics = self.monitor.metrics
            logger.info(f"系统性能指标: 总任务数={metrics.total_tasks}, 成功率={metrics.success_rate:.2%}, 平均响应时间={metrics.average_response_time:.2f}s")
        
        # 检查上一次运行时间
        if self.last_run:
            time_since_last_run = (datetime.now() - self.last_run).total_seconds() / 3600
            logger.info(f"上次运行时间: {self.last_run.strftime('%Y-%m-%d %H:%M:%S')} ({time_since_last_run:.2f}小时前)")
    
    def _write_pid_file(self):
        """写入PID文件"""
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(os.getpid()))
            logger.info(f"PID文件已创建: {self.pid_file}")
        except Exception as e:
            logger.error(f"创建PID文件失败: {e}")
    
    def _remove_pid_file(self):
        """删除PID文件"""
        try:
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
                logger.info(f"PID文件已删除: {self.pid_file}")
        except Exception as e:
            logger.error(f"删除PID文件失败: {e}")
    
    def _setup_signals(self):
        """设置信号处理"""
        def signal_handler(sig, frame):
            logger.info(f"接收到信号 {sig}，准备退出")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def _run_async_task(self, coroutine_func):
        """运行异步任务的包装函数"""
        if not self.running:
            return
            
        try:
            loop = asyncio.get_event_loop()
            loop.run_until_complete(coroutine_func())
        except RuntimeError:
            # 如果当前线程没有事件循环，创建一个新的
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(coroutine_func())
            finally:
                # 确保在任何情况下都能干净地关闭事件循环
                try:
                    # 获取所有任务（不包括当前正在运行的任务）
                    tasks = [t for t in asyncio.all_tasks(loop) if t is not asyncio.current_task(loop)]
                    if tasks:
                        # 取消所有未完成的任务
                        for task in tasks:
                            task.cancel()
                        # 等待任务取消完成
                        loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))
                    # 关闭事件循环
                    loop.close()
                except Exception as cleanup_error:
                    logger.warning(f"清理事件循环时出错: {cleanup_error}")
        except Exception as e:
            logger.error(f"运行异步任务失败: {e}")

class TaskConfigBuilder:
    """任务配置构建器"""
    
    @staticmethod
    def load_from_file(config_file: str) -> Dict[str, Any]:
        """从配置文件加载配置"""
        if not os.path.exists(config_file):
            logger.warning(f"配置文件不存在: {config_file}")
            return {}
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                logger.info(f"成功从配置文件加载配置: {config_file}")
                return config
        except json.JSONDecodeError as e:
            logger.error(f"配置文件格式错误: {e}")
            return {}
        except Exception as e:
            logger.error(f"读取配置文件失败: {e}")
            return {}
    
    @staticmethod
    def merge_configs(base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
        """合并两个配置字典"""
        result = base_config.copy()
        
        for key, value in override_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                # 递归合并嵌套字典
                result[key] = TaskConfigBuilder.merge_configs(result[key], value)
            else:
                # 直接覆盖值
                result[key] = value
        
        return result
    
    @staticmethod
    def build_from_args_and_file(args: argparse.Namespace) -> Dict[str, Any]:
        """从命令行参数和配置文件构建配置"""
        # 首先从配置文件加载配置
        config = {}
        logger.info(f"开始构建配置: args={args}")
        if args.config_file:
            # 如果是相对路径，相对于SCRIPT_DIR（aicrawl目录）解析
            if not os.path.isabs(args.config_file):
                config_file_path = os.path.join(SCRIPT_DIR, args.config_file)
            else:
                config_file_path = args.config_file
            file_config = TaskConfigBuilder.load_from_file(config_file_path)
            
            # 将schedule部分展开到顶层
            if 'schedule' in file_config:
                schedule_config = file_config.pop('schedule')
                if 'type' in schedule_config:
                    config['schedule_type'] = schedule_config['type']
                if 'time' in schedule_config:
                    config['schedule_time'] = schedule_config['time']
                if 'interval_minutes' in schedule_config:
                    config['interval_minutes'] = schedule_config['interval_minutes']
            
            # 合并其他配置
            config = TaskConfigBuilder.merge_configs(config, file_config)
        
        # 然后从命令行参数构建配置，但只为那些用户明确提供的参数设置值
        # 对于布尔标志参数，我们需要检查用户是否明确提供了这些参数
        args_config = {
            'pid_file': args.pid_file,
            'enable_anti_detection': not args.disable_anti_detection,
            'save_to_database': not args.no_database,
            'run_immediately': not args.no_immediate_run,
            'schedule_type': args.schedule_type,
            'schedule_time': args.schedule_time,
            'interval_minutes': args.interval_minutes,
            'task_config': {}
        }
        
        # 只有当用户显式提供了数据库URL时才设置
        if args.database_url:
            args_config['database_url'] = args.database_url
        
        # 对于task_config中的参数，只包含用户显式提供的或需要覆盖的参数
        # 对于deep_crawl和fast_mode，只有当用户明确使用了对应的命令行参数时才设置
        if hasattr(args, 'deep_crawl') and args.deep_crawl:
            args_config['task_config']['deep_crawl'] = True
        if hasattr(args, 'max_depth'):
            args_config['task_config']['max_depth'] = args.max_depth
        if hasattr(args, 'max_articles'):
            args_config['task_config']['max_articles'] = args.max_articles
        if hasattr(args, 'no_full_content'):
            args_config['task_config']['fetch_full_content'] = not args.no_full_content
        if hasattr(args, 'fast_mode') and args.fast_mode:
            args_config['task_config']['fast_mode'] = True
        if hasattr(args, 'load_more_clicks'):
            args_config['task_config']['load_more_clicks'] = args.load_more_clicks
        
        # 如果指定了页面，添加到任务配置
        if args.pages:
            args_config['task_config']['pages'] = args.pages.split(',')
        
        # 合并配置，确保配置文件的设置优先
        # 将参数顺序调换，让config（配置文件）覆盖args_config（命令行参数）
        final_config = TaskConfigBuilder.merge_configs(args_config, config)
        
        logger.info(f"配置文件加载的配置: {config}")
        logger.info(f"命令行参数构建的配置: {args_config}")
        logger.info(f"最终合并的配置: {final_config}")
        
        return final_config
        
    @staticmethod
    def build_from_args(args: argparse.Namespace) -> Dict[str, Any]:
        """从命令行参数构建配置"""
        config = {
            'pid_file': args.pid_file,
            'enable_anti_detection': not args.disable_anti_detection,
            'save_to_database': not args.no_database,
            'database_url': args.database_url,
            'run_immediately': not args.no_immediate_run,
            'schedule_type': args.schedule_type,
            'schedule_time': args.schedule_time,
            'interval_minutes': args.interval_minutes,
            'task_config': {}
        }
        
        # 只添加有明确命令行参数的任务配置
        if hasattr(args, 'deep_crawl'):
            config['task_config']['deep_crawl'] = args.deep_crawl
        if hasattr(args, 'max_depth'):
            config['task_config']['max_depth'] = args.max_depth
        if hasattr(args, 'max_articles'):
            config['task_config']['max_articles'] = args.max_articles
        if hasattr(args, 'no_full_content'):
            config['task_config']['fetch_full_content'] = not args.no_full_content
        if hasattr(args, 'fast_mode'):
            config['task_config']['fast_mode'] = args.fast_mode
        if hasattr(args, 'load_more_clicks'):
            config['task_config']['load_more_clicks'] = args.load_more_clicks
        
        # 如果指定了页面，添加到任务配置
        if args.pages:
            config['task_config']['pages'] = args.pages.split(',')
        
        return config

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='AI爬虫守护进程')
    
    # 配置文件参数
    parser.add_argument('--config-file', type=str, help='配置文件路径')
    
    # 基本配置
    parser.add_argument('--pid-file', type=str, default='crawler_daemon.pid',
                        help='PID文件路径 (默认: crawler_daemon.pid)')
    parser.add_argument('--disable-anti-detection', action='store_true',
                        help='禁用反检测功能')
    parser.add_argument('--no-database', action='store_true',
                        help='不保存到数据库')
    parser.add_argument('--database-url', type=str, help='自定义数据库连接URL')
    parser.add_argument('--no-immediate-run', action='store_true',
                        help='不立即运行爬取任务，仅等待定时任务触发')
    
    # 调度配置
    parser.add_argument('--schedule-type', type=str, choices=['daily', 'hourly', 'interval'],
                        default='daily', help='调度类型 (默认: daily)')
    parser.add_argument('--schedule-time', type=str, default='08:00',
                        help='定时执行时间 (格式: HH:MM, 默认: 08:00)')
    parser.add_argument('--interval-minutes', type=int, default=120,
                        help='间隔执行的分钟数 (默认: 120)')
    
    # 任务配置
    parser.add_argument('--pages', type=str, help='要爬取的页面URL，多个URL用逗号分隔')
    parser.add_argument('--deep-crawl', action='store_true',
                        help='启用深度爬取模式')
    parser.add_argument('--max-depth', type=int, default=5,
                        help='深度爬取的最大深度 (默认: 5)')
    parser.add_argument('--max-articles', type=int, default=1000,
                        help='深度爬取的最大文章数 (默认: 1000)')
    parser.add_argument('--no-full-content', action='store_true',
                        help='不获取文章完整内容')
    parser.add_argument('--fast-mode', action='store_true',
                        help='启用快速模式（更高并发）')
    parser.add_argument('--load-more-clicks', type=int, default=10,
                        help='加载更多按钮的点击次数 (默认: 10)')
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()
    
    # 构建配置（从命令行参数和配置文件）
    config = TaskConfigBuilder.build_from_args_and_file(args)
    
    # 创建守护进程管理器
    daemon = DaemonManager(config)
    
    # 启动守护进程
    success = daemon.start()
    
    # 注意：不要在这里调用sys.exit()，因为daemon.start()方法已经包含了主循环
    # 这里返回success状态，但不主动退出进程
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    # 注意：这里不调用sys.exit()，因为daemon.start()方法已经包含了主循环
    # 当main()返回时，意味着daemon.start()已经执行完毕
    # 这可能是由于异常、信号处理或守护进程被显式停止导致的
    pass