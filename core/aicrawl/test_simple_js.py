#!/usr/bin/env python3
"""
测试简单的JavaScript执行
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawler_engine import XueqiuCrawler

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_simple_js():
    """测试简单的JavaScript执行"""
    try:
        print("=== 测试简单JavaScript执行 ===")
        
        # 创建爬虫实例
        crawler = XueqiuCrawler(enable_anti_detection=True)
        
        # 测试URL
        test_url = "https://xueqiu.com/today"
        
        print(f"测试URL: {test_url}")
        
        # 使用简单的JavaScript代码
        simple_js = """
        (function() {
            console.log('Hello from JavaScript!');
            
            // 提取所有链接
            const links = [];
            document.querySelectorAll('a[href]').forEach(link => {
                const href = link.getAttribute('href');
                if (href && href.match(/^https:\/\/xueqiu\.com\/\\d+\/\\d+$/)) {
                    links.push(href);
                }
            });
            
            console.log('Found links:', links.length);
            return links;
        })();
        """
        
        print("开始执行简单JavaScript...")
        
        # 执行爬取
        result = await crawler._crawl_with_crawl4ai(
            test_url,
            js_code=simple_js,
            page_timeout=60000,
            delay_before_return_html=2
        )
        
        print(f"\n=== 测试结果 ===")
        print(f"爬取成功: {result.success}")
        print(f"数据长度: {len(result.data) if result.data else 0}")
        
        if hasattr(result, 'js_result'):
            print(f"JavaScript结果: {result.js_result}")
            if result.js_result:
                print(f"JavaScript结果类型: {type(result.js_result)}")
                print(f"JavaScript结果长度: {len(result.js_result) if hasattr(result.js_result, '__len__') else 'N/A'}")
            else:
                print("⚠️ JavaScript结果为None或空")
        else:
            print("❌ 没有js_result属性")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple_js())
