FROM unclecode/crawl4ai:all

# 设置容器内的工作目录
WORKDIR /app

# 将当前目录下的所有文件复制到容器内工作目录
COPY . /app

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    bc \
    && rm -rf /var/lib/apt/lists/*

# 升级pip
RUN python -m pip install --upgrade pip

# 安装Python依赖
RUN pip3 install -r requirements.txt

# 设置执行权限
RUN chmod +x /app/start_daemon.sh

# 创建测试脚本
RUN echo '#!/bin/bash\n\necho "容器启动开始"\necho "测试Python导入..."\npython3 -c "from main_advanced import PopularArticlesCrawler; print(\"导入成功！\")" 2>&1\necho "测试守护进程启动..."\ncd /app\n/app/start_daemon.sh start --config-file=daemon_config.json 2>&1\necho "守护进程启动测试完成"\n' > /app/test_import.sh && chmod +x /app/test_import.sh

# 设置入口点脚本
ENTRYPOINT ["/app/test_import.sh"]
