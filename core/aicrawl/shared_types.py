#!/usr/bin/env python3
"""
共享数据类型模块
用于存放不同模块之间共享的数据类定义，避免循环导入
"""
from dataclasses import dataclass
from typing import Dict, List, Optional, Any

@dataclass
class RequestResult:
    """请求结果数据类"""
    success: bool
    data: Optional[str] = None
    error: Optional[str] = None
    status_code: Optional[int] = None
    response_time: Optional[float] = None
    js_result: Optional[Any] = None  # JavaScript执行结果

@dataclass
class CrawlResult:
    """爬取结果数据类"""
    success: bool
    url: str
    raw_html: Optional[str] = None
    parsed_data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    response_time: Optional[float] = None
    data_type: Optional[str] = None