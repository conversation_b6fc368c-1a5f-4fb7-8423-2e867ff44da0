#!/usr/bin/env python3
"""
滑块验证功能测试脚本
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from slider_verification import SliderVerificationHandler
from config import AntiDetectionConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_slider_verification():
    """测试滑块验证功能"""
    try:
        # 创建滑块验证处理器
        handler = SliderVerificationHandler()
        
        # 测试URL
        test_url = "https://xueqiu.com/today"
        
        logger.info(f"开始测试滑块验证功能: {test_url}")
        
        # 检查是否需要验证
        is_required = handler.is_verification_required("", test_url)
        logger.info(f"是否需要滑块验证: {is_required}")
        
        # 尝试解决滑块验证
        logger.info("开始处理滑块验证...")
        cookies = await handler.solve_and_get_valid_cookies(test_url)
        
        if cookies:
            logger.info(f"滑块验证成功，获得 {len(cookies)} 个cookies")
            for name, value in list(cookies.items())[:5]:  # 只显示前5个
                logger.info(f"  {name}: {value[:50]}...")
        else:
            logger.warning("滑块验证失败，未获得cookies")
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

async def test_crawler_with_slider():
    """测试带滑块验证的爬虫功能"""
    try:
        from crawler_engine import XueqiuCrawler
        
        logger.info("开始测试带滑块验证的爬虫功能...")
        
        # 创建爬虫实例
        crawler = XueqiuCrawler(enable_anti_detection=True)
        
        # 测试URL
        test_url = "https://xueqiu.com/today"
        
        # 尝试爬取
        result = await crawler.crawl_url(test_url)
        
        if result.success:
            logger.info(f"爬取成功，数据长度: {len(result.data) if result.data else 0}")
        else:
            logger.warning(f"爬取失败: {result.error}")
            
    except Exception as e:
        logger.error(f"爬虫测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    logger.info("=== 滑块验证功能测试 ===")
    
    # 测试1: 滑块验证功能
    logger.info("\n1. 测试滑块验证功能")
    await test_slider_verification()
    
    # 测试2: 带滑块验证的爬虫
    logger.info("\n2. 测试带滑块验证的爬虫功能")
    await test_crawler_with_slider()
    
    logger.info("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(main())
