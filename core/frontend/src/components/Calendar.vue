<template>
    <div class="b-calendar">
        <a-card size="small">
            <a-row :gutter="32">
                <a-col :span="10">
                <a-alert class="mb16" message="日历以及事件新闻跟踪。" banner />
                <a-calendar :fullscreen="false" @change="onDateChange" @panelChange="onDateChange">
                    <div 
                        class="b-calendar-cell" 
                        :class="{'b-calendar-cell-active': getCalendar(value).mark, 'b-calendar-cell-danger': getCalendar(value).danger}"
                        slot="dateFullCellRender" 
                        slot-scope="value">
                        <div class="ant-fullcalendar-date" @contextmenu.prevent.stop="handleClick($event, value)">
                            <div class="ant-fullcalendar-value">
                                {{value.get('date')}}</div>
                            <div class="b-calendar-cell-content">
                                <span class="xiu" v-if="!(getDaliyData(value) && getDaliyData(value).isOpen)">休</span>
                                <span class="shi" v-if="getListData(value) && getListData(value).length > 0">事</span>
                            </div>
                        </div>
                    </div>
                </a-calendar>
                </a-col>
                <a-col :span="14">
                    <div class="b-calendar-notification" v-if="currentData && currentData.length > 0 || currentPostList || (Object.keys(reporttime) && Object.keys(reporttime).length > 0)">
                        <div v-if="importantList && importantList.length > 0">
                            <a-divider orientation="left">
                                48H新闻解读
                            </a-divider>
                            <div class="section-gray">
                                <div v-for="im in importantList" :key="im.newsID" class="mb16">
                                    <div class="mb4">{{im.time}} {{im.content}}</div>
                                    <!-- <div class="color-primary">{{im.mark}}</div> -->
                                    <a-alert banner :show-icon="false" v-if="im.mark" :message="im.mark" type="info" />
                                    <div class="mt8" v-if="im.newsStocks && im.newsStocks.length > 0">
                                        <StockLabel :stocks="im.newsStocks" :show="true" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="b-calendar-content" v-if="currentData && currentData.length > 0">
                            <a-divider orientation="left">
                                重要事项
                            </a-divider>
                            <div v-for="(item, index) in currentData" :key="index">
                                <div v-if="item.event">
                                    <div class="mt8">
                                        <a-badge :status="item.event.type" :text="item.event.content" />
                                        <div class="fs12 pl14">{{item.event.description}}</div>
                                    </div>
                                    <div class="mt8" v-if="item.event && ((item.event.eventTags && item.event.eventTags.length > 0) || (item.event.eventStocks && item.event.eventStocks.length > 0))">
                                        <TagLabel :tags="item.event.eventTags" :show="true" />
                                        <StockLabel :stocks="item.event.eventStocks" :show="true" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="currentPostList && currentPostList.length > 0">
                            <a-divider orientation="left">
                                每日记录
                            </a-divider>
                            <div v-for="post in currentPostList" :key="post && post.id ? post.id : 'post-' + index" class="previewable-image">
                                {{post.createdAt | moment("HH:mm")}}
                                <div v-html="marked.marked(post.content, {breaks: true})" />
                            </div>
                        </div>
                        <!-- 写死 -->
                        <div v-if="reporttime">
                            <div v-if="reporttime['1'] && reporttime['1'].length > 0">
                                <a-divider orientation="left">
                                一季报披露
                                </a-divider>
                                 <StockLabel2 :stocks="reporttime['1']" :show="true" />
                            </div>
                            <div v-if="reporttime['2'] && reporttime['2'].length > 0">
                                <a-divider orientation="left">
                                半年报披露
                                </a-divider>
                                <StockLabel2 :stocks="reporttime['2']" :show="true" />  
                            </div>
                            <div v-if="reporttime['3'] && reporttime['3'].length > 0">
                                <a-divider orientation="left">
                                三季报披露
                                </a-divider>
                                <StockLabel2 :stocks="reporttime['3']" :show="true" />
                            </div>
                            <div v-if="reporttime['4'] && reporttime['4'].length > 0">
                                <a-divider orientation="left">
                                年报披露
                                </a-divider>
                                <StockLabel2 :stocks="reporttime['4']" :show="true" />
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <a-empty class="mt64" description="暂无相关信息" />
                    </div>
                </a-col>
            </a-row>
        </a-card>
    </div>
</template>

<script>
import moment from 'moment';
import { CancelToken, isCancel } from 'axios'
import StockLabel2 from 'components/StockLabel2'
import StockLabel from 'components/StockLabel'
import TagLabel from 'components/TagLabel'

export default {
    data() {
        return {
            marked,
            moment,
            visible: false,
            calendars: [],
            daliyData: [],
            currentTime: moment(),
            currentData: null,
            postData: null,
            currentPostList: null,
            reporttime: {},
            cancel: null,
            importantList: []
        }
    },
    props: {},
    components: {
        StockLabel,
        StockLabel2,
        TagLabel
    },
    computed: {},
    created() {},
    mounted() {
        this.fetchData();
    },
    methods: {
        fetchData() {
            this.$api.news.getImportantNews().then((result) => {
                this.importantList = result
            }).finally(() => {
            })

            this.$api.calendar.calendarList({
                type: 'all',
                date: this.currentTime.format('YYYYMMDD'),
            }).then((result) => {
                this.calendars = result.calendarData;
                this.daliyData = result.daliyData;
                this.postData = result.postData;
                this.currentData = this.getListData(this.currentTime)
                this.currentPostList = this.getPostData(this.currentTime, 'dailystory')
            }).catch((err) => {

            });
        },
        onDateChange(value) {
            this.currentTime = value;
            this.fetchData();
            if (this.cancel) {
                this.cancel();
            }
            this.$api.calendar.getReporttime({
                date: value.format('YYYYMMDD'),
            }, {
                cancelToken: new CancelToken(c => (this.cancel = c))
            }).then((result) => {
                this.reporttime = result
            }).catch((err) => {
                if (isCancel) {
                    // eslint-disable-next-line no-console
                    console.log(err.message)
                // eslint-disable-next-line no-empty
                } else {
                    
                }
            }).finally(() => {})
        },
        getListData(value) {
            let listData = [];
            
            if (this.calendars && Array.isArray(this.calendars)) {
                this.calendars.forEach(element => {
                    if (element.date === value.format('YYYYMMDD')) {
                        listData = element.calendarEvents || [];
                    }
                });
            }
            return listData;
        },
        getDaliyData(value) {
			let daliy = null;
			
			this.daliyData &&  this.daliyData.forEach(element => {
				if (element.date === value.format('YYYYMMDD')) {
					daliy = element;
				}
			});
			return daliy;
		},
        getPostData(value, type) {
            let post_list = []
            if (this.postData && Array.isArray(this.postData)) {
                this.postData.forEach(element => {
                    if (element.time === value.format('YYYYMMDD') && element.category === type) {
                        post_list.push(element)
                    }
                });
            }
            return post_list
        },
        getCalendar(value) {
            let obj = {
                mark: false,
                danger: false,
            }
            if (this.calendars && Array.isArray(this.calendars)) {
                this.calendars.forEach(element => {
                    if (element.date === value.format('YYYYMMDD')) {
                        obj = element || obj;
                    }
                });
            }

            return obj
        },
    }
}
</script>

<style scoped lang="less">
.b-calendar {
    // min-height: 420px;
}

.b-calendar-cell {
    width: 100%;
    height: 100%;
}
.events {
  list-style: none;
  margin: 0;
  padding: 0;
}
.events .ant-badge-status {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  text-overflow: ellipsis;
  font-size: 12px;
}

.smallcube {
    width: 10px;
    height: 10px;
    background: #f2f3f5;
    display: none;
    margin-top: 6px;
}

.show-red {
    display: inline-block;
    background: #f5222d;
}

.show-green {
    display: inline-block;
    background: #52c41a;
}

.show {
    display: inline-block;
}

.b-calendar-cell-content {
    display: flex;
	align-items: center;
    justify-content: center;
}

.xiu {
	display: block;
	height: 12px;
	width: 12px;
	line-height: 12px;
	color: #fff;
	font-size: 10px;
	text-align: center;
	background: #faad14;
	margin: 0 2px;
}
.shi {
	display: block;
	height: 12px;
	width: 12px;
	line-height: 12px;
	color: #fff;
	font-size: 10px;
	text-align: center;
	background: #0b928b;
	margin: 0 2px;
}

.b-calendar-notification{
    // max-height: 338px;
    // overflow-y: auto;
}
</style>