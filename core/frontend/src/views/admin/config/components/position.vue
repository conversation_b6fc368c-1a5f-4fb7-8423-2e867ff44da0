<template>
<div>
    <a-row>
        <a-col class="mb16" :xs="24" :sm="24" :md="24" :lg="24" :xl="8">
            <canvas id="position"></canvas>
        </a-col>
        <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="16">
            <div class="text-right">
                <a-button
                    type="primary"
                    class="mb16"
                    @click.native="showModal">
                    新建仓位
                </a-button>
            </div>
           <a-table
                :columns="columns"
                :rowKey="record => record.id"
                :dataSource="positions"
                :customRow="customRow"
                :pagination="false"
            >   
                <template slot="account" slot-scope="text, record">
                    <span v-if="record.account">{{record.account}}</span>
                </template>
                <template slot="code" slot-scope="text, record">
                    <span v-if="record.source">
                        <StockBar :source="record.source" :stock="record"/>
                    </span>
                    <span v-else-if="record.code == 'cash'">
                        现金
                    </span>
                    <span v-else-if="record.code == 'financ'">
                        两融
                    </span>
                    <span v-else>
                        {{record.code}}
                    </span>
                </template>
                <template slot="value" slot-scope="text, record">
                    <span v-if="record.source">
                        {{record.value | numFilter(4, 2, '')}}
                    </span>
                    <span v-else>
                    {{record.value | numFilter(4, 2, '')}}
                    </span>
                </template>
                <template slot="profit" slot-scope="text, record">
                    <span v-if="record.source">
                        <span v-html="replaceMethod(record.source.f2 - record.cost, (record.source.f2 - record.cost)/record.cost*100, 0, 2,'%')" /><a-divider type="vertical"></a-divider>
                        <span v-html="replaceMethod(record.source.f2 - record.cost, (record.source.f2 - record.cost)*record.amount/10000, 0, 2,'万')" />
                    </span>
                </template>
                <template slot="cost" slot-scope="text, record">
                    <span v-if="record.source">{{record.cost}} / {{record.source.f2}}</span>
                </template>
                <template slot="amount" slot-scope="text, record">
                    {{record.amount}}
                </template>
            </a-table>
        </a-col>
    </a-row>
        <a-modal title="仓位" v-model="visible" :footer="null">
            <a-form :form="form2">
                <a-form-item label="账户" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
                    <a-input
                        v-decorator="['account', { rules: [{ required: true, message: 'Please input your account!' }] }]"
                    />
                </a-form-item>
                <a-form-item label="仓位类型" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
                    <a-select
                        v-decorator="[
                        'type',
                        { rules: [{ required: true, message: 'Please select your type' }] },
                        ]"
                        placeholder="仓位类型"
                        defaultValue="stock"
                        @change="handleSelectChange"
                    >
                        <a-select-option value="stock">股票</a-select-option>
                        <a-select-option value="cash">现金</a-select-option>
                        <a-select-option value="financ">两融</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item v-if="toggle" label="股票" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
                    <a-select
                        v-decorator="[
                        'stock',
                        { rules: [{ required: true, message: 'Please input your stock!' }] },
                        ]"
                        show-search
                        :value="stock"
                        placeholder="搜索股票"
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="false"
                        :not-found-content="null"
                        @search="handleSearch"
                        @change="handleChange"
                    >
                        <a-select-option v-for="d in stockList" :key="d.value">
                        {{ d.label }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item v-if="!toggle" label="价值" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
                    <a-input
                        v-decorator="['value', { rules: [{ required: true, message: 'Please input your value!' }] }]"
                    />
                </a-form-item>
                <a-form-item v-if="toggle" label="数量" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
                    <a-input
                        v-decorator="['amount', { rules: [{ required: true, message: 'Please input your amount!' }] }]"
                    />
                </a-form-item>
                <a-form-item v-if="toggle" label="成本价" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
                    <a-input
                        v-decorator="['cost', { rules: [{ required: true, message: 'Please input your cost!' }] }]"
                    />
                </a-form-item>
                <a-form-item :wrapper-col="{ span: 12, offset: 5 }">
                <a-button class="mr16" type="danger" v-if="isUpdate" html-type="submit" @click="deleteAction">删除</a-button>
                <a-button type="primary" html-type="submit" @click="registerAction">{{isUpdate ? '更新' : '保存'}}</a-button>
                </a-form-item>
            </a-form>
        </a-modal>
</div>
</template>
<script>
import moment from 'moment';
import { mapActions } from 'vuex';
import StockBar from 'components/StockBar';
// import F2 from "@antv/f2/lib/index-all";

const columns = [
{
    title: '账户',
    dataIndex: 'account',
    scopedSlots: { customRender: 'account' },
    width: 80
},
{
    title: '仓位',
    dataIndex: 'code',
    scopedSlots: { customRender: 'code' },
    width: 200
},
{
    title: '市值(万元)',
    dataIndex: 'value',
    scopedSlots: { customRender: 'value' },
},
{
    title: '成本/现价(元)',
    dataIndex: 'cost',
    scopedSlots: { customRender: 'cost' },
},
{
    title: '持仓(股)',
    dataIndex: 'amount',
    scopedSlots: { customRender: 'amount' },
},
{
    title: '盈亏(%)',
    dataIndex: 'profit',
    scopedSlots: { customRender: 'profit' },
},
];

export default {
    data () {
        return {
            form2: this.$form.createForm(this, { name: 'coordinated' }),
            columns,
            positions: [],
            visible: false,
            isUpdate: false,
            toggle: true,
            stockList: [],
            selectedPosition: null,
            chartData: [],
            timer: null,
        };
    },
    components: {
        StockBar
    },
    props: {},
    computed: {},
    created() {},
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    },
    mounted() {
        this.fetchData();
        this.timer = setInterval(() => {
            this.fetchData();
        }, 1000 * 20);
    },
    methods: {
        ...mapActions(('stock'),['showStockDetail']),
        moment,
         customRow(record, index) {
            return {
                on: {
                    click: () => {
                        if (record.code == '总计') {
                            return
                        }

                        if (record.code == 'cash' || record.code == 'financ') {
                            this.toggle = false
                        } else {
                            this.toggle = true
                        }

                        this.isUpdate = true;
                        this.visible = true
                        this.selectedPosition = record;
                        this.handleSearch(record.code);
                        this.$nextTick(() => {
                            this.form2.setFieldsValue({
                                type: record.source ? 'stock' : 'cash',
                                account: record.account,
                                stock: record.code,
                                amount: record.amount,
                                value: record.value,
                                cost: record.cost,
                            })

                        })
                    },
                    mouseenter: () => {
                        // this.type === 'mouseenter' && this.$emit('stockSelect', record.code);
                    },
                },
            }
        },
        fetchData() {
            // this.loading = true;
            this.positions = []
            this.chartData = []
            this.$api.position.getPositions({
                pageIndex: 1,
                pageSize: 1000,
            }).then((result) => {
                this.positions = result;
                this.positions.forEach((item) => {
                    if (item.source) {
                        Object.assign(item, {
                            value: (item.source.f2 > 0 ? item.source.f2 : (item.source.f18 ? item.source.f18 : item.cost)) * item.amount,
                            name: item.source ? item.source.displayName : item.code,
                            const: 'const',
                        })
                        this.chartData.push(item)
                    } else {
                        // 现金不统计
                        // if (item.code == 'cash') {
                        //     this.chartData.push({
                        //         ...item,
                        //         const: 'const',
                        //         name: '现金',
                        //         a: '1'
                        //     })
                        // }
                    }
                })
                this.renderChart();
                
                let total = 0
                result.forEach(element => {
                    if (element.source) {
                        total = (element.source.f2 > 0 ? element.source.f2 : (element.source.f18 ? element.source.f18 : element.cost)) * element.amount + total
                    } else {
                        total = total + element.value
                    }
                });

                this.positions.push({
                    code: '总计',
                    value: total
                })
            })
        },
        renderChart() {
            const chart = new F2.Chart({
                id: 'position',
                pixelRatio: window.devicePixelRatio
            });
            chart.source(this.chartData, {
                percent: {
                    formatter: function formatter(val) {
                        return val * 100 + '%';
                    }   
                }
            });
            chart.legend({
                position: 'right',
                itemFormatter: function itemFormatter(val) {
                    return val;
                }
            });
            chart.tooltip(false);
            chart.coord('polar', {
                transposed: true,
                radius: 0.85
            });
            chart.axis(false);
            chart.interval()
            .position('const*value')
            .color('name', [ '#1890FF', '#13C2C2', '#2FC25B', '#FACC14', '#F04864', '#8543E0' ])
            .adjust('stack')
            .style({
                lineWidth: 1,
                stroke: '#fff',
                lineJoin: 'round',
                lineCap: 'round'
            })
            .animate({
                appear: {
                duration: 1200,
                easing: 'bounceOut'
                }
            });

            chart.render();
        },
        showModal() {
            this.visible = true;
            this.isUpdate = false;
            this.selectedPosition = null;
            this.toggle = true
            this.$nextTick(() => {
                this.form2.resetFields();
                this.form2.setFieldsValue({
                    type: 'stock'
                })
            })
        },
        hideModal() {
            this.visible = false;
        },
        handleSearch(inputValue) {
            this.$fetchCenter.onSearch('stock', {
                text: inputValue
            }, (result) => {
                this.stockList = result.rows.map((item) => {
                    return {
                        label: item.displayName,
                        value: item.code
                    }
                });
            })
        },
        handleChange(value) {
            // console.log(`selected ${value}`);
        },
        handleSelectChange(value) {
            if (value == 'stock') {
                this.toggle = true
            } else {
                this.toggle = false
            }
        },
        deleteAction() {
            this.$api.position.deletePosition(this.selectedPosition.id).then((result) => {
                this.form2.resetFields();
                this.$message.success('删除仓位成功')
                this.visible = false;
                this.fetchData()
            })
        },
        registerAction() {
            this.form2.validateFields((err, values) => {
                if (!err) {
                    if (this.isUpdate) {
                        this.$api.position.updatePosition(this.selectedPosition.id, {
                            code: values.type == 'stock' ? values.stock : values.type,
                            amount: values.amount,
                            cost: values.cost,
                            value: values.value,
                            account: values.account
                        }).then((result) => {
                            this.form2.resetFields();
                            this.$message.success('更新仓位成功')
                            this.visible = false;
                            this.fetchData()
                        })

                        return;
                    }

                    this.$api.position.createPosition({
                        code: values.type == 'stock' ? values.stock : values.type,
                        amount: values.amount,
                        cost: values.cost,
                        value: values.value,
                        account: values.account
                    }).then((result) => {
                        this.form2.resetFields();
                        this.$message.success('新建仓位成功')
                        this.visible = false;
                        this.fetchData();
                    })
                }
            });
        },
    },
};
</script>

<style scoped lang="less">
#position {
    width: 100%;
    height: 400px;
}
</style>