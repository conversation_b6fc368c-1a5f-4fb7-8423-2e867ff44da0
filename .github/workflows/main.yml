name: Docker Image CI

on:
  push:
    tags:
    - '*'
  workflow_dispatch:
    inputs:
      components:
        description: 'Components to build (comma-separated: backend,frontend,quant,crawl,jialin,deploy-cron,proxy-pool,all)'
        required: true
        default: 'all'
      version_suffix:
        description: 'Version suffix (optional)'
        required: false

jobs:
  # 检测变化的文件路径
  changes:
    runs-on: ubuntu-latest
    outputs:
      backend: ${{ steps.changes.outputs.backend }}
      frontend: ${{ steps.changes.outputs.frontend }}
      quant: ${{ steps.changes.outputs.quant }}
      crawl: ${{ steps.changes.outputs.crawl }}
      jialin: ${{ steps.changes.outputs.jialin }}
      deploy_cron: ${{ steps.changes.outputs.deploy_cron }}
      proxy_pool: ${{ steps.changes.outputs.proxy_pool }}
      aicrawl: ${{ steps.changes.outputs.aicrawl }}
      version: ${{ steps.version.outputs.version }}
      build_backend: ${{ steps.decide.outputs.build_backend }}
      build_frontend: ${{ steps.decide.outputs.build_frontend }}
      build_quant: ${{ steps.decide.outputs.build_quant }}
      build_crawl: ${{ steps.decide.outputs.build_crawl }}
      build_jialin: ${{ steps.decide.outputs.build_jialin }}
      build_deploy_cron: ${{ steps.decide.outputs.build_deploy_cron }}
      build_proxy_pool: ${{ steps.decide.outputs.build_proxy_pool }}
      build_aicrawl: ${{ steps.decide.outputs.build_aicrawl }}
    steps:
    - name: Check out code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0

    - name: Check for file changes
      uses: dorny/paths-filter@v2
      id: changes
      with:
        filters: |
          backend:
            - 'core/backend/**'
          frontend:
            - 'core/frontend/**'
          quant:
            - 'core/quant/**'
          crawl:
            - 'core/crawl/**'
          jialin:
            - 'core/jialin/**'
          deploy_cron:
            - 'plugin/gocron/**'
          proxy_pool:
            - 'plugin/proxy_pool/**'
          aicrawl:
            - 'core/aicrawl/**'

    - name: Extract version
      id: version
      run: |
        if [[ $GITHUB_REF == refs/tags/* ]]; then
          # 从标签提取版本号
          VERSION=${GITHUB_REF#refs/tags/}
          # 移除v前缀（如果存在）
          VERSION=${VERSION#v}
        else
          # 非标签推送，使用分支名和时间戳
          BRANCH=${GITHUB_REF#refs/heads/}
          TIMESTAMP=$(date +%Y%m%d-%H%M%S)
          VERSION="${BRANCH}-${TIMESTAMP}-${GITHUB_SHA::8}"
        fi

        # 添加用户指定的后缀
        if [[ -n "${{ github.event.inputs.version_suffix }}" ]]; then
          VERSION="${VERSION}-${{ github.event.inputs.version_suffix }}"
        fi

        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "Generated version: $VERSION"

    - name: Decide what to build
      id: decide
      run: |
        # 默认值
        BUILD_BACKEND="false"
        BUILD_FRONTEND="false"
        BUILD_QUANT="false"
        BUILD_CRAWL="false"
        BUILD_JIALIN="false"
        BUILD_DEPLOY_CRON="false"
        BUILD_PROXY_POOL="false"
        BUILD_AICRAWL="false"

        if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          # 手动触发，根据输入参数决定
          COMPONENTS="${{ github.event.inputs.components }}"
          if [[ "$COMPONENTS" == "all" ]] || [[ "$COMPONENTS" == *"backend"* ]]; then
            BUILD_BACKEND="true"
          fi
          if [[ "$COMPONENTS" == "all" ]] || [[ "$COMPONENTS" == *"frontend"* ]]; then
            BUILD_FRONTEND="true"
          fi
          if [[ "$COMPONENTS" == "all" ]] || [[ "$COMPONENTS" == *"quant"* ]]; then
            BUILD_QUANT="true"
          fi
          if [[ "$COMPONENTS" == "all" ]] || [[ "$COMPONENTS" == "crawl" ]]; then
            BUILD_CRAWL="true"
          fi
          if [[ "$COMPONENTS" == "all" ]] || [[ "$COMPONENTS" == *"jialin"* ]]; then
            BUILD_JIALIN="true"
          fi
          if [[ "$COMPONENTS" == "all" ]] || [[ "$COMPONENTS" == *"deploy-cron"* ]]; then
            BUILD_DEPLOY_CRON="true"
          fi
          if [[ "$COMPONENTS" == "all" ]] || [[ "$COMPONENTS" == *"proxy-pool"* ]]; then
            BUILD_PROXY_POOL="true"
          fi
          if [[ "$COMPONENTS" == "all" ]] || [[ "$COMPONENTS" == "aicrawl" ]]; then
            BUILD_AICRAWL="true"
          fi
        else
          # 标签触发，根据标签名称或文件变化决定
          TAG_NAME=${GITHUB_REF#refs/tags/}
          if [[ "$TAG_NAME" == *"backend"* ]] || [[ "${{ steps.changes.outputs.backend }}" == "true" ]]; then
            BUILD_BACKEND="true"
          fi
          if [[ "$TAG_NAME" == *"frontend"* ]] || [[ "${{ steps.changes.outputs.frontend }}" == "true" ]]; then
            BUILD_FRONTEND="true"
          fi
          if [[ "$TAG_NAME" == *"quant"* ]] || [[ "${{ steps.changes.outputs.quant }}" == "true" ]]; then
            BUILD_QUANT="true"
          fi
          # 精确匹配crawl标签，避免与aicrawl冲突
          if [[ "$TAG_NAME" == "crawl"* ]] || [[ "$TAG_NAME" == *"-crawl"* ]] || [[ "$TAG_NAME" == *"_crawl"* ]] || [[ "${{ steps.changes.outputs.crawl }}" == "true" ]]; then
            BUILD_CRAWL="true"
          fi
          if [[ "$TAG_NAME" == *"jialin"* ]] || [[ "${{ steps.changes.outputs.jialin }}" == "true" ]]; then
            BUILD_JIALIN="true"
          fi
          if [[ "$TAG_NAME" == *"deploy-cron"* ]] || [[ "${{ steps.changes.outputs.deploy_cron }}" == "true" ]]; then
            BUILD_DEPLOY_CRON="true"
          fi
          if [[ "$TAG_NAME" == *"proxy-pool"* ]] || [[ "${{ steps.changes.outputs.proxy_pool }}" == "true" ]]; then
            BUILD_PROXY_POOL="true"
          fi
          # 精确匹配aicrawl标签
          if [[ "$TAG_NAME" == "aicrawl"* ]] || [[ "$TAG_NAME" == *"-aicrawl"* ]] || [[ "$TAG_NAME" == *"_aicrawl"* ]] || [[ "${{ steps.changes.outputs.aicrawl }}" == "true" ]]; then
            BUILD_AICRAWL="true"
          fi

          # 如果是通用版本标签（如v1.0.0），构建所有组件
          if [[ "$TAG_NAME" =~ ^v?[0-9]+\.[0-9]+\.[0-9]+ ]] || [[ "$TAG_NAME" == *"all"* ]]; then
            BUILD_BACKEND="true"
            BUILD_FRONTEND="true"
            BUILD_QUANT="true"
            BUILD_CRAWL="true"
            BUILD_JIALIN="true"
            BUILD_DEPLOY_CRON="true"
            BUILD_PROXY_POOL="true"
            BUILD_AICRAWL="true"
          fi
        fi

        echo "build_backend=$BUILD_BACKEND" >> $GITHUB_OUTPUT
        echo "build_frontend=$BUILD_FRONTEND" >> $GITHUB_OUTPUT
        echo "build_quant=$BUILD_QUANT" >> $GITHUB_OUTPUT
        echo "build_crawl=$BUILD_CRAWL" >> $GITHUB_OUTPUT
        echo "build_jialin=$BUILD_JIALIN" >> $GITHUB_OUTPUT
        echo "build_deploy_cron=$BUILD_DEPLOY_CRON" >> $GITHUB_OUTPUT
        echo "build_proxy_pool=$BUILD_PROXY_POOL" >> $GITHUB_OUTPUT
        echo "build_aicrawl=$BUILD_AICRAWL" >> $GITHUB_OUTPUT

        echo "Build decisions:"
        echo "  Backend: $BUILD_BACKEND"
        echo "  Frontend: $BUILD_FRONTEND"
        echo "  Quant: $BUILD_QUANT"
        echo "  Crawl: $BUILD_CRAWL"
        echo "  Jialin: $BUILD_JIALIN"
        echo "  Deploy-Cron: $BUILD_DEPLOY_CRON"
        echo "  Proxy-Pool: $BUILD_PROXY_POOL"
        echo "  AI Crawl: $BUILD_AICRAWL"

  # 构建Backend
  build-backend:
    needs: changes
    if: needs.changes.outputs.build_backend == 'true'
    runs-on: ubuntu-latest
    steps:
    - name: Check out code
      uses: actions/checkout@v3

    - name: Install ENV
      run: |
        ./install.sh online

    - name: Login to Docker Registry
      run: |
        docker login --username=${{ secrets.DOCKER_USERNAME }} --password=${{ secrets.DOCKER_PASSWORD }} registry.cn-hangzhou.aliyuncs.com

    - name: Build and Push Backend Docker Image
      run: |
        cd core/backend
        VERSION="${{ needs.changes.outputs.version }}"

        # 构建镜像，同时打上版本标签和latest标签
        docker build . --file Dockerfile \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/backend:$VERSION \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/backend:latest

        # 推送镜像
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/backend:$VERSION
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/backend:latest

        echo "Built and pushed backend:$VERSION"

  # 构建Frontend
  build-frontend:
    needs: changes
    if: needs.changes.outputs.build_frontend == 'true'
    runs-on: ubuntu-latest
    steps:
    - name: Check out code
      uses: actions/checkout@v3

    - name: Install ENV
      run: |
        ./install.sh online

    - name: Login to Docker Registry
      run: |
        docker login --username=${{ secrets.DOCKER_USERNAME }} --password=${{ secrets.DOCKER_PASSWORD }} registry.cn-hangzhou.aliyuncs.com

    - name: Build and Push Frontend Docker Image
      run: |
        cd core/frontend
        VERSION="${{ needs.changes.outputs.version }}"

        docker build . --file Dockerfile \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/frontend:$VERSION \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/frontend:latest

        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/frontend:$VERSION
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/frontend:latest

        echo "Built and pushed frontend:$VERSION"

  # 构建Quant
  build-quant:
    needs: changes
    if: needs.changes.outputs.build_quant == 'true'
    runs-on: ubuntu-latest
    steps:
    - name: Check out code
      uses: actions/checkout@v3

    - name: Install ENV
      run: |
        ./install.sh online

    - name: Login to Docker Registry
      run: |
        docker login --username=${{ secrets.DOCKER_USERNAME }} --password=${{ secrets.DOCKER_PASSWORD }} registry.cn-hangzhou.aliyuncs.com

    - name: Build and Push Quant Docker Image
      run: |
        cd core/quant
        VERSION="${{ needs.changes.outputs.version }}"

        docker build . --file Dockerfile \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/quant:$VERSION \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/quant:latest

        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/quant:$VERSION
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/quant:latest

        echo "Built and pushed quant:$VERSION"

  # 构建Crawl
  build-crawl:
    needs: changes
    if: needs.changes.outputs.build_crawl == 'true'
    runs-on: ubuntu-latest
    steps:
    - name: Check out code
      uses: actions/checkout@v3

    - name: Install ENV
      run: |
        ./install.sh online

    - name: Login to Docker Registry
      run: |
        docker login --username=${{ secrets.DOCKER_USERNAME }} --password=${{ secrets.DOCKER_PASSWORD }} registry.cn-hangzhou.aliyuncs.com

    - name: Build and Push Crawl Docker Image
      run: |
        cd core/crawl
        VERSION="${{ needs.changes.outputs.version }}"

        docker build . --file Dockerfile \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/crawl:$VERSION \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/crawl:latest

        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/crawl:$VERSION
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/crawl:latest

        echo "Built and pushed crawl:$VERSION"

  # 构建Jialin
  build-jialin:
    needs: changes
    if: needs.changes.outputs.build_jialin == 'true'
    runs-on: ubuntu-latest
    steps:
    - name: Check out code
      uses: actions/checkout@v3

    - name: Install ENV
      run: |
        ./install.sh online

    - name: Login to Docker Registry
      run: |
        docker login --username=${{ secrets.DOCKER_USERNAME }} --password=${{ secrets.DOCKER_PASSWORD }} registry.cn-hangzhou.aliyuncs.com

    - name: Build and Push Jialin Docker Image
      run: |
        cd core/jialin
        VERSION="${{ needs.changes.outputs.version }}"

        docker build . --file Dockerfile \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/jialin:$VERSION \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/jialin:latest

        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/jialin:$VERSION
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/jialin:latest

        echo "Built and pushed jialin:$VERSION"

  # 构建Deploy-Cron
  build-deploy-cron:
    needs: changes
    if: needs.changes.outputs.build_deploy_cron == 'true'
    runs-on: ubuntu-latest
    steps:
    - name: Check out code
      uses: actions/checkout@v3

    - name: Install ENV
      run: |
        ./install.sh online

    - name: Login to Docker Registry
      run: |
        docker login --username=${{ secrets.DOCKER_USERNAME }} --password=${{ secrets.DOCKER_PASSWORD }} registry.cn-hangzhou.aliyuncs.com

    - name: Build and Push Deploy-Cron Docker Image
      run: |
        cd plugin/gocron
        VERSION="${{ needs.changes.outputs.version }}"

        # 构建镜像，同时打上版本标签和latest标签
        docker build . --file Dockerfile \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/deploy-cron:$VERSION \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/deploy-cron:latest \
          --platform linux/amd64

        # 推送镜像
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/deploy-cron:$VERSION
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/deploy-cron:latest

        echo "Built and pushed deploy-cron:$VERSION"

  # 构建Proxy Pool
  build-proxy-pool:
    needs: changes
    if: needs.changes.outputs.build_proxy_pool == 'true'
    runs-on: ubuntu-latest
    steps:
    - name: Check out code
      uses: actions/checkout@v3

    - name: Install ENV
      run: |
        ./install.sh online

    - name: Login to Docker Registry
      run: |
        docker login --username=${{ secrets.DOCKER_USERNAME }} --password=${{ secrets.DOCKER_PASSWORD }} registry.cn-hangzhou.aliyuncs.com

    - name: Build and Push Proxy Pool Docker Image
      run: |
        cd plugin/proxy_pool
        VERSION="${{ needs.changes.outputs.version }}"

        # 构建镜像，同时打上版本标签和latest标签
        docker build . --file Dockerfile \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/proxy-pool:$VERSION \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/proxy-pool:latest \
          --platform linux/amd64

        # 推送镜像
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/proxy-pool:$VERSION
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/proxy-pool:latest

        echo "Built and pushed proxy-pool:$VERSION"

  # 构建AI Crawl
  build-aicrawl:
    needs: changes
    if: needs.changes.outputs.build_aicrawl == 'true'
    runs-on: ubuntu-latest
    steps:
    - name: Check out code
      uses: actions/checkout@v3

    - name: Install ENV
      run: |
        ./install.sh online

    - name: Login to Docker Registry
      run: |
        docker login --username=${{ secrets.DOCKER_USERNAME }} --password=${{ secrets.DOCKER_PASSWORD }} registry.cn-hangzhou.aliyuncs.com

    - name: Build and Push AI Crawl Docker Image
      run: |
        cd core/aicrawl
        VERSION="${{ needs.changes.outputs.version }}"

        # 构建镜像，同时打上版本标签和latest标签
        docker build . --file Dockerfile \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/aicrawl:$VERSION \
          --tag registry.cn-hangzhou.aliyuncs.com/blackbear/aicrawl:latest

        # 推送镜像
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/aicrawl:$VERSION
        docker push registry.cn-hangzhou.aliyuncs.com/blackbear/aicrawl:latest

        echo "Built and pushed aicrawl:$VERSION"

  # 构建汇总
  build-summary:
    needs: [changes, build-backend, build-frontend, build-quant, build-crawl, build-jialin, build-deploy-cron, build-proxy-pool, build-aicrawl]
    if: always()
    runs-on: ubuntu-latest
    steps:
    - name: Build Summary
      run: |
        echo "=== Build Summary ==="
        echo "Version: ${{ needs.changes.outputs.version }}"
        echo "Components built:"

        if [[ "${{ needs.changes.outputs.build_backend }}" == "true" ]]; then
          if [[ "${{ needs.build-backend.result }}" == "success" ]]; then
            echo "✅ Backend: SUCCESS"
          else
            echo "❌ Backend: FAILED"
          fi
        else
          echo "⏭️ Backend: SKIPPED"
        fi

        if [[ "${{ needs.changes.outputs.build_frontend }}" == "true" ]]; then
          if [[ "${{ needs.build-frontend.result }}" == "success" ]]; then
            echo "✅ Frontend: SUCCESS"
          else
            echo "❌ Frontend: FAILED"
          fi
        else
          echo "⏭️ Frontend: SKIPPED"
        fi

        if [[ "${{ needs.changes.outputs.build_quant }}" == "true" ]]; then
          if [[ "${{ needs.build-quant.result }}" == "success" ]]; then
            echo "✅ Quant: SUCCESS"
          else
            echo "❌ Quant: FAILED"
          fi
        else
          echo "⏭️ Quant: SKIPPED"
        fi

        if [[ "${{ needs.changes.outputs.build_crawl }}" == "true" ]]; then
          if [[ "${{ needs.build-crawl.result }}" == "success" ]]; then
            echo "✅ Crawl: SUCCESS"
          else
            echo "❌ Crawl: FAILED"
          fi
        else
          echo "⏭️ Crawl: SKIPPED"
        fi

        if [[ "${{ needs.changes.outputs.build_jialin }}" == "true" ]]; then
          if [[ "${{ needs.build-jialin.result }}" == "success" ]]; then
            echo "✅ Jialin: SUCCESS"
          else
            echo "❌ Jialin: FAILED"
          fi
        else
          echo "⏭️ Jialin: SKIPPED"
        fi

        if [[ "${{ needs.changes.outputs.build_deploy_cron }}" == "true" ]]; then
          if [[ "${{ needs.build-deploy-cron.result }}" == "success" ]]; then
            echo "✅ Deploy-Cron: SUCCESS"
          else
            echo "❌ Deploy-Cron: FAILED"
          fi
        else
          echo "⏭️ Deploy-Cron: SKIPPED"
        fi

        if [[ "${{ needs.changes.outputs.build_proxy_pool }}" == "true" ]]; then
          if [[ "${{ needs.build-proxy-pool.result }}" == "success" ]]; then
            echo "✅ Proxy-Pool: SUCCESS"
          else
            echo "❌ Proxy-Pool: FAILED"
          fi
        else
          echo "⏭️ Proxy-Pool: SKIPPED"
        fi

        if [[ "${{ needs.changes.outputs.build_aicrawl }}" == "true" ]]; then
          if [[ "${{ needs.build-aicrawl.result }}" == "success" ]]; then
            echo "✅ AI Crawl: SUCCESS"
          else
            echo "❌ AI Crawl: FAILED"
          fi
        else
          echo "⏭️ AI Crawl: SKIPPED"
        fi

        echo "===================="


