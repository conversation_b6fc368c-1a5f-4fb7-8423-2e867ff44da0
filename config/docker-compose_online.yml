services:
  ##### 部署辅助程序 #####
  # deploy-db:
  #   platform: linux/amd64
  #   image: mysql:8.0.18
  #   container_name: deploy-db
  #   restart: always
  #   environment:
  #     MYSQL_USER: mizhdi
  #     MYSQL_PASSWORD: mzd12345
  #     MYSQL_DATABASE: blackbear
  #     MYSQL_ROOT_PASSWORD: mzd12345
  #   ports:
  #     - '3308:3306'
  #   logging:
  #     driver: 'json-file'
  #     options:
  #       max-size: '5g'
  #   volumes:
  #     - ./deploy/deploy-db/data:/var/lib/mysql
  #     # 挂载配置文件目录
  #     - ./deploy/deploy-db/conf.d:/etc/mysql/conf.d
  #     # - ./deploy/deploy-db/init.d:/docker-entrypoint-initdb.d
  #   command:
  #     --default-authentication-plugin=mysql_native_password
  #     --character-set-server=utf8mb4
  #     --collation-server=utf8mb4_general_ci
  #     --explicit_defaults_for_timestamp=true
  #     --lower_case_table_names=1
  #     --sql_mode=STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION
  #   networks:
  #     app-network:
  #       ipv4_address: ************00
  # deploy-redis:
  #   image: redis
  #   container_name: deploy-redis
  #   command: redis-server --appendonly yes --requirepass 12345
  #   restart: always
  #   ports:
  #     - "6380:6379"
  #   volumes:
  #     - ./deploy/deploy-redis:/data
  #   networks:
  #     app-network:
  #       ipv4_address: ************01
  deploy-cron:
    platform: linux/amd64
    # image: ouqg/gocron:latest
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/deploy-cron
    container_name: deploy-cron
    restart: always
    ports:
      - "5920:5920"
    env_file:
      - ./prod.env
      - ./common.env
    environment:
      # 自动配置选项
      AUTO_CONFIG: "true"
      WAIT_FOR_DB: "true"
      GOCRON_SKIP_INSTALL_CHECK: "true"
      DB_PREFIX: "cron_"
      # 应用配置
      APP_NAME: "Blackbear定时任务管理系统"
      LOG_LEVEL: "${BB_LOG_LEVEL:-INFO}"

      # 可选的额外配置
      ENABLE_TLS: "${BB_CRON_ENABLE_TLS:-false}"
      CONCURRENCY_QUEUE: "${BB_CRON_CONCURRENCY_QUEUE:-500}"
    volumes:
      # 配置文件持久化
      - ./deploy/deploy-cron/conf:/app/conf
    # depends_on:
    #   - deploy-db
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5920/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    networks:
      app-network:
        ipv4_address: ************02
    labels:
      - "com.blackbear.service=gocron"
      - "com.blackbear.description=定时任务管理系统"
  # Blackbear 多维度代理池服务 (生产环境)
  deploy-proxy:
    platform: linux/amd64
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/proxy-pool:latest
    container_name: deploy-proxy
    restart: always
    ports:
      - "5010:5010"
    env_file:
      - ./prod.env
      - ./common.env
    volumes:
      - ./deploy/deploy-proxy/logs:/app/logs
      - ./deploy/deploy-proxy/data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5010/count/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      app-network:
        ipv4_address: ************03
  # deploy-splash:
  #   platform: linux/amd64
  #   image: scrapinghub/splash:latest
  #   container_name: deploy-splash
  #   ports:
  #     - "5023:5023"
  #   restart: always
  #   env_file:
  #     - ./common.env
  #   networks:
  #     app-network:
  #       ipv4_address: ************14
  # deploy-minio:
  #   image: minio/minio
  #   container_name: deploy-minio
  #   ports:
  #     - 9000:9000 # api 端口
  #     - 9001:9001 # 控制台端口
  #   env_file:
  #     - ./prod.env
  #     - ./common.env
  #   environment:
  #     MINIO_ACCESS_KEY: admin    #管理后台用户名
  #     MINIO_SECRET_KEY: admin12345 #管理后台密码，最小8个字符
  #   volumes:
  #     - ./deploy/deploy-minio/data:/data               #映射当前目录下的data目录至容器内/data目录
  #     - ./deploy/deploy-minio/config:/root/.minio/     #映射配置目录
  #   command: server --console-address ':9001' /data  #指定容器中的目录 /data
  #   privileged: true
  #   restart: always
  #   networks:
  #     app-network:
  #       ipv4_address: ************10
  # deploy-aktools:
  #   build: ./deploy/aktools
  #   # image: registry.cn-shanghai.aliyuncs.com/akfamily/aktools
  #   restart: always
  #   environment:
  #     TZ: Asia/Shanghai
  #   ports:
  #     - "8888:8080"
  ###### 后台主程序 #####
  backend:
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/backend
    container_name: backend
    restart: always
    ports:
      - "7001:7001"
    env_file:
      - ./prod.env
      - ./common.env
    # depends_on:
    #   - deploy-db
    networks:
      app-network:
        ipv4_address: ************05
  ###### 前台程序 #####
  frontend:
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/frontend
    container_name: frontend
    restart: always
    ports:
      - "8080:80"
    env_file:
      - ./prod.env
      - ./common.env
    depends_on:
      - backend
    networks:
      app-network:
        ipv4_address: ************06
  ###### 爬虫 Master 节点 #####
  crawl:
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/crawl
    container_name: crawl
    restart: always
    ports:
      - "6800:6800"  # Scrapyd 端口
      - "5001:5001"  # API 管理端口
    command: ["master"]
    environment:
      # 分布式爬虫配置 - Master 节点
      CRAWLER_ROLE: "master"
      CRAWLER_MODE: "smart"
      CRAWLER_MACHINE_ID: "online-master-001"
      # 调度配置（生产环境优化值）
      CRAWLER_FREQUENT_MINUTES: "30"
      CRAWLER_PERIODIC_MINUTES: "360"
      CRAWLER_MAINTENANCE_MINUTES: "120"
      CRAWLER_REFRESH_MINUTES: "720"
      CRAWLER_CLEANUP_MINUTES: "480"
      # 智能调度配置
      CRAWLER_SMART_REALTIME_MINUTES: "30"
      CRAWLER_SMART_MINUTE_MINUTES: "60"
      CRAWLER_SMART_DETAIL_MINUTES: "240"
      # Redis 数据管理配置（基于去重分析优化）
      CRAWLER_MAX_QUEUE_SIZE: "18000"
      CRAWLER_CLEANUP_THRESHOLD: "30000"
      CRAWLER_KEEP_RECENT_COUNT: "15000"
      # 代理配置 (新增)
      BB_PROXY_URL: http://deploy-proxy:5010
      PROXY_QUALITY_API: http://deploy-proxy:5010
    env_file:
      - ./prod.env
      - ./common.env
    depends_on:
      - deploy-proxy
    networks:
      app-network:
        ipv4_address: ************07
  # gerapy:
  #   container_name: blackbear-gerapy
  #   image: germey/gerapy:latest
  #   ports:
  #     - "8000:8000"
  #   depends_on:
  #     - crawl
  #   restart: always
  ###### 博客程序 #####
  jialin:
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/jialin
    container_name: jialin
    restart: always
    ports:
      - "3000:3000"
    env_file:
      - ./prod.env
      - ./common.env
    depends_on:
      - backend
    networks:
      app-network:
        ipv4_address: ************08
  ###### 量化脚本 #####
  quant:
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/quant
    container_name: quant
    restart: always
    ports:
      - "5921:5921"
    env_file:
      - ./prod.env
      - ./common.env
    # depends_on:
    #   - deploy-db
    networks:
      app-network:
        ipv4_address: ************09
  
  ###### AI爬虫服务 #####
  aicrawl:
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/aicrawl
    container_name: aicrawl
    restart: always
    ports:
      - "5030:5030"  # AI爬虫API端口
    env_file:
      - ./prod.env
      - ./common.env
    networks:
      app-network:
        ipv4_address: ************11
networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: ************/24
          gateway: ************