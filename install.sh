#!/bin/bash
#
# Blackbear项目配置部署启动脚本
# 功能：环境变量配置、Docker Compose配置生成、目录权限设置
# 支持环境：local, test, online
#

set -euo pipefail  # 严格错误处理

# ==================== 全局配置 ====================
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly CONFIG_DIR="${SCRIPT_DIR}/config"
readonly CORE_DIR="${SCRIPT_DIR}/core"
readonly DEPLOY_DIR="${SCRIPT_DIR}/deploy"

# 颜色输出配置
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# ==================== 工具函数 ====================

# 日志输出函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

# 显示帮助信息
show_help() {
    cat << EOF
Blackbear项目配置部署脚本

使用方法:
    $0 <环境名称> [选项]

环境名称:
    local   - 本地开发环境
    test    - 测试环境
    online  - 生产环境
    m2      - 线上服务本地补充环境（爬虫等特殊服务）

选项:
    -h, --help     显示此帮助信息
    -v, --verbose  详细输出模式
    --dry-run      预览模式，不执行实际操作

示例:
    $0 local                # 配置本地环境
    $0 test --verbose       # 配置测试环境（详细输出）
    $0 online --dry-run     # 预览生产环境配置
EOF
}

# 验证环境参数
validate_environment() {
    local env="$1"
    local valid_envs=("local" "test" "online" "m2")

    for valid_env in "${valid_envs[@]}"; do
        if [[ "$env" == "$valid_env" ]]; then
            return 0
        fi
    done

    log_error "无效的环境名称: $env"
    log_error "支持的环境: ${valid_envs[*]}"
    return 1
}

# 检查必要文件
check_required_files() {
    local env="$1"
    local required_files=(
        "${CONFIG_DIR}/${env}.sh"
        "${CONFIG_DIR}/docker-compose_${env}.yml"
        "${SCRIPT_DIR}/tmp.env"
    )

    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "必需文件不存在: $file"
            return 1
        fi
    done

    log_success "所有必需文件检查通过"
    return 0
}

# 创建必要目录
create_directories() {
    local dirs=(
        "${DEPLOY_DIR}/deploy-cron/conf"
        "${DEPLOY_DIR}/deploy-cron/logs"
        "${CORE_DIR}/frontend"
        "${CORE_DIR}/jialin"
        "${CORE_DIR}/quant"
    )

    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done

    # 设置cron目录权限
    # 在Linux上尝试设置容器用户权限，在macOS上使用宽松权限
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux环境：尝试设置容器内app用户权限 (UID:GID = 100:65533)
        if chown -R 100:65533 "${DEPLOY_DIR}/deploy-cron/conf" 2>/dev/null; then
            log_info "deploy-cron/conf 目录所有权设置成功"
        else
            log_info "设置 deploy-cron/conf 目录权限为777（容器兼容）"
            chmod 777 "${DEPLOY_DIR}/deploy-cron/conf" 2>/dev/null
        fi

        if chown -R 100:65533 "${DEPLOY_DIR}/deploy-cron/logs" 2>/dev/null; then
            log_info "deploy-cron/logs 目录所有权设置成功"
        else
            log_info "设置 deploy-cron/logs 目录权限为777（容器兼容）"
            chmod 777 "${DEPLOY_DIR}/deploy-cron/logs" 2>/dev/null
        fi
    else
        # macOS/其他环境：直接使用宽松权限确保容器访问
        log_info "设置 deploy-cron 目录权限（容器兼容）"
        chmod 755 "${DEPLOY_DIR}/deploy-cron/conf" 2>/dev/null || chmod 777 "${DEPLOY_DIR}/deploy-cron/conf" 2>/dev/null
        chmod 755 "${DEPLOY_DIR}/deploy-cron/logs" 2>/dev/null || chmod 777 "${DEPLOY_DIR}/deploy-cron/logs" 2>/dev/null
    fi

    log_success "目录创建和权限设置完成"
}

# ==================== 主要功能函数 ====================

# 显示横幅
show_banner() {
    echo
    echo -e "${BLUE}|------------------------------------------------------|${NC}"
    echo -e "${BLUE}|                  Blackbear配置部署                     |${NC}"
    echo -e "${BLUE}|------------------------------------------------------|${NC}"
    echo
}

# 加载环境配置
load_environment_config() {
    local env="$1"
    local config_file="${CONFIG_DIR}/${env}.sh"

    log_info "加载环境配置: $env"

    # 检查配置文件权限
    if [[ ! -r "$config_file" ]]; then
        log_error "配置文件不可读: $config_file"
        return 1
    fi

    # 加载配置文件
    # shellcheck source=/dev/null
    source "$config_file"

    log_success "环境配置加载完成: $config_file"
}

# 生成环境变量文件
generate_env_files() {
    local env="$1"

    log_info "生成环境变量文件..."

    # 生成 prod.env (BB_开头的环境变量)
    if ! env | grep '^BB_' > "${SCRIPT_DIR}/prod.env"; then
        log_warning "未找到BB_开头的环境变量"
        touch "${SCRIPT_DIR}/prod.env"
    fi

    # 生成 common.env (通用环境变量)
    if ! envsubst < "${SCRIPT_DIR}/tmp.env" > "${SCRIPT_DIR}/common.env"; then
        log_error "生成 common.env 失败"
        return 1
    fi

    # 生成前端环境变量文件
    if ! grep '^VUE_APP' "${SCRIPT_DIR}/tmp.env" | envsubst > "${CORE_DIR}/frontend/.env.production"; then
        log_warning "生成前端环境变量文件失败"
    fi

    # 生成Jialin环境变量文件
    if ! grep '^NUXT_PUBLIC' "${SCRIPT_DIR}/tmp.env" | envsubst > "${CORE_DIR}/jialin/.env.production"; then
        log_warning "生成Jialin环境变量文件失败"
    fi

    # 生成量化脚本配置文件
    if ! envsubst < "${CONFIG_DIR}/${env}.sh" > "${CORE_DIR}/quant/online.sh"; then
        log_warning "生成量化脚本配置文件失败"
    fi

    # 生成aicrawl配置文件
    if ! envsubst < "${CONFIG_DIR}/${env}.sh" > "${CORE_DIR}/aicrawl/online.sh"; then
        log_warning "生成aicrawl配置文件失败"
    fi

    log_success "环境变量文件生成完成"
}

# 生成 gocron 配置文件
generate_gocron_config() {
    local env="$1"
    local config_file="${DEPLOY_DIR}/deploy-cron/conf/app.ini"

    log_info "生成 gocron 配置文件..."

    # 检查必要的环境变量
    local required_vars=(
        "BB_CRON_MYSQL_HOST"
        "BB_CRON_MYSQL_USER"
        "BB_CRON_MYSQL_PASSWD"
        "BB_CRON_MYSQL_DBNAME"
    )

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "缺少必要的环境变量: $var"
            return 1
        fi
    done

    # 生成 app.ini 配置文件 (gocron 1.5 格式)
    cat > "$config_file" << EOF
# gocron 配置文件
# 由 install.sh 自动生成于 $(date)

[default]
# 数据库配置
db.engine = mysql
db.host = ${BB_CRON_MYSQL_HOST}
db.port = ${BB_CRON_MYSQL_PORT:-3306}
db.user = ${BB_CRON_MYSQL_USER}
db.password = ${BB_CRON_MYSQL_PASSWD}
db.database = ${BB_CRON_MYSQL_DBNAME}
db.prefix = cron_
db.charset = utf8
db.max.idle.conns = 30
db.max.open.conns = 100

# 应用配置
allow_ips =
app.name = Blackbear定时任务管理系统
api.key =
api.secret =
api.sign.enable = true
enable_tls = false
concurrency.queue = 500
auth_secret = $(openssl rand -hex 32)
ca_file =
cert_file =
key_file =

# Web服务配置
web.addr = 0.0.0.0
web.port = 5920

# 邮件配置
mail.host =
mail.port = 587
mail.user =
mail.password =
mail.pool_size = 10

# Slack配置
slack.url =
slack.channel =

# 日志配置
log.path = /app/log
log.level = info

# 其他配置
enable_tls = false
EOF

    # 设置配置文件权限
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux环境：尝试设置容器用户权限
        chmod 644 "$config_file" 2>/dev/null
        if chown 100:65533 "$config_file" 2>/dev/null; then
            log_info "gocron配置文件权限设置成功"
        else
            log_info "使用默认权限（容器兼容）"
            chmod 666 "$config_file" 2>/dev/null
        fi
    else
        # macOS/其他环境：使用兼容权限
        chmod 644 "$config_file" 2>/dev/null || chmod 666 "$config_file" 2>/dev/null
    fi

    log_success "gocron 配置文件生成完成: $config_file"
}

# 生成Docker Compose配置
generate_docker_compose() {
    local env="$1"
    local source_file="${CONFIG_DIR}/docker-compose_${env}.yml"
    local target_file="${SCRIPT_DIR}/docker-compose.yml"

    log_info "生成Docker Compose配置..."

    if ! cp "$source_file" "$target_file"; then
        log_error "复制Docker Compose配置失败"
        return 1
    fi

    log_success "Docker Compose配置生成完成: $target_file"
}

# 验证生成的文件
validate_generated_files() {
    local files=(
        "${SCRIPT_DIR}/prod.env"
        "${SCRIPT_DIR}/common.env"
        "${SCRIPT_DIR}/docker-compose.yml"
        "${CORE_DIR}/frontend/.env.production"
        "${CORE_DIR}/jialin/.env.production"
        "${CORE_DIR}/quant/online.sh"
        "${DEPLOY_DIR}/deploy-cron/conf/app.ini"
    )

    log_info "验证生成的文件..."

    for file in "${files[@]}"; do
        if [[ -f "$file" ]]; then
            log_success "✓ $file"
        else
            log_warning "✗ $file (文件不存在)"
        fi
    done
}

# ==================== 主函数 ====================

main() {
    local env=""
    local verbose=false
    local dry_run=false

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -z "$env" ]]; then
                    env="$1"
                else
                    log_error "多余的参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # 检查环境参数
    if [[ -z "$env" ]]; then
        log_error "缺少环境参数"
        show_help
        exit 1
    fi

    # 显示横幅
    show_banner

    # 验证环境
    if ! validate_environment "$env"; then
        exit 1
    fi

    # 检查必要文件
    if ! check_required_files "$env"; then
        exit 1
    fi

    # 预览模式
    if [[ "$dry_run" == true ]]; then
        log_info "预览模式 - 将要执行的操作:"
        log_info "1. 加载环境配置: $env"
        log_info "2. 创建必要目录"
        log_info "3. 生成环境变量文件"
        log_info "4. 生成Docker Compose配置"
        log_info "5. 生成 gocron 配置文件"
        log_info "6. 验证生成的文件"
        exit 0
    fi

    # 执行配置流程
    log_info "开始配置 $env 环境..."

    # 1. 加载环境配置
    if ! load_environment_config "$env"; then
        log_error "环境配置加载失败"
        exit 1
    fi

    # 2. 创建必要目录
    if ! create_directories; then
        log_error "目录创建失败"
        exit 1
    fi

    # 3. 生成环境变量文件
    if ! generate_env_files "$env"; then
        log_error "环境变量文件生成失败"
        exit 1
    fi

    # 4. 生成Docker Compose配置
    if ! generate_docker_compose "$env"; then
        log_error "Docker Compose配置生成失败"
        exit 1
    fi

    # 5. 生成 gocron 配置文件
    if ! generate_gocron_config "$env"; then
        log_error "gocron 配置文件生成失败"
        exit 1
    fi

    # 6. 验证生成的文件
    if [[ "$verbose" == true ]]; then
        validate_generated_files
    fi

    # 完成
    echo
    log_success "✅ Blackbear $env 环境配置完成 ✅"
    echo
    log_info "下一步操作:"
    log_info "  启动服务: docker-compose up -d"
    log_info "  查看日志: docker-compose logs -f"
    log_info "  停止服务: docker-compose down"
    echo
}

# 执行主函数
main "$@"